<template>
	<view class="assistant-container">
		<u-navbar title="" :is-back="true" :background="background">
			<view class="slot-wrap">
				<image class="logo" src="/static/images/logo.png" mode="aspectFit"></image>
				<text class="title">官方助手</text>
			</view>
		</u-navbar>

		<!-- 消息列表 -->
		<scroll-view class="message-list" scroll-y="true">
			<!-- 每日签到卡片 -->
			<view class="message-item">
				<view class="time-stamp">00:01</view>
				<view class="card-container daily-checkin">
					<view class="card-content">
						<view class="card-left">
							<view class="card-title">每日签到</view>
							<view class="card-button" @click="goCheckin">去购买 ></view>
						</view>
						<view class="card-right">
							<image class="card-image" src="/static/images/checkin-icon.png" mode="aspectFit"></image>
						</view>
					</view>
					<view class="card-description">致全体用户的安全提醒</view>
				</view>
			</view>

			<!-- 生日好礼卡片 -->
			<view class="message-item">
				<view class="time-stamp">00:01</view>
				<view class="card-container birthday-gift">
					<view class="card-content">
						<view class="card-left">
							<view class="card-title">生日好礼</view>
							<view class="card-subtitle">BIRTHDAY</view>
							<view class="card-button" @click="goBirthday">去查看 ></view>
						</view>
						<view class="card-right">
							<image class="card-image" src="/static/images/birthday-cake.png" mode="aspectFit"></image>
						</view>
					</view>
					<view class="card-description">致全体用户的安全提醒</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			background: {
				background: '#ffffff'
			}
		}
	},

	onLoad() {
		// 页面加载时的初始化
	},

	methods: {
		// 去签到
		goCheckin() {
			console.log('去签到');
			uni.showToast({
				title: '跳转到签到页面',
				icon: 'none'
			});
			// 实际项目中跳转到签到页面
			// uni.navigateTo({
			//     url: '/pages/checkin/index'
			// });
		},

		// 去查看生日好礼
		goBirthday() {
			console.log('去查看生日好礼');
			uni.showToast({
				title: '跳转到生日好礼页面',
				icon: 'none'
			});
			// 实际项目中跳转到生日好礼页面
			// uni.navigateTo({
			//     url: '/pages/birthday/index'
			// });
		}
	}
}
</script>

<style lang="scss" scoped>
.assistant-container {
	height: 100vh;
	background-color: #f8f8f8;
	display: flex;
	flex-direction: column;
}

.slot-wrap {
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	gap: 16rpx;
}

.logo {
	width: 48rpx;
	height: 48rpx;
}

.title {
	font-weight: 600;
	font-size: 36rpx;
	color: #323233;
}

.message-list {
	flex: 1;
	padding: 32rpx;
	background-color: #f8f8f8;
}

.message-item {
	margin-bottom: 40rpx;
}

.time-stamp {
	text-align: center;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 24rpx;
}

.card-container {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 32rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	position: relative;
	overflow: hidden;
}

.daily-checkin {
	background: linear-gradient(135deg, #E8F5E8 0%, #D4F1D4 100%);
}

.birthday-gift {
	background: linear-gradient(135deg, #F0E6FF 0%, #E6D7FF 100%);
}

.card-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;
}

.card-left {
	flex: 1;
}

.card-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 8rpx;
}

.card-subtitle {
	font-size: 24rpx;
	color: #007AFF;
	font-weight: 500;
	margin-bottom: 16rpx;
}

.card-button {
	background: linear-gradient(135deg, #4CAF50 0%, #45A049 100%);
	color: #ffffff;
	padding: 16rpx 32rpx;
	border-radius: 32rpx;
	font-size: 28rpx;
	font-weight: 500;
	display: inline-block;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
}

.birthday-gift .card-button {
	background: linear-gradient(135deg, #9C27B0 0%, #8E24AA 100%);
	box-shadow: 0 4rpx 12rpx rgba(156, 39, 176, 0.3);
}

.card-right {
	width: 120rpx;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.card-image {
	width: 100rpx;
	height: 100rpx;
}

.card-description {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.4;
}

/* 装饰元素 */
.daily-checkin::before {
	content: '';
	position: absolute;
	top: -20rpx;
	right: -20rpx;
	width: 80rpx;
	height: 80rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
}

.birthday-gift::before {
	content: '';
	position: absolute;
	top: -30rpx;
	right: -30rpx;
	width: 100rpx;
	height: 100rpx;
	background: rgba(255, 255, 255, 0.15);
	border-radius: 50%;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.card-title {
		font-size: 36rpx;
	}

	.card-button {
		font-size: 26rpx;
		padding: 14rpx 28rpx;
	}

	.card-image {
		width: 80rpx;
		height: 80rpx;
	}
}
</style>
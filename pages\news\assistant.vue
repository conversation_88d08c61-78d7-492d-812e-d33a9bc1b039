<template>
	<view class="assistant-container">
		<u-navbar title="" :is-back="true" :background="background">
			<view class="slot-wrap">
				<image class="logo" src="https://photo.zastatic.com/images/common-cms/it/20250530/1748585884843_125375_t.png" mode="aspectFit"></image>
				<text class="title">官方助手</text>
			</view>
		</u-navbar>

		<!-- 消息列表 -->
		<scroll-view class="message-list" scroll-y="true">
			<view
				class="message-item"
				v-for="item in messageList"
				:key="item.id">
				<view class="time-stamp">{{ item.time }}</view>
				<view class="message-content" @click="handleButtonClick(item.buttonAction)">
					<image class="message-image" :src="item.image" mode="aspectFill"></image>
					<view class="message-description">{{ item.description }}</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			background: {
				background: '#ffffff'
			},
			messageList: [
				{
					id: 1,
					time: '00:01',
					type: 'daily-checkin',
					image: 'https://photo.zastatic.com/images/common-cms/it/20250530/1748586008215_550536_t.png',
					description: '致全体用户的安全提醒',
					buttonAction: 'goCheckin'
				},
				{
					id: 2,
					time: '00:01',
					type: 'birthday-gift',
					image: 'https://photo.zastatic.com/images/common-cms/it/20250530/1748586030663_93009_t.png',
					description: '致全体用户的安全提醒',
					buttonAction: 'goBirthday'
				}
			]
		}
	},

	onLoad() {
		// 页面加载时的初始化
	},

	methods: {
		// 通用按钮点击处理
		handleButtonClick(action) {
			if (this[action] && typeof this[action] === 'function') {
				this[action]();
			} else {
				console.warn(`方法 ${action} 不存在`);
			}
		},

		// 去签到
		goCheckin() {
			console.log('去签到');
			uni.showToast({
				title: '跳转到签到页面',
				icon: 'none'
			});
			// 实际项目中跳转到签到页面
			// uni.navigateTo({
			//     url: '/pages/checkin/index'
			// });
		},

		// 去查看生日好礼
		goBirthday() {
			console.log('去查看生日好礼');
			uni.showToast({
				title: '跳转到生日好礼页面',
				icon: 'none'
			});
			// 实际项目中跳转到生日好礼页面
			// uni.navigateTo({
			//     url: '/pages/birthday/index'
			// });
		}
	}
}
</script>

<style lang="scss" scoped>
.assistant-container {
	height: 100vh;
	background-color: #f8f8f8;
	display: flex;
	flex-direction: column;
}

.slot-wrap {
	display: flex;
	align-items: center;
    justify-content: center;
	padding: 0 32rpx;
	gap: 16rpx;
    margin-left: 132rpx;
}

.logo {
	width: 48rpx;
	height: 48rpx;
}

.title {
	font-weight: 600;
	font-size: 36rpx;
	color: #323233;
}

.message-list {
	flex: 1;
	padding: 32rpx;
	background-color: #f8f8f8;
}

.message-item {
	margin-bottom: 40rpx;
}

.time-stamp {
	text-align: center;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 24rpx;
}

.message-content {
	background: #ffffff;
	border-radius: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	cursor: pointer;
	transition: transform 0.2s ease;
}

.message-content:active {
	transform: scale(0.98);
}

.message-image {
	width: 100%;
	height: 320rpx;
	display: block;
}

.message-description {
	padding: 24rpx 32rpx;
	font-size: 28rpx;
	color: #333333;
	line-height: 1.5;
	text-align: left;
	background: #ffffff;
}
</style>
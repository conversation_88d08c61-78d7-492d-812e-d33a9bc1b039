<template>
	<view style="padding-bottom:350rpx">
		<u-navbar title="TA的动态" :is-back="true" :border-bottom="false" :background="background"
			title-color="#111224"></u-navbar>
		<view class="bgjb"></view>

		<view class="userbox" v-if="JSON.stringify(formData) != '{}'" @click="goDetail()">
			<image :src="formData.userImg?formData.userImg:'../../../static/logo.png'" mode="aspectFill"
				style="width: 160rpx;height: 160rpx;border-radius: 30rpx;"></image>
			<view class="margin-left-sm">
				<view class="flex align-center">
					<view class="yhm">{{formData.realName}}</view>
					<image src="../../../static/images/my/rzicon.png" class="rencion"
						style="width: 40rpx;height: 40rpx;"></image>
				</view>
				<view class="flex align-center margin-tb-xs">
					<view class="sexicon" v-if="formData.sex==1">
						<u-icon name="man" color="#FFFFFF"></u-icon>
						{{formData.age}}岁
					</view>
					<view class="sexicons" v-if="formData.sex==2">
						<u-icon name="woman" color="#FFFFFF"></u-icon>
						{{formData.age}}岁
					</view>
					<view class="labe">{{formData.education}}</view>
					<view class="labe" v-if="formData.marriageStatus==1">未婚</view>
					<view class="labe" v-if="formData.marriageStatus==2">离异</view>
					<view class="labe">{{formData.userHeight}}CM</view>
				</view>
				<view style="font-size: 26rpx;color: #999999;">{{formData.locationCity}}{{formData.locationCounty}}
				</view>
			</view>
		</view>


		<view class="list-box-item" :style="{marginTop:JSON.stringify(formData) == '{}'?'20rpx':''}">
			<Head v-if="list.length>0" :list="list" :userId="userId" @click="clickItem"></Head>
			<!-- 暂无数据 -->
			<view v-if="list.length == 0" class="emptybox">
				<view class="flex justify-center" style="margin-top: 200rpx;">
					<image src="../../../static/images/empty.png" style="width: 300rpx;height: 341rpx;" mode="widthFix">
					</image>
				</view>
				<view class="flex justify-center"
					style="font-size: 32rpx;color: #999999;margin-top: 20rpx;font-weight: 700;">
					暂无数据
				</view>
			</view>
		</view>


		<view class=" taber" v-if="JSON.stringify(formData) != '{}'">
			<view class="gunbi" @click="callPhone">
				<!-- <u-icon name="../static/hqx.png" color="#FFFFFF" size="45"></u-icon> -->
				<image src="../static/hqx.png" style="width:40rpx;height: 40rpx;"></image>

				<text class="margin-left-xs" v-if="formData.isGetPhone!=1">获取联系方式</text>
				<text class="margin-left-xs" v-else>查看联系方式</text>
			</view>
			<view class="xihuan" @click="like()" v-if="formData.isLike==0">
				<u-icon name="/static/images/index/xihuan.png" color="#FFFFFF" size="50"></u-icon>
				<text class="margin-left-xs">喜欢</text>
			</view>
			<view class="xihuans" @click="like()" v-else>
				<u-icon name="/static/images/index/noxihuan.png" color="#FFFFFF" size="50"></u-icon>
				<text class="margin-left-xs">不喜欢</text>
			</view>
		</view>

		<u-popup v-model="show" mode="center" border-radius="32" :closeable="true" close-icon="close-circle"
			close-icon-size="48">
			<view class="pupobox">
				<view class="bg">
					<image src="../../../static/images/index/beijing.png" style="width: 100%;max-height: 508rpx;">
					</image>
				</view>
				<view class="pupocot">
					<view class="pupotit">获取联系方式</view>
					<!-- <view class="tit">
						确认获取系统将立即短信 通知对方，并且您的头像信息将 显示在对方的消息栏中
					</view> -->
					<view class="patit">本次获取需支付{{money}}元</view>
					<view class="vrn" @click="openpay">确认获取</view>
				</view>
			</view>
		</u-popup>

		<u-popup v-model="payshow" mode="bottom" border-radius="32" :closeable="true" close-icon="close-circle"
			close-icon-size="48">
			<view class="pupoboxs">
				<view class="bg">
					<image src="../../../static/images/index/beijing.png" style="width: 100%;max-height: 508rpx;">
					</image>
				</view>
				<view class="pupocot">
					<view class="pupotit">支付方式</view>
					<view class="flex align-center justify-between" style="height: 100upx;padding: 30upx;"
						v-for="(item,index) in openLists" :key='index'>
						<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;"></image>
						<view style="font-size: 30upx;margin-left:0upx;width: 70%;">{{item.text}}
						</view>
						<radio-group name="openWay" style="margin-left: 20upx;" @tap='selectWay(item)'>
							<label class="tui-radio">
								<radio color="linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);"
									:checked="openWay === item.id ? true : false" />
							</label>
						</radio-group>
					</view>

					<view class="btn margin-top" @click="pay">立即支付</view>
				</view>
			</view>
		</u-popup>

		<hninfo ref="hnPopup"></hninfo>

		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	import Head from '@/components/Head.vue'
	import empty from '@/components/empty.vue'
	import hninfo from "@/components/hnInfo.vue";
	export default {
		components: {
			empty,
			Head,
			hninfo
		},
		data() {
			return {
				background: {
					backgroundImage: 'linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)'
				},
				list: [],
				byUserId: '',
				userId: '',
				formData: {},
				page: 1,
				limit: 10,
				count: 0,
				money: '',
				payshow: false,
				show: false,
				openLists: '',
				openWay: 1,


				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				isVip: '',
				statusStauts: ''
			}
		},
		onLoad(option) {
			uni.showLoading({
				title: '加载中...'
			})
			// #ifdef APP-PLUS
			this.openLists = [{
				image: '../../../static/images/my/zhifubao.png',
				text: '支付宝',
				id: 1
			}, {
				image: '../../../static/images/my/icon_weixin.png',
				text: '微信',
				id: 2
			}, {
				image: '../../../static/images/my/cz.png',
				text: '零钱',
				id: 3
			}];
			this.openWay = 1;
			// #endif

			// #ifdef MP-WEIXIN
			this.openLists = [{
				image: '../../../static/images/my/icon_weixin.png',
				text: '微信',
				id: 2
			}, {
				image: '../../../static/images/my/cz.png',
				text: '零钱',
				id: 3
			}];
			this.openWay = 2;
			// #endif

			// #ifdef H5
			let ua = navigator.userAgent.toLowerCase();
			if (ua.indexOf('micromessenger') !== -1) {
				//公众号是否自动登录  333
				this.$Request.get('/app/common/type/333').then(res => {
					if (res.data && res.data.value && res.data.value == '是') {
						this.openLists = [{
							image: '../../../static/images/my/zhifubao.png',
							text: '支付宝',
							id: 1
						}, {
							image: '../../../static/images/my/icon_weixin.png',
							text: '微信',
							id: 2
						}, {
							image: '../../../static/images/my/cz.png',
							text: '零钱',
							id: 3
						}];
						this.openWay = 2;
					} else {
						this.openLists = [{
							image: '../../../static/images/my/zhifubao.png',
							text: '支付宝',
							id: 1
						}, {
							image: '../../../static/images/my/cz.png',
							text: '零钱',
							id: 3
						}];
						this.openWay = 1;
					}
				})
			} else {
				this.openLists = [{
					image: '../../../static/images/my/zhifubao.png',
					text: '支付宝',
					id: 1
				}, {
					image: '../../../static/images/my/cz.png',
					text: '零钱',
					id: 3
				}];
				this.openWay = 1;
			}
			// #endif

			this.userId = uni.getStorageSync('userId')
			if (option.byUserId) {
				this.byUserId = option.byUserId
			}
		},
		onShow() {
			if (this.byUserId) {
				this.getUserInfo()
				this.getdongtai()
				this.getInfo()
				this.getRenZheng()
				this.taskData()
			}
			this.$Request.getT('/app/common/type/324').then(res => {
				if (res.code == 0) {
					if (res.data && res.data.value) {
						this.money = res.data.value
					}
				}
			});
		},
		methods: {
			goDetail() {
				uni.navigateTo({
					url: '/package/pages/game/detail?byUserId=' + this.formData.userId
				})
			},
			// 获取余额
			taskData() {
				this.$Request.get("/app/userMoney/selectMyMoney").then(res => {
					if (res.code == 0 && res.data) {
						this.mymoney = res.data.money
					}
				});
			},
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm1') {
					uni.navigateTo({
						url: '/my/vip/index'
					})
				} else if (that.meIndex == 'm4') {
					uni.navigateTo({
						url: '/my/setting/userinfo'
					})
				} else if (that.meIndex == 'm6') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				} else if (that.meIndex == 'm7') {
					uni.navigateTo({
						url: '/my/wallet/Txmoney'
					})
				}

			},
			//取消
			meHandleClose() {
				let that = this
				if (that.meIndex == 'm1') {

				}
			},
			getRenZheng() {
				this.$Request.get("/app/userCertification/getMyUserCertification", {
					authType: 1
				}).then(res => {
					if (res.code == 0 && res.data) {
						// 0审核中 1通过 2拒绝 
						if (res.data.status == 0) {
							this.statusStauts = 1 //审核中
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 1) {
							this.statusStauts = 2 //已实名
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 2) {
							this.statusStauts = 3 //已拒绝
							uni.setStorageSync('statusStauts', this.statusStauts)
						}
					} else {
						this.statusStauts = 0 //未实名
						uni.setStorageSync('statusStauts', this.statusStauts)
					}
				});
			},
			callPhone() {
				if (uni.getStorageSync('token')) {
					this.$Request.get("/app/user/selectUserById").then(res => {
						if (res.code == 0 && res.data) {
							if (res.data.phone) {
								if (this.statusStauts == 1) { //待审核
									this.meShowModel = true
									this.meTitle = '提示'
									this.meContent = '实名认证审核中,请通过后再来查看'
									this.meIndex = 'm6'
									this.meConfirmText = '去查看'
									this.meShowCancel = true
								} else if (this.statusStauts == 2) { //已通过
									if (this.isVip == 1) { //是否是会员(0不是 1是)
										this.$refs.hnPopup.open(this.formData)
									} else {
										this.meShowModel = true
										this.meTitle = '提示'
										this.meContent = '开通会员获取红娘联系方式'
										this.meIndex = 'm1'
										this.meConfirmText = '确认'
										this.meShowCancel = true

									}
								} else if (this.statusStauts == 3) { //已通过
									this.meShowModel = true
									this.meTitle = '提示'
									this.meContent = '实名审核被拒绝'
									this.meIndex = 'm6'
									this.meConfirmText = '去认证'
									this.meShowCancel = true
								} else if (this.statusStauts == -1) { //已通过
									this.meShowModel = true
									this.meTitle = '提示'
									this.meContent = '未实名认证，请先去实名认证'
									this.meIndex = 'm6'
									this.meConfirmText = '去认证'
									this.meShowCancel = true
								}

							} else {
								this.meShowModel = true
								this.meTitle = '提示'
								this.meContent = '请先去完善个人信息'
								this.meIndex = 'm9'
								this.meConfirmText = '去完善'
								this.meShowCancel = true
							}
						}
					})


				} else {
					this.noLogin()
				}
			},
			getdongtai() {
				this.$Request.getT("/app/trends/getTrendsList?byUserId=" + this.userId + '&page=' + this.page +
					'&limit=' + this.limit + '&userId=' + this.byUserId).then(res => {
					if (res.code == 0) {
						let list = res.data.records
						list.forEach(d => {
							if (d.trendsImage) {
								d.trendsImage = d.trendsImage.split(',')
							}
						});
						if (this.page == 1) {
							this.list = list
						} else {
							this.list = [...this.list, ...list]
						}
						this.count = res.data.total;
					}
					uni.hideLoading();
					uni.stopPullDownRefresh()
				});
			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						this.isVip = res.data.isVip
					}
				});
			},
			like() {
				let that = this
				let userId = uni.getStorageSync('userId')
				if (that.statusStauts == 1) { //待审核
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '实名认证审核中，请通过后去完善资料'
					that.meIndex = 'm6'
					that.meConfirmText = '去查看'
					that.meShowCancel = true
				} else if (that.statusStauts == 2) { //已通过
					that.$Request.get("/app/userData/getUserDataInfo?userId=" + userId).then(res => {
						if (res.code == 0) {
							if (res.data) {
								that.$Request.postT("/app/scFollow/saveScFollow?byUserId=" + that
										.byUserId + '&type=2')
									.then(res => {
										if (res.code == 0) {
											uni.showToast({
												title: res.msg,
												icon: 'none',
												duration: 3000
											})
											that.getInfo()
										}
									});
							} else {
								that.firstlogin = false
								that.meShowModel = true
								that.meTitle = '提示'
								that.meContent = '完善个人资料可以被更多人看到哦'
								that.meConfirmText = '去完善'
								that.meIndex = 'm4'
								that.meShowCancel = true
							}

						}

					});

				} else if (that.statusStauts == 3) { //已拒绝
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '实名审核被拒绝，请先去认证'
					that.meIndex = 'm6'
					that.meConfirmText = '去认证'
					that.meShowCancel = true
				} else if (that.statusStauts == -1) { //已通过
					// uni.navigateTo({
					// 	url: '/my/renzheng/index'
					// })
					that.firstlogin = false
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '未实名认证，请先去实名认证'
					that.meConfirmText = '去认证'
					that.meIndex = 'm6'
					that.meShowCancel = true
				}
				// that.$Request.get("/app/userData/getUserDataInfo?userId=" + userId).then(res => {
				// 	if (res.code == 0) {
				// 		if (res.data) {
				// 			that.$Request.postT("/app/scFollow/saveScFollow?byUserId=" + that.byUserId + '&type=2').then(res => {
				// 				if (res.code == 0) {
				// 					uni.showToast({
				// 						title: res.msg,
				// 						icon: 'none',
				// 						duration:3000
				// 					})
				// 					that.getInfo()

				// 				}

				// 			});
				// 		} else {
				// 			that.firstlogin = false
				// 			that.meShowModel = true
				// 			that.meTitle = '提示'
				// 			that.meContent = '完善个人资料可以被更多人看到哦'
				// 			that.meConfirmText = '去完善'
				// 			that.meIndex = 'm4'
				// 			that.meShowCancel = true
				// 		}

				// 	}

				// });


			},
			getInfo() {
				this.$Request.get("/app/userData/getUserDataInfo?byUserId=" + this.userId + '&userId=' + this.byUserId)
					.then(res => {
						if (res.code == 0 && res.data) {
							this.formData = res.data
							if (this.formData && this.formData.userImg) {
								let userImg = this.formData.userImg.split(',')
								this.formData.userImg = userImg[0]
							}
							this.$forceUpdate()
						}

					});
			},
			clickItem: function(options) {
				let token = this.$queue.getData('token');
				if (!token) {
					this.goLoginInfo();
					return;
				}
				console.log(options)
				if (options.index == 0) {
					uni.navigateTo({
						url: '/package/pages/detail/listDetails?trendsId=' + this.list[options.id].trendsId
					});
				} else if (options.index == 1) {
					let token = this.$queue.getData('token');
					if (token) {
						//去关注页面
						uni.navigateTo({
							url: '/my/gird/guanzhuDetail?userId=' + this.list[options.id].userId
						});

					} else {
						this.goLoginInfo();
					}
				} else if (options.index == 2) {
					// this.getMessageList2();
				} else if (options.index == 3) {
					this.saveLove(this.list[options.id].trendsId);
				} else if (options.index == 9) { //点击关注
					this.guanzhu1(this.list[options.id].userId);
				} else if (options.index == 10) {
					console.log(options);
					let data = options.id
					this.$refs.hnPopup.open(data)
				}

			},
			guanzhu1(userId) {
				this.$Request.postT('/app/scFollow/saveScFollow?byUserId=' + userId + '&type=1').then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.page = 1;
						this.getdongtai();
					}
				});
			},
			saveLove(trendsId) {
				// if (this.renzheng != 2) {
				// 	this.meShowModel = true
				// 	this.meTitle = '温馨提示'
				// 	this.meContent = '您还未认证陪玩官,请先认证'
				// 	this.meIndex = 'm1'
				// 	return;
				// }
				// let userId = this.$queue.getData('userId');
				let data = {
					trendsId: trendsId,
					// userId: userId
				}
				this.$Request.postT('/app/trendsLike/saveTrendsLike', data).then(res => {
					if (res.code == 0) {
						this.page = 1;
						this.getdongtai();
					} else {
						this.$queue.showToast(res.msg);
					}
				});
			},
			openpay() {
				this.show = false
				this.payshow = true
			},

			selectWay: function(item) {
				this.openWay = item.id;
			},
			pay() {
				uni.showLoading({
					title: '支付中...'
				});
				this.payshow = false
				if (this.openWay == 1) { //支付宝支付
					// #ifdef H5
					let data = {
						classify: 5,
						dataId: this.formData.dataId,
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(res => {
						if (res.code == 0) {
							const div = document.createElement('div')
							div.innerHTML = res.data //此处form就是后台返回接收到的数据
							document.body.appendChild(div)
							document.forms[0].submit()
							uni.hideLoading()
						} else {
							uni.showToast({
								icon: 'none',
								title: '支付失败!'
							});
						}
					});
					// #endif

					// #ifdef APP
					let data = {
						classify: 4,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
						console.log(ret)
						that.isCheckPay(ret.code, 'alipay', ret.data);
					});
					// #endif
				} else if (this.openWay == 2) { //微信支付
					// #ifdef MP-WEIXIN
					let data = {
						classify: 2,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
						uni.hideLoading()
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: ret.data.timestamp,
							nonceStr: ret.data.noncestr,
							package: ret.data.package,
							signType: ret.data.signType,
							paySign: ret.data.sign,
							success: function(suc) {
								console.log('success:' + JSON.stringify(suc));
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})

							},
							fail: function(err) {
								console.log('fail:' + JSON.stringify(err));
								uni.showToast({
									title: '支付失败',
									icon: 'none'
								})
							}
						});
					});
					// #endif
					// #ifdef H5
					let data = {
						classify: 3,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(res => {
						if (res.code == 0) {
							uni.hideLoading()
							this.callPay(res.data);
						} else {
							uni.showToast({
								icon: 'none',
								title: '支付失败!'
							});
						}
					});
					// #endif
					// #ifdef APP
					let data = {
						classify: 1,
						dataId: this.formData.dataId
					}
					this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
						console.log(ret, 'retretretretretret')
						this.isCheckPay(ret.code, 'wxpay', JSON.stringify(ret.data));
					});
					// #endif
				} else if (this.openWay == 3) {
					if (this.mymoney >= this.money) {
						let data = {
							classify: 0,
							dataId: this.formData.dataId
						}
						this.$Request.postT("/app/userGetPhoneRecord/buyPhone", data).then(ret => {
							uni.hideLoading()
							if (ret.code == 0) {
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								this.getUserInfo()
								this.getInfo()
							} else {
								uni.showToast({
									icon: 'none',
									title: ret.msg
								});
							}
						});
					} else {
						uni.hideLoading()
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '零钱余额不足，请选去充值'
						this.meConfirmText = '去充值'
						this.meIndex = 'm7'
						this.meShowCancel = true
					}
				}
			},
			callPay: function(response) {
				console.log(response)
				if (typeof WeixinJSBridge === "undefined") {

					if (document.addEventListener) {
						document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);
					} else if (document.attachEvent) {
						document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));
						document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));
					}
				} else {
					console.log(1)
					this.onBridgeReady(response);
				}
			},
			onBridgeReady: function(response) {
				let that = this;
				if (!response.package) {
					return;
				}
				console.log(response, '++++++++')
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
						"appId": response.appid, //公众号名称，由商户传入
						"timeStamp": response.timestamp, //时间戳，自1970年以来的秒数
						"nonceStr": response.noncestr, //随机串
						"package": response.package,
						"signType": response.signType, //微信签名方式：
						"paySign": response.sign //微信签名
					},
					function(res) {
						console.log(res, '/*-/*-/*-')
						if (res.err_msg === "get_brand_wcpay_request:ok") {
							// 使用以上方式判断前端返回,微信团队郑重提示：
							//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
							uni.showLoading({
								title: '支付成功'
							});
							setTimeout(function() {
								uni.hideLoading();
							}, 1000);
						} else {
							uni.hideLoading();
						}
						WeixinJSBridge.log(response.err_msg);
					}
				);
			},
			isCheckPay(status, name, order) {
				if (status == 0) {
					this.setPayment(name, order);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: '支付信息有误',
						icon: 'none'
					});
				}
			},
			setPayment(name, order) {
				console.log('*-*-*')
				uni.requestPayment({
					provider: name,
					orderInfo: order, //微信、支付宝订单数据
					success: function(res) {
						console.log(res)
						uni.hideLoading();
						uni.showLoading({
							title: '支付成功'
						});
					},
					fail: function(err) {
						console.log(err)
						uni.hideLoading();
					},
					complete() {
						uni.hideLoading();
					}
				});
			},
		},
		onReachBottom: function() {
			if (this.list.length == this.count) {
				uni.showToast({
					title: '已经到底了',
					icon: 'none'
				})
			} else {
				this.page = this.page + 1;
				this.getdongtai();
			}
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getdongtai();
		}
	}
</script>

<style lang="less">
	page {
		background: #F2F6FC;
	}

	.rencion {
		width: 40rpx !important;
		height: 40rpx !important;
	}

	.bgjb {
		width: 100%;
		height: 531rpx;
		background-image: url('../../../static/images/bgImg.png');
		background-size: 100% 100%;
		position: fixed;
		top: 80rpx;
		left: 0;
		right: 0;
		z-index: 0;

	}

	.userbox {
		background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		padding: 30rpx;
		display: flex;
		align-items: center;
		position: fixed;
		top: 78rpx;
		left: 0;
		right: 0;
		z-index: 999;

		.yhm {
			font-size: 42rpx;
			font-family: PingFang SC;
			font-weight: bold;

			margin-right: 20rpx;
		}

		.sexicon {
			background: #38CAFF;
			border-radius: 10rpx;
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			padding: 4rpx 10rpx;
			margin-right: 10rpx;
		}

		.labe {
			background: #FFFFFF;
			border-radius: 10rpx;
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			padding: 4rpx 10rpx;
			margin-right: 15rpx;
		}
	}

	.list-box-item {
		/* #ifdef H5 */
		margin-top: 250rpx;
		/* #endif */
		/* #ifndef H5 */
		margin-top: 140rpx;
		/* #endif */
		position: relative;
		z-index: 99;


		// height: 100vh;
		.emptybox {
			background: #FFFFFF;
			border-radius: 24rpx;
			margin: 0 30rpx;
			padding: 30rpx;
			height: 50vh;
		}
	}

	.gunbi {
		width: 333rpx;
		height: 100rpx;
		background: #686BFE;
		box-shadow: 0rpx 10rpx 20rpx 0rpx #E4E4E4;
		border-radius: 55rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #FFFFFF;
	}

	.xihuan {
		width: 333rpx;
		height: 100rpx;
		background: linear-gradient(0deg, #FF6F9C 0%, #FFA8C7 100%);
		box-shadow: 0rpx 10rpx 20rpx 0rpx #FFDBE7;
		border-radius: 55rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #FFFFFF;
	}

	.xihuans {
		width: 333rpx;
		height: 100rpx;
		background: linear-gradient(0deg, #cacad1 0%, #dedee2 100%);
		box-shadow: 0rpx 10rpx 20rpx 0rpx #dedee2;
		border-radius: 55rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #FFFFFF;
	}

	.taber {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 60rpx 30rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		z-index: 999;
	}

	.pupobox {
		width: 596rpx;
		// height: 508rpx;
		background: #FFFFFF;
		border-radius: 32rpx;
		position: relative;
		padding: 40rpx 30rpx 30rpx;

		.bg {
			height: 225rpx;
			// background: linear-gradient(90deg, #D8E3FF 0%, #F3DAF7 100%);
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			z-index: 0;
		}

		.pupocot {
			width: 100%;
			position: relative;
			z-index: 99;

			.pupotit {
				text-align: center;
				font-size: 38rpx;
				font-family: PingFang SC;
				font-weight: 800;
				color: #1E1E1E;
			}

			.tit {
				width: 446rpx;
				margin: 25rpx auto;
				text-align: center;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
			}

			.patit {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				;
				text-align: center;
				margin-bottom: 40rpx;
				margin-top: 30rpx;
			}

			.vrn {
				width: 494rpx;
				height: 92rpx;
				background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				;
				border-radius: 46rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				margin: 10rpx auto;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

	}

	.pupoboxs {
		width: 100%;
		// height: 508rpx;
		background: #FFFFFF;
		border-radius: 32rpx;
		position: relative;
		padding: 40rpx 30rpx 30rpx;

		.bg {
			height: 225rpx;
			// background: linear-gradient(90deg, #D8E3FF 0%, #F3DAF7 100%);
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			z-index: 0;
		}

		.pupocot {
			width: 100%;
			position: relative;
			z-index: 99;

			.pupotit {
				text-align: center;
				font-size: 38rpx;
				font-family: PingFang SC;
				font-weight: 800;
				color: #1E1E1E;
			}

			.btn {
				width: 100%;
				height: 80rpx;
				background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				;
				border-radius: 46rpx;
				font-size: 28rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #FFFFFF;
				margin: 10rpx auto;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.sexicons {
		background: #edbef3;
		border-radius: 10rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		padding: 4rpx 10rpx;
		margin-right: 10rpx;
	}
</style>
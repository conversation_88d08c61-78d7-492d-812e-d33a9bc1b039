<template>
	<view class="chat-list-container">
		<!-- 导航栏 -->
		<view class="nav-bar">
			<text class="nav-title">消息</text>
		</view>

		<!-- 聊天列表 -->
		<scroll-view
			class="chat-list"
			scroll-y="true"
			@scrolltolower="loadMore">

			<view
				class="chat-item"
				v-for="item in chatList"
				:key="item.objectId"
				@click="openChat(item)">

				<!-- 用户头像 -->
				<view class="avatar-container">
					<image
						class="avatar"
						:src="item.avatar || defaultAvatar"
						mode="aspectFill">
					</image>
					<!-- 未读消息数量 -->
					<view class="unread-badge" v-if="item.unreadCount > 0">
						<text class="unread-text">{{ item.unreadCount > 99 ? '99+' : item.unreadCount }}</text>
					</view>
				</view>

				<!-- 聊天信息 -->
				<view class="chat-info">
					<view class="chat-header">
						<text class="username">{{ item.nickname }}</text>
						<text class="chat-time">{{ formatTime(item.sendTime) }}</text>
					</view>

					<view class="chat-content">
						<text class="last-message">{{ item.messageContent }}</text>
						<!-- AI可聊天标识 -->
						<view class="ai-tag" v-if="!item.locked">
							<text class="ai-text">AI可聊天</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多提示 -->
			<view class="load-more" v-if="chatList.length > 0">
				<view class="load-more-loading" v-if="loading">
					<text class="load-more-text">加载中...</text>
				</view>
				<view class="load-more-nomore" v-else-if="!hasMore">
					<text class="load-more-text">没有更多数据了</text>
				</view>
				<view class="load-more-default" v-else>
					<text class="load-more-text">上拉加载更多</text>
				</view>
			</view>
		</scroll-view>

		<!-- 空状态 -->
		<view class="empty-state" v-if="chatList.length === 0 && !loading">
			<image class="empty-icon" src="/static/images/empty-chat.png" mode="aspectFit"></image>
			<text class="empty-text">暂无聊天记录</text>
		</view>

		<!-- 加载状态 -->
		<view class="loading-state" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			chatList: [],
			loading: false,
			page: 1,
			pageSize: 10,
			hasMore: true,
			totalPages: 0,
			total: 0,
			defaultAvatar: '/static/images/default-avatar.png'
		}
	},

	onLoad() {
		this.loadChatList();
	},

	methods: {
		// 加载聊天列表
		async loadChatList() {
			if (this.loading || !this.hasMore) return;

			this.loading = true;

			try {
				// 模拟数据，实际应该调用API
				const mockResponse = this.getMockData();

				if (this.page === 1) {
					this.chatList = mockResponse.records;
				} else {
					this.chatList.push(...mockResponse.records);
				}

				// 更新分页信息
				this.totalPages = mockResponse.pages;
				this.total = mockResponse.total;
				this.hasMore = this.page < this.totalPages;
				this.page++;

			} catch (error) {
				console.error('加载聊天列表失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 加载更多
		loadMore() {
            debugger
			this.loadChatList();
		},

		// 打开聊天页面
		openChat(item) {
			// 清除未读数量
			item.unreadCount = 0;

			// 跳转到聊天页面
			uni.navigateTo({
				url: `/pages/news/news?userId=${item.objectId}&username=${item.nickname}`
			});
		},

		// 格式化时间
		formatTime(timeString) {
			if (!timeString) return '';

			const now = new Date();
			const messageTime = new Date(timeString);
			const diffTime = now.getTime() - messageTime.getTime();
			const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

			if (diffDays === 0) {
				// 今天，显示时间
				return messageTime.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit',
					hour12: false
				});
			} else if (diffDays === 1) {
				// 昨天
				return '昨天';
			} else if (diffDays < 7) {
				// 一周内，显示星期
				const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
				return weekdays[messageTime.getDay()];
			} else {
				// 超过一周，显示月日
				return messageTime.toLocaleDateString('zh-CN', {
					month: '2-digit',
					day: '2-digit'
				});
			}
		},

		// 获取模拟数据
		getMockData() {
			// 完整的用户数据
			const allUsers = [{
				"objectId": 1801458324,
				"avatar": "https://static.jiebao.zhenai.com/202305193380-29b3ae4d-d900-4515-b334b9-84cd8459b00e.png?imageView2/0/w/500/h/500",
				"nickname": "品味人生",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "对方给你发来了消息",
				"unreadCount": 1,
				"locked": true,
				"sendTime": "2025-05-06 20:37:11",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1854538387,
				"avatar": "https://static.jiebao.zhenai.com/20250310-7090-41160e1ef5-839d-4be0-bc9c-ddb25f3e60cf.png?imageView2/0/w/500/h/500",
				"nickname": "发呆",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "您好，认识一下吗",
				"unreadCount": 0,
				"locked": false,
				"sendTime": "2025-05-04 14:40:38",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 82679756,
				"avatar": "https://static.jiebao.zhenai.com/202211181149-bc5f2bf8-9339-4e15-bb5f-eda217ea32e48f.png?imageView2/0/w/500/h/500",
				"nickname": "终极爱",
				"advantage": "",
				"match": false,
				"messageType": 1,
				"messageContent": "晚上好",
				"unreadCount": 0,
				"locked": false,
				"sendTime": "2025-05-03 22:16:08",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1487031786,
				"avatar": "https://static.jiebao.zhenai.com/20241017-2854-6a326311-0e92-465132-8fbc-f595207094da.png?imageView2/0/w/500/h/500",
				"nickname": "小白",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "对方给你发来了消息",
				"unreadCount": 1,
				"locked": true,
				"sendTime": "2025-05-03 22:02:58",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1520501615,
				"avatar": "https://static.jiebao.zhenai.com/20240712495-f4a722dd-d5ed-436e30-ab54-e02e9a005757.png?imageView2/0/w/500/h/500",
				"nickname": "紫伊",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "对方给你发来了消息",
				"unreadCount": 1,
				"locked": true,
				"sendTime": "2025-04-08 13:07:33",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1801987705,
				"avatar": "https://static.jiebao.zhenai.com/20241230-2403-167ccb65-4e4b-4854-ac1d-79402d18e3c0a1.png?imageView2/0/w/500/h/500",
				"nickname": "小白兔",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "对方给你发来了消息",
				"unreadCount": 1,
				"locked": true,
				"sendTime": "2025-04-06 21:51:48",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1594902169,
				"avatar": "https://static.jiebao.zhenai.com/2024090549802-abe84a05-492e-4cc9-a958-02e9291a281d.jpg?imageView2/0/w/500/h/500",
				"nickname": "冰淇淋",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "在不",
				"unreadCount": 0,
				"locked": false,
				"sendTime": "2025-04-03 08:06:19",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1396399201,
				"avatar": "https://static.jiebao.zhenai.com/20250328-8972-2a95ee2059-3af1-4677-ad56-f1382bb3e43a.png?imageView2/0/w/500/h/500",
				"nickname": "薇薇安",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "对方给你发来了消息",
				"unreadCount": 1,
				"locked": true,
				"sendTime": "2025-03-24 18:17:40",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 94651974,
				"avatar": "https://static.jiebao.zhenai.com/20231224579812-47145486-70e3-407d-a045-d30655c68d41.png?imageView2/0/w/500/h/500",
				"nickname": "新悦",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "对方给你发来了消息",
				"unreadCount": 1,
				"locked": true,
				"sendTime": "2025-03-23 23:19:56",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1854322342,
				"avatar": "https://static.jiebao.zhenai.com/202405261390-456d675a-0768-422c-a0ba-27779943d35ade.png?imageView2/0/w/500/h/500",
				"nickname": "瑶瑶",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "可以聊聊了解一下吗",
				"unreadCount": 1,
				"locked": false,
				"sendTime": "2025-03-23 12:31:01",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1234567890,
				"avatar": "https://static.jiebao.zhenai.com/202305193380-29b3ae4d-d900-4515-b334b9-84cd8459b00e.png?imageView2/0/w/500/h/500",
				"nickname": "阳光少女",
				"advantage": "",
				"match": false,
				"messageType": 1,
				"messageContent": "今天天气真好呢",
				"unreadCount": 2,
				"locked": false,
				"sendTime": "2025-03-20 15:30:00",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1234567891,
				"avatar": "https://static.jiebao.zhenai.com/20250310-7090-41160e1ef5-839d-4be0-bc9c-ddb25f3e60cf.png?imageView2/0/w/500/h/500",
				"nickname": "夜猫子",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "晚安，做个好梦",
				"unreadCount": 0,
				"locked": true,
				"sendTime": "2025-03-19 23:45:00",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1234567892,
				"avatar": "https://static.jiebao.zhenai.com/202211181149-bc5f2bf8-9339-4e15-bb5f-eda217ea32e48f.png?imageView2/0/w/500/h/500",
				"nickname": "咖啡时光",
				"advantage": "",
				"match": false,
				"messageType": 1,
				"messageContent": "想请你喝咖啡",
				"unreadCount": 1,
				"locked": false,
				"sendTime": "2025-03-18 14:20:00",
				"chatVipIdentifyUrl": null
			}, {
				"objectId": 1234567893,
				"avatar": "https://static.jiebao.zhenai.com/20241017-2854-6a326311-0e92-465132-8fbc-f595207094da.png?imageView2/0/w/500/h/500",
				"nickname": "书香墨韵",
				"advantage": "",
				"match": false,
				"messageType": 4,
				"messageContent": "推荐一本好书给你",
				"unreadCount": 0,
				"locked": false,
				"sendTime": "2025-03-17 10:15:00",
				"chatVipIdentifyUrl": null
			}];

			// 根据当前页码计算数据
			const startIndex = (this.page - 1) * this.pageSize;
			const endIndex = startIndex + this.pageSize;
			const pageData = allUsers.slice(startIndex, endIndex);

			// 模拟分页响应结构
			return {
				current: this.page,
				pages: Math.ceil(allUsers.length / this.pageSize),
				size: this.pageSize,
				total: allUsers.length,
				records: pageData
			};
		}
	}
}
</script>

<style lang="scss" scoped>
.chat-list-container {
	height: 100vh;
	background-color: #f8f8f8;
	display: flex;
	flex-direction: column;
}

.nav-bar {
	height: 88rpx;
	background-color: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 1rpx solid #e5e5e5;
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
}

.nav-title {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

.chat-list {
	flex: 1;
	margin-top: 88rpx;
	background-color: #ffffff;
}

.chat-item {
	display: flex;
	align-items: center;
	padding: 24rpx 32rpx;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.2s;

	&:active {
		background-color: #f5f5f5;
	}

	&:last-child {
		border-bottom: none;
	}
}

.avatar-container {
	position: relative;
	margin-right: 24rpx;
}

.avatar {
	width: 96rpx;
	height: 96rpx;
	border-radius: 50%;
	background-color: #f0f0f0;
}

.unread-badge {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	min-width: 32rpx;
	height: 32rpx;
	background-color: #ff4757;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 2rpx solid #ffffff;
}

.unread-text {
	font-size: 20rpx;
	color: #ffffff;
	font-weight: 600;
	padding: 0 8rpx;
}

.chat-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-width: 0;
}

.chat-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 12rpx;
}

.username {
	font-size: 32rpx;
	font-weight: 500;
	color: #333333;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.chat-time {
	font-size: 24rpx;
	color: #999999;
	margin-left: 16rpx;
	flex-shrink: 0;
}

.chat-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.last-message {
	font-size: 28rpx;
	color: #666666;
	flex: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	line-height: 1.4;
}

.ai-tag {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 24rpx;
	padding: 8rpx 16rpx;
	margin-left: 16rpx;
	flex-shrink: 0;
}

.ai-text {
	font-size: 20rpx;
	color: #ffffff;
	font-weight: 500;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 60vh;
	background-color: #ffffff;
}

.empty-icon {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 32rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 28rpx;
	color: #999999;
}

.loading-state {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	background-color: #ffffff;
}

.loading-text {
	font-size: 28rpx;
	color: #999999;
}

.load-more {
	padding: 30rpx 0;
	text-align: center;
	background-color: #ffffff;
}

.load-more-loading {
	display: flex;
	align-items: center;
	justify-content: center;
}

.load-more-loading::before {
	content: '';
	width: 32rpx;
	height: 32rpx;
	border: 4rpx solid #e5e5e5;
	border-top-color: #007aff;
	border-radius: 50%;
	animation: loading-spin 1s linear infinite;
	margin-right: 16rpx;
}

.load-more-text {
	font-size: 28rpx;
	color: #999999;
}

.load-more-nomore .load-more-text {
	color: #cccccc;
}

.load-more-default .load-more-text {
	color: #007aff;
}

@keyframes loading-spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* 适配安全区域 */
.chat-list-container {
	padding-bottom: env(safe-area-inset-bottom);
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.chat-item {
		padding: 20rpx 24rpx;
	}

	.avatar {
		width: 80rpx;
		height: 80rpx;
	}

	.username {
		font-size: 30rpx;
	}

	.last-message {
		font-size: 26rpx;
	}
}
</style>
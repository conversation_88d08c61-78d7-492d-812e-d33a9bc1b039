{
  "easycom": {
    "^u-(.*)": "@/uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    {
      "path": "package/pages/game/detail",
      "style": {
        "navigationBarTitleText": "",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom",
        "app-plus": {
          "bounce": "none"
        }
      }
    },
    {
      "path": "pages/index/details",
      "style": {
        "navigationBarTitleText": "动态",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/hongniang/index",
      "style": {
        "navigationBarTitleText": "红娘牵线",
        "enablePullDownRefresh": true,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/news/index",
      "style": {
        "navigationBarTitleText": "消息",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/news/chat",
      "style": {
        "navigationBarTitleText": "聊天",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/news/assistant",
      "style": {
        "navigationBarTitleText": "官方助手",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/news/notifice",
      "style": {
        "navigationBarTitleText": "消息通知",
        "enablePullDownRefresh": false,
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/index/webView",
      "style": {
        "navigationBarTitleText": "网页"
      }
    },
    {
      "path": "pages/my/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/my/invitationUser",
      "style": {
        "navigationBarTitleText": "邀请好友",
        "enablePullDownRefresh": false,
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#FFFFFF",
        "app-plus": {
          "bounce": "none",
          "scrollIndicator": "none",
          "titleNView": true
        }
      }
    },
    {
      "path": "pages/my/userinfo",
      "style": {
        "navigationBarTitleText": "个人信息",
        "navigationBarTextStyle": "black",
        "navigationBarBackgroundColor": "#FFFFFF"
      }
    },
    {
      "path": "pages/public/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationBarBackgroundColor": "#ffffff"
      }
    },
    {
      "path": "pages/public/bind",
      "style": {
        "navigationBarTitleText": "绑定手机号"
      }
    },
    {
      "path": "pages/public/forgetPwd",
      "style": {
        "navigationBarTitleText": "忘记密码"
      }
    },
    {
      "path": "pages/public/loginphone",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationBarBackgroundColor": "#ffffff"
      }
    },
    {
      "path": "pages/public/pwd",
      "style": {
        "navigationBarTitleText": "修改密码",
        "navigationBarBackgroundColor": "#FFFFFF",
        "navigationBarTextStyle": "black"
      }
    },
    {
      "path": "pages/public/register",
      "style": {
        "navigationBarTitleText": "注册",
        "navigationBarBackgroundColor": "#ffffff"
      }
    },
		{
			"path": "my/hongniang/admin",
			"style": {
				"navigationBarTitleText": "红娘工作台",
				"enablePullDownRefresh": true,
				"navigationStyle": "custom"
			}
		}
  ],
  "subPackages": [
    {
      "root": "package/pages",
      "pages": [
        {
          "path": "game/pay",
          "style": {
            "navigationBarTitleText": "购买记录",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "game/dongtai",
          "style": {
            "navigationBarTitleText": "",
            "navigationStyle": "custom",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "detail/qianxian",
          "style": {
            "navigationBarTitleText": "红娘牵线",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "detail/tuijian",
          "style": {
            "navigationBarTitleText": "超级推荐",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "detail/listDetails",
          "style": {
            "navigationBarTitleText": "动态详情",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "detail/dongtai",
          "style": {
            "navigationBarTitleText": "我的动态",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "detail/goreport",
          "style": {
            "navigationBarTitleText": "我的投诉",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "detail/youhuijuan",
          "style": {
            "navigationBarTitleText": "我的优惠券",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "releaseone/releaseone",
          "style": {
            "navigationBarTitleText": "",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "feedbackIndex/feedbackIndex",
          "style": {
            "navigationBarTitleText": "帮助与反馈",
            "enablePullDownRefresh": false,
            "navigationBarBackgroundColor": "#FFFFFF"
          }
        },
        {
          "path": "helpDetail/helpDetail",
          "style": {
            "navigationBarTitleText": "帮助详情",
            "enablePullDownRefresh": false
          }
        }
      ]
    },
    {
      "root": "my",
      "pages": [
        {
          "path": "hongniang/renzheng",
          "style": {
            "navigationBarTitleText": "申请成为红娘",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "Events/detail",
          "style": {
            "navigationBarTitleText": "活动详情",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "Events/index",
          "style": {
            "navigationBarTitleText": "活动",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "Events/huodong",
          "style": {
            "navigationBarTitleText": "我的活动",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "Events/hnList",
          "style": {
            "navigationBarTitleText": "活动管理",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "Events/bmlist",
          "style": {
            "navigationBarTitleText": "当前报名",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "gird/mylike",
          "style": {
            "navigationBarTitleText": "我喜欢的",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "gird/like",
          "style": {
            "navigationBarTitleText": "喜欢我的",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "gird/guanzhu",
          "style": {
            "navigationBarTitleText": "我的粉丝",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "renzheng/index",
          "style": {
            "navigationBarTitleText": "实名认证",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "renzheng/renzheng",
          "style": {
            "navigationBarTitleText": "实名认证",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "renzheng/xueli",
          "style": {
            "navigationBarTitleText": "学生认证",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "vip/index",
          "style": {
            "navigationBarTitleText": "开通会员",
            "navigationBarBackgroundColor": "#FDE1EF",
            "navigationBarTextStyle": "black",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "wallet/Txmoney",
          "style": {
            "navigationBarTitleText": "我的钱包",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "wallet/qianbao",
          "style": {
            "navigationBarTitleText": "我的钱包",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "team/team",
          "style": {
            "navigationBarTitleText": "我的团队",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "team/earnings",
          "style": {
            "navigationBarTitleText": "团队收益",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "team/mysylist",
          "style": {
            "navigationBarTitleText": "收益明细",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "wallet/zhifubao",
          "style": {
            "navigationBarTitleText": "提现账号",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "wallet/wallet",
          "style": {
            "navigationBarTitleText": "提现",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "wallet/cashList",
          "style": {
            "navigationBarTitleText": "提现记录",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "wallet/mymoneydetail",
          "style": {
            "navigationBarTitleText": "钱包明细",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "order/index",
          "style": {
            "navigationBarTitleText": "我的订单",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "order/pay",
          "style": {
            "navigationBarTitleText": "待付款",
            "navigationBarBackgroundColor": "#ffffff"
          }
        },
        {
          "path": "order/complain",
          "style": {
            "navigationBarTitleText": "投诉"
          }
        },
        {
          "path": "order/declarationCenter",
          "style": {
            "navigationBarTitleText": "报单中心",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "publish/index",
          "style": {
            "navigationBarTitleText": "我的技能",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "publish/editor",
          "style": {
            "navigationBarTitleText": "我的技能",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "publish/orderDetail",
          "style": {
            "navigationBarTitleText": "抢单详情",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/customer",
          "style": {
            "navigationBarTitleText": "在线客服",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/userinfo",
          "style": {
            "navigationBarTitleText": "我的资料"
          }
        },
        {
          "path": "feedback/index",
          "style": {
            "navigationBarTitleText": "意见反馈",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/help",
          "style": {
            "navigationBarTitleText": "帮助中心",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/index",
          "style": {
            "navigationBarTitleText": "设置中心",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/mimi",
          "style": {
            "navigationBarTitleText": "隐私政策",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/vipxieyi",
          "style": {
            "navigationBarTitleText": "会员服务协议",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/xieyi",
          "style": {
            "navigationBarTitleText": "用户协议",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/xieyi2",
          "style": {
            "navigationBarTitleText": "协议说明",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "store/info",
          "style": {
            "navigationBarTitleText": "门店信息",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "store/downLine",
          "style": {
            "navigationBarTitleText": "我的下线",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "store/storeOrder",
          "style": {
            "navigationBarTitleText": "门店订单",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
				{
					"path": "store/storeIncome",
					"style": {
						"navigationBarTitleText": "门店收益",
						"navigationBarBackgroundColor": "#FFFFFF",
						"navigationBarTextStyle": "black"
					}
				},
        {
          "path": "store/staffList",
          "style": {
            "navigationBarTitleText": "员工列表",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/about",
          "style": {
            "navigationBarTitleText": "关于我们",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "setting/userphone",
          "style": {
            "navigationBarTitleText": "修改手机号"
          }
        },
        {
          "path": "order/feedback",
          "style": {
            "navigationBarTitleText": "评论",
            "navigationBarBackgroundColor": "#FFFFFF",
            "navigationBarTextStyle": "black"
          }
        },
        {
          "path": "yuewanOrder/index",
          "style": {
            "navigationBarTitleText": "我的派单",
            "enablePullDownRefresh": true,
            "navigationBarBackgroundColor": "#FFFFFF"
          }
        },
        {
          "path": "setting/logOff",
          "style": {
            "navigationBarTitleText": "注销帐号",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "setting/offXieyi",
          "style": {
            "navigationBarTitleText": "用户注销协议",
            "enablePullDownRefresh": false
          }
        },
        {
          "path": "myMsg/myMsg",
          "style": {
            "navigationBarTitleText": "消息",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "hongniang/admin",
          "style": {
            "navigationBarTitleText": "红娘工作台",
            "enablePullDownRefresh": true,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "hongniang/qxList",
          "style": {
            "navigationBarTitleText": "牵线记录"
          }
        },
        {
          "path": "hongniang/changeType",
          "style": {
            "navigationBarTitleText": " ",
            "navigationStyle": "custom"
          }
        },
        {
          "path": "hongniang/changeUser",
          "style": {
            "navigationBarTitleText": "选择会员",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "hongniang/publishUser",
          "style": {
            "navigationBarTitleText": "发布信息"
          }
        },
        {
          "path": "hongniang/releaseone",
          "style": {
            "navigationBarTitleText": "发布动态"
          }
        },
        {
          "path": "hongniang/myUsers",
          "style": {
            "navigationBarTitleText": "我服务的会员",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "hongniang/myDongtai",
          "style": {
            "navigationBarTitleText": "红娘动态",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "shop/shopSet",
          "style": {
            "navigationBarTitleText": "我要开店"
          }
        },
        {
          "path": "hongniang/hongniangPay",
          "style": {
            "navigationBarTitleText": "开通婚介"
          }
        },
        {
          "path": "pay/paySuccess",
          "style": {
            "navigationBarTitleText": "支付成功"
          }
        }
      ]
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "相亲",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#FFFFFF"
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#FF76A1",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "package/pages/game/detail",
        "iconPath": "static/tabbar/index.png",
        "selectedIconPath": "static/tabbar/index_.png",
        "text": "首页",
        "visible": true
      },
      {
        "pagePath": "pages/index/details",
        "iconPath": "static/tabbar/shequ.png",
        "selectedIconPath": "static/tabbar/shequ_.png",
        "text": "动态"
      },
      {
        "pagePath": "my/hongniang/admin",
        "iconPath": "static/tabbar/worker.png",
        "selectedIconPath": "static/tabbar/worker_.png",
        "text": "工作台",
        "visible": false
      },
      {
        "pagePath": "pages/hongniang/index",
        "iconPath": "static/tabbar/hn.png",
        "selectedIconPath": "static/tabbar/hn_.png",
        "text": "红娘",
        "visible": true
      },
      {
        "pagePath": "pages/news/index",
        "iconPath": "static/tabbar/news.png",
        "selectedIconPath": "static/tabbar/news_.png",
        "text": "消息",
        "visible": true
      },
      {
        "pagePath": "pages/my/index",
        "iconPath": "static/tabbar/my.png",
        "selectedIconPath": "static/tabbar/my_.png",
        "text": "我的"
      }
    ]
  },
	"condition" : { //模式配置，仅开发期间生效
		"current": 0, //当前激活的模式(list 的索引项)
		"list": [
			{
				"name": "聊天", //模式名称
				"path": "pages/news/chat", //启动页面，必选
				"query": "userId=4&username=甜桃" //启动参数，在页面的onLoad函数里面得到
			}
		]
	}
}

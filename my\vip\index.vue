<template>
  <view class="pages">
    <img
      src="https://photo.zastatic.com/images/common-cms/it/20250527/1748334546234_150695_t.png"
      style="width: 100vw"
      class="bg-img"
      alt=""
    />
    <view class="tab-switch">
      <img
        class="back-icon"
        @click="goBack"
        src="https://photo.zastatic.com/images/common-cms/it/20250527/1748353647707_201331.png"
        alt=""
      />
      <view
        @click="changeTab(0)"
        :class="tabIndex === 0 ? 'tab-switch-item active' : 'tab-switch-item'"
        >开通会员</view
      >/
      <view
        @click="changeTab(1)"
        :class="tabIndex === 1 ? 'tab-switch-item active' : 'tab-switch-item'"
        >人工牵线</view
      >
    </view>
    <view class="vipItem" v-show="tabIndex == 0">
      <view class="vipbox">
        <view class="margin-left">
          <view class="viptit">VIP会员</view>
          <view
            class="text-sm margin-top-xs"
            style="color: #ff5974"
            v-if="!isVip"
            >您暂未开通会员</view
          >
          <view class="text-sm margin-top-xs" style="color: #ff5974" v-else
            >会员到期时间：{{ vipEndTime }}</view
          >
        </view>
        <view class="xin">
          <image
            src="../static/huiyuan.png"
            style="width: 254rpx; height: 203rpx"
          ></image>
        </view>
      </view>

      <view class="vipcint">
        <view class="flex margin-top-xs flex-wrap justify-between">
          <view
            v-for="(item, index) in vipList"
            :key="index"
            @click="select(item, index)"
            class="box"
            :class="selNum == index ? 'active' : ''"
          >
            <view class="text-center">
              <view class="">{{ item.vipName }}</view>
              <view class="margin-top-xs"
                ><text class="text-xxl text-bold">{{ item.money }}</text>
                元
              </view>
            </view>
          </view>
          <view class="box" style="border: none; height: 0"></view>
          <view class="box" style="border: none; height: 0"></view>
        </view>
        <view
          class="margin-tb-sm text-center text-lg text-bold"
          style="color: #333333"
          >超多会员权益 爱你更多姿势</view
        >
        <view>
          <view v-for="(item, index) in MemberList" :key="index">
            <view class="flex align-center margin-bottom-xl">
              <image
                :src="item.memberImg"
                style="width: 70rpx; height: 41rpx"
              ></image>
              <view class="margin-left">
                <view class="text-bold" style="color: #333333"
                  >{{ item.memberName }}
                  <text v-if="index == 1">+{{ addRecommendCount }}</text>
                  <text v-if="index == 2">{{ addPhoneCount }}次</text>
                </view>
                <view style="color: #999999; font-size: 22rpx"
                  >{{ item.memberContent }}
                </view>
              </view>
            </view>
          </view>
          <!-- <view class="flex align-center margin-bottom-xl">
						<image src="../static/vipone.png" style="width: 70rpx;height: 41rpx;"></image>
						<view class="margin-left">
							<view class="text-bold" style="color:#333333 ;">无限查看谁喜欢我</view>
							<view style="color: #999999;font-size: 22rpx;">解锁全部喜欢我的人，急速匹配</view>
						</view>
					</view>
					<view class="flex align-center margin-bottom-xl">
						<view class="text-center" style="width: 70rpx;">
							<image src="../static/viptwo.png" style="width: 52rpx;height: 44rpx;"></image>
						</view>
						<view class="margin-left">
							<view class="text-bold" style="color:#333333 ;">每日超级推荐+5</view>
							<view style="color: #999999;font-size: 22rpx;">被更多嘉宾浏览，获得更多机会</view>
						</view>
					</view>
					<view class="flex align-center margin-bottom">
						<view class="text-center" style="width: 70rpx;">
							<image src="../static/vipthree.png" style="width: 58rpx;height: 44rpx;"></image>
						</view>
						<view class="margin-left">
							<view class="text-bold" style="color:#333333 ;">每日获取联系方式3次</view>
							<view style="color: #999999;font-size: 22rpx;">直接获取对方联系，配对率提高10倍</view>
						</view>
					</view> -->
        </view>
        <view style="text-align: center">
          <u-checkbox-group>
            <u-checkbox
              v-model="checked"
              shape="circle"
              active-color="#FF5974"
              size="28"
            >
              <view class="text-sm" style="color: #999999">
                同意
                <text style="color: #ff5974" @click="govip"
                  >《会员服务协议》</text
                >
              </view>
            </u-checkbox>
          </u-checkbox-group>
        </view>
        <view class="btn" @click="handleOpenPay" v-if="!isVip"
          >{{ price }}元开通</view
        >
      </view>
    </view>
    <view class="qianxianItem" v-show="tabIndex == 1">
      <view class="vipcint">
        <view class="flex flex-wrap card-list">
          <view
            v-for="(item, index) in qianxianList"
            :key="index"
            @click="qianxianSelect(item, index)"
            class="qianxian-card"
            :class="qianxianIndex === index ? 'active' : ''"
          >
            <view class="card-time">{{ item.time }}个月</view>
            <view class="card-times">{{ item.desc }}</view>
            <view class="card-price">{{ item.price }}</view>
            <view class="card-oldprice">原价¥{{ item.oldPrice }}</view>
            <view class="card-pricePerDay">
              ￥{{ item.pricePerDay }}元/天
            </view>
            <view class="card-level">等级{{ index + 1 }}</view>
          </view>
        </view>
        <view class="qianxian-rights">
          <view class="qianxian-rights-title">尊享特权</view>
          <view class="qianxian-rights-list">
            <view class="qianxian-rights-item">
              <img
                class="rights-icon"
                src="https://photo.zastatic.com/images/common-cms/it/20250527/1748339276354_467030_t.png"
                alt=""
              />
              <text class="rights-text">心仪对象牵线</text>
            </view>
            <view class="qianxian-rights-item">
              <img
                class="rights-icon"
                src="https://photo.zastatic.com/images/common-cms/it/20250527/1748339276331_262737_t.png"
                alt=""
              />
              <text class="rights-text">优质异性推荐</text>
            </view>
            <view class="qianxian-rights-item">
              <img
                class="rights-icon"
                src="https://photo.zastatic.com/images/common-cms/it/20250527/1748339276354_803394_t.png"
                alt=""
              />
              <text class="rights-text">尊享专属标识</text>
            </view>
          </view>
        </view>
        <view class="payment">
          <view class="payment-title">支付方式</view>
          <view class="payment-method">
            <img
              class="payment-icon"
              src="https://photo.zastatic.com/images/common-cms/it/20250527/1748341227691_317879_t.png"
              alt=""
            />
            <radio-group
              name="openWay"
              style="margin-left: 20upx"
              @click="alipaySelected = !alipaySelected"
            >
              <label class="tui-radio">
                <radio color="#FF5974" :checked="alipaySelected" />
              </label>
            </radio-group>
          </view>
        </view>
        <view style="position: fixed; bottom: 0; left: 0; width: 100%">
          <view class="btn" @click="openpay" v-if="!isVip">立即续费</view>
          <view
            style="text-align: center; margin-top: 20rpx; margin-bottom: 40rpx"
          >
            <u-checkbox-group>
              <u-checkbox
                v-model="checked"
                shape="circle"
                active-color="#FF5974"
                size="28"
              >
                <view class="text-sm" style="color: #999999">
                  购买即代表同意
                  <text style="color: #ff7aa5" @click="govip"
                    >《囍上媒捎人工牵线服务协议》</text
                  >
                </view>
              </u-checkbox>
            </u-checkbox-group>
          </view>
        </view>
      </view>
    </view>

    <u-popup v-model="show" mode="bottom" border-radius="24">
      <view class="padding">
        <view class="text-center text-bold text-lg">支付方式</view>
        <view
          class="flex align-center justify-between"
          style="height: 100upx; padding: 30upx"
          v-for="(item, index) in openLists"
          :key="index"
        >
          <image
            :src="item.image"
            style="width: 55upx; height: 55upx; border-radius: 50upx"
          ></image>
          <view style="font-size: 30upx; margin-left: 0upx; width: 70%"
            >{{ item.text }}
          </view>
          <radio-group
            name="openWay"
            style="margin-left: 20upx"
            @tap="selectWay(item)"
          >
            <label class="tui-radio">
              <radio
                color="#FF5974"
                :checked="openWay === item.id ? true : false"
              />
            </label>
          </radio-group>
        </view>
        <!-- <view class="flex align-center justify-between" style="height: 100upx;padding: 30upx;"
					v-for="(item,index) in openList" :key='index' v-if="mymoney>0">
					<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;"></image>
					<view style="font-size: 30upx;margin-left:0upx;width: 70%;">{{item.text}}
					</view>
					<radio-group name="openWay" style="margin-left: 20upx;" @tap='selectWay(item)'>
						<label class="tui-radio">
							<radio color="#777CFE" :checked="openWay === item.id ? true : false" />
						</label>
					</radio-group>
				</view> -->
        <view class="btn margin-top" @click="pay">立即支付</view>
      </view>
    </u-popup>

    <!-- modal弹窗 -->
    <u-modal
      v-model="meShowModel"
      :content="meContent"
      :title="meTitle"
      :show-cancel-button="meShowCancel"
      @cancel="meHandleClose"
      @confirm="meHandleBtn"
      :confirm-text="meConfirmText"
      :cancel-text="meCancelText"
    ></u-modal>
  </view>
</template>

<script>
const qianxianList = [
  {
    time: "6",
    price: 998,
    desc: "专属红娘牵线2次",
    oldPrice: 1998,
    pricePerDay: "5.42",
  },
  {
    time: "12",
    price: 1998,
    desc: "专属红娘牵线5次",
    oldPrice: 2998,
    pricePerDay: "5.47",
  },
  {
    time: "12",
    price: 5898,
    desc: "专属红娘牵线不限次",
    oldPrice: 16998,
    pricePerDay: "16.16",
  },
];
export default {
  data() {
    return {
      background: {
        backgroundImage:
          "linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)",
      },
      vipList: [],
      selNum: 0,
      price: 0,
      MemberList: [],
      checked: false,
      isVip: false,
      vipEndTime: "",
      show: false,
      openLists: [],
      openWay: 1,
      openList: [
        {
          image: "../../static/images/my/cz.png",
          text: "零钱",
          id: 3,
        },
      ],
      mymoney: 0,
      //弹窗
      meShowModel: false, //是否显示弹框
      meShowCancel: true, //是否显示取消按钮
      meTitle: "提示", //弹框标题
      meContent: "", //弹框内容
      meConfirmText: "确认", //确认按钮的文字
      meCancelText: "取消", //关闭按钮的文字
      meIndex: "", //弹窗的key

      addRecommendCount: "", //曝光次数
      addPhoneCount: "", //联系方式

      tabIndex: 0,
      qianxianList: qianxianList,
      qianxianIndex: 0,

      alipaySelected: false,
    };
  },
  onLoad() {
    // #ifdef APP-PLUS
    this.openLists = [
      {
        image: "../static/zhifubao.png",
        text: "支付宝",
        id: 1,
      },
      {
        image: "../static/icon_weixin.png",
        text: "微信",
        id: 2,
      },
      {
        image: "../../static/images/my/cz.png",
        text: "零钱",
        id: 3,
      },
    ];
    this.openWay = 1;
    // #endif

    // #ifdef MP-WEIXIN
    this.openLists = [
      {
        image: "../static/icon_weixin.png",
        text: "微信",
        id: 2,
      },
      {
        image: "../../static/images/my/cz.png",
        text: "零钱",
        id: 3,
      },
    ];
    this.openWay = 2;
    // #endif

    // #ifdef H5
    let ua = navigator.userAgent.toLowerCase();
    if (ua.indexOf("micromessenger") !== -1) {
      //公众号是否自动登录  333
      this.$Request.get("/app/common/type/333").then((res) => {
        if (res.data && res.data.value && res.data.value == "是") {
          this.openLists = [
            {
              image: "../static/zhifubao.png",
              text: "支付宝",
              id: 1,
            },
            {
              image: "../static/icon_weixin.png",
              text: "微信",
              id: 2,
            },
            {
              image: "../../static/images/my/cz.png",
              text: "零钱",
              id: 3,
            },
          ];
          this.openWay = 2;
        } else {
          this.openLists = [
            {
              image: "../static/zhifubao.png",
              text: "支付宝",
              id: 1,
            },
            {
              image: "../../static/images/my/cz.png",
              text: "零钱",
              id: 3,
            },
          ];
          this.openWay = 1;
        }
      });
    } else {
      this.openLists = [
        {
          image: "../static/zhifubao.png",
          text: "支付宝",
          id: 1,
        },
        {
          image: "../../static/images/my/cz.png",
          text: "零钱",
          id: 3,
        },
      ];
      this.openWay = 1;
    }
    // #endif

    this.getUserInfo();
    this.getVipList();
    // this.getUserVip()
    this.getMemberList();
    this.taskData();
  },
  methods: {
    //确认
    meHandleBtn() {
      let that = this;
      if (that.meIndex == "m7") {
        uni.navigateTo({
          url: "/my/wallet/Txmoney",
        });
      }
    },
    //取消
    meHandleClose() {
      let that = this;
      if (that.meIndex == "m7") {
      }
    },
    // 获取余额
    taskData() {
      this.$Request.get("/app/userMoney/selectMyMoney").then((res) => {
        if (res.code == 0 && res.data) {
          this.mymoney = res.data.money;
        }
      });
    },
    govip() {
      uni.navigateTo({
        url: "/my/setting/vipxieyi",
      });
    },
    getUserInfo() {
      this.$Request.get("/app/user/selectUserById").then((res) => {
        if (res.code == 0) {
          this.vipEndTime = res.data.vipEndTime;
          // 会员  0不是  1是
          if (res.data.isVip && res.data.isVip == 1) {
            this.isVip = true;
            uni.setStorageSync("isVIP", this.isVip);
          } else {
            this.isVip = false;
            uni.setStorageSync("isVIP", this.isVip);
          }
        }
      });
    },
    //获取VIP列表
    getVipList() {
      this.$Request.get("/app/vipDetails/getVipDetailsList").then((res) => {
        if (res.code == 0) {
          this.vipList = res.data.records;
          // this.selNum = this.vipList.records[0].id
          if (this.vipList.length != 0) {
            this.price = this.vipList[0].money;
            this.addRecommendCount = this.vipList[0].addRecommendCount;
            this.addPhoneCount = this.vipList[0].addPhoneCount;
          }
        }
      });
    },
    select(e, index) {
      console.log(e);
      this.selNum = index;
      this.price = e.money;
      this.addRecommendCount = e.addRecommendCount;
      this.addPhoneCount = e.addPhoneCount;
    },
    // 获取特权列表
    getMemberList() {
      this.$Request.get("/app/member/getMemberList").then((res) => {
        if (res.code == 0) {
          this.MemberList = res.data.records;
        }
      });
    },
    selectWay: function (item) {
      this.openWay = item.id;
    },
    openpay() {
      if (!this.checked) {
        uni.showToast({
          title: "请先阅读并同意《服务协议》",
          icon: "none",
        });
        return;
      }
      if (!this.alipaySelected) {
        uni.showToast({
          title: "请选择支付方式",
          icon: "none",
        });
        return;
      }
      uni.showLoading({
        title: "支付中",
      });
      setTimeout(() => {
        uni.hideLoading();
        setTimeout(() => {
          uni.reLaunch({
            url: "/my/pay/paySuccess",
          });
        }, 1500);
      }, 1000);
    },
    handleOpenPay() {
      if (!this.checked) {
        uni.showToast({
          title: "请同意会员服务协议",
          icon: "none",
        });
        return;
      }
      this.show = true;
    },
    pay() {
      uni.showLoading({
        title: "支付中...",
      });
      this.show = false;
      let id = this.vipList[this.selNum].id;
      if (this.openWay == 1) {
        //支付宝支付
        // #ifdef H5
        let data = {
          classify: 5,
          id: id,
        };
        this.$Request.postT("/app/vipDetails/userBuyVip", data).then((res) => {
          if (res.code == 0) {
            const div = document.createElement("div");
            div.innerHTML = res.data; //此处form就是后台返回接收到的数据
            document.body.appendChild(div);
            document.forms[0].submit();
            uni.hideLoading();
          } else {
            uni.showToast({
              icon: "none",
              title: "支付失败!",
            });
          }
        });
        // #endif

        // #ifdef APP
        let data = {
          classify: 4,
          id: id,
        };
        this.$Request.postT("/app/vipDetails/userBuyVip", data).then((ret) => {
          console.log(ret);
          that.isCheckPay(ret.code, "alipay", ret.data);
        });
        // #endif
      } else if (this.openWay == 2) {
        //微信支付
        // #ifdef MP-WEIXIN
        let data = {
          classify: 2,
          id: id,
        };
        this.$Request.postT("/app/vipDetails/userBuyVip", data).then((ret) => {
          uni.hideLoading();
          console.log(ret);
          uni.requestPayment({
            provider: "wxpay",
            timeStamp: ret.data.timestamp,
            nonceStr: ret.data.noncestr,
            package: ret.data.package,
            signType: ret.data.signType,
            paySign: ret.data.sign,
            success: function (suc) {
              console.log("success:" + JSON.stringify(suc));
              uni.showToast({
                title: "支付成功",
                icon: "success",
              });
              setTimeout(function () {
                uni.navigateBack();
              }, 1000);
            },
            fail: function (err) {
              console.log("fail:" + JSON.stringify(err));
              uni.showToast({
                title: "支付失败",
                icon: "none",
              });
            },
          });
        });
        // #endif
        // #ifdef H5
        let data = {
          classify: 3,
          id: id,
        };
        this.$Request.postT("/app/vipDetails/userBuyVip", data).then((res) => {
          if (res.code == 0) {
            uni.hideLoading();
            this.callPay(res.data);
          } else {
            uni.showToast({
              icon: "none",
              title: res.msg,
            });
          }
        });
        // #endif
        // #ifdef APP
        let data = {
          classify: 1,
          id: id,
        };
        this.$Request.postT("/app/vipDetails/userBuyVip", data).then((ret) => {
          console.log(ret, "retretretretretret");
          this.isCheckPay(ret.code, "wxpay", JSON.stringify(ret.data));
        });
        // #endif
      } else if (this.openWay == 3) {
        if (this.mymoney >= this.price) {
          let data = {
            classify: 0,
            id: id,
          };
          this.$Request
            .postT("/app/vipDetails/userBuyVip", data)
            .then((res) => {
              if (res.code == 0) {
                uni.hideLoading();
                uni.showToast({
                  title: "支付成功",
                  icon: "success",
                });
                setTimeout(function () {
                  uni.navigateBack();
                }, 1000);
              } else {
                uni.showToast({
                  icon: "none",
                  title: res.msg,
                });
              }
            });
        } else {
          uni.hideLoading();
          this.meShowModel = true;
          this.meTitle = "提示";
          this.meContent = "零钱余额不足，请选去充值";
          this.meConfirmText = "去充值";
          this.meIndex = "m7";
          this.meShowCancel = true;
        }
      }
    },
    callPay: function (response) {
      console.log(response);
      if (typeof WeixinJSBridge === "undefined") {
        if (document.addEventListener) {
          document.addEventListener(
            "WeixinJSBridgeReady",
            this.onBridgeReady(response),
            false
          );
        } else if (document.attachEvent) {
          document.attachEvent(
            "WeixinJSBridgeReady",
            this.onBridgeReady(response)
          );
          document.attachEvent(
            "onWeixinJSBridgeReady",
            this.onBridgeReady(response)
          );
        }
      } else {
        console.log(1);
        this.onBridgeReady(response);
      }
    },
    onBridgeReady: function (response) {
      let that = this;
      if (!response.package) {
        return;
      }
      console.log(response, "++++++++");
      WeixinJSBridge.invoke(
        "getBrandWCPayRequest",
        {
          appId: response.appid, //公众号名称，由商户传入
          timeStamp: response.timestamp, //时间戳，自1970年以来的秒数
          nonceStr: response.noncestr, //随机串
          package: response.package,
          signType: response.signType, //微信签名方式：
          paySign: response.sign, //微信签名
        },
        function (res) {
          console.log(res, "/*-/*-/*-");
          if (res.err_msg === "get_brand_wcpay_request:ok") {
            // 使用以上方式判断前端返回,微信团队郑重提示：
            //res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
            uni.showLoading({
              title: "支付成功",
            });
            uni.hideLoading();
            setTimeout(function () {
              uni.navigateBack();
            }, 1000);
          } else {
            uni.hideLoading();
          }
          WeixinJSBridge.log(response.err_msg);
        }
      );
    },
    isCheckPay(status, name, order) {
      if (status == 0) {
        this.setPayment(name, order);
      } else {
        uni.hideLoading();
        uni.showToast({
          title: "支付信息有误",
          icon: "none",
        });
      }
    },
    setPayment(name, order) {
      console.log("*-*-*");
      uni.requestPayment({
        provider: name,
        orderInfo: order, //微信、支付宝订单数据
        success: function (res) {
          console.log(res);
          uni.hideLoading();

          setTimeout(function () {
            uni.navigateBack();
          }, 1000);
        },
        fail: function (err) {
          console.log(err);
          uni.hideLoading();
        },
        complete() {
          uni.hideLoading();
        },
      });
    },
    changeTab(index) {
      this.tabIndex = index;
    },
    qianxianSelect(item, index) {
      this.qianxianIndex = index;
    },
    goBack() {
      uni.navigateBack();
    },
  },
};
</script>

<style lang="scss">
* {
  box-sizing: border-box;
}
.pages {
  height: 110vh;
  background: #fff;
  position: relative;
  // background: #f8fafc;
}
.bg-img {
  position: absolute;
  left: 0;
  top: 0;
}

.tab-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 48rpx;
  width: 100vw;
  padding: 26rpx 0rpx;
  font-size: 36rpx;
  color: #323233;
  font-weight: 400;
  line-height: 38rpx;
  letter-spacing: 1rpx;

  .back-icon {
    width: 30rpx;
    height: 30rpx;
    position: absolute;
    left: 45rpx;
  }
}
.tab-switch-item {
  text-align: center;
  transition: font-weight 0.3s ease-in-out;
  &.active {
    font-weight: 700;
  }
}

.vipItem {
  background: #fff4f4;
  border-radius: 24rpx;
  margin-top: 40rpx;
  position: absolute;
  top: 200rpx;

  .vipbox {
    height: 156rpx;
    display: flex;
    align-items: center;
    position: relative;

    .viptit {
      font-size: 42rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #333333;
    }

    .xin {
      position: absolute;
      bottom: 0;
      right: 19rpx;
    }
  }

  .vipcint {
    background: #ffffff;
    padding: 30rpx 24rpx;
    border-radius: 24rpx;

    .box {
      width: 206rpx;
      height: 206rpx;
      background: #ffffff;
      border: 2px solid #e6e6e6;
      border-radius: 8rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 18rpx;
      &.active {
        background: #ffe2ec;
        border: 2px solid #ff6d9d;
      }
    }

    .btn {
      width: 90vw;
      height: 88rpx;
      background-image: linear-gradient(97deg, #ff7aa5 0%, #ffa3c4 100%);
      border-radius: 16px;
      font-size: 28rpx;
      font-family: PingFang SC;
      font-weight: bold;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 30rpx 0;
    }
  }
}
.qianxianItem {
  position: absolute;
  top: 422rpx;
  width: 100vw;
  padding-top: 192rpx;
  background: #fff;
  border-radius: 40rpx 40rpx 0 0;
  .card-list {
    position: absolute;
    top: 14rpx;
    left: 0;
    justify-content: center;
    display: flex;
    width: 100%;
    transform: translateY(-50%);
  }
  .qianxian-card {
    width: 218rpx;
    min-height: 302rpx;
    position: relative;
    background: #fff;
    border: 1rpx solid #fcedcf;
    margin-right: 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 22rpx;
    padding-top: 26rpx;
    box-sizing: border-box;
    &.active {
      border: 4rpx solid #ffd28e;
      background-image: linear-gradient(180deg, #fff0c7 2%, #fffef5 64%);
    }
    &:last-child {
      margin-right: 0rpx;
    }
    .card-time {
      font-size: 28rpx;
      color: #323233;
      font-weight: 600;
      line-height: 40rpx;
      margin-bottom: 10rpx;
    }
    .card-times {
      font-size: 22rpx;
      color: #7e5013;
      line-height: 32rpx;
      margin-bottom: 10rpx;
    }
    .card-price {
      font-size: 40rpx;
      color: #7b4b0c;
      font-weight: 600;
      line-height: 48rpx;
      margin-bottom: 10rpx;
    }
    .card-oldprice {
      font-size: 20rpx;
      color: #969696;
      line-height: 30rpx;
      margin-bottom: 10rpx;
      text-decoration: line-through;
    }
    .card-pricePerDay {
      font-size: 24rpx;
      color: #ff5555;
      font-weight: 500;
      line-height: 32rpx;
      margin-bottom: 10rpx;
    }
    .card-level {
      position: absolute;
      bottom: 0;
      font-size: 28rpx;
      line-height: 32rpx;
      color: #7e5013;
      padding: 10rpx 0;
      width: 100%;
      text-align: center;
      background-image: linear-gradient(258deg, #fbd379 0%, #fee6b2 100%);
      border-radius: 0rpx 0rpx 16rpx 16rpx;
    }
  }
  .qianxian-rights {
    padding: 0rpx 32rpx;
    margin-bottom: 70rpx;
    .qianxian-rights-title {
      font-size: 32rpx;
      color: #000;
      font-weight: 500;
      line-height: 44rpx;
      margin-bottom: 32rpx;
    }
    .qianxian-rights-list {
      display: flex;
      padding: 0rpx 32rpx;
      justify-content: space-between;
      .qianxian-rights-item {
        display: flex;
        align-items: center;
        flex-direction: column;
        margin-right: 60rpx;
        width: 168rpx;

        &:last-child {
          margin-right: 0rpx;
        }
        .rights-icon {
          width: 124rpx;
          height: 124rpx;
          margin-bottom: 24rpx;
        }
        .rights-text {
          font-size: 28rpx;
          line-height: 40rpx;
          font-weight: 400;
        }
      }
    }
  }
  .payment {
    padding: 0 18rpx;
    .payment-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #000000;
      line-height: 44rpx;
      margin-bottom: 32rpx;
    }
    .payment-method {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0rpx 28rpx;
    }
    .payment-icon {
      width: 52rpx;
      height: 52rpx;
    }
  }
}

.btn {
  width: 90vw;
  height: 88rpx;
  background-image: linear-gradient(97deg, #ff7aa5 0%, #ffa3c4 100%);
  border-radius: 15rpx;
  font-size: 36rpx;
  font-family: PingFang SC;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30rpx auto;
}
</style>

<template>
	<view>
		<view class="margin-lr ">
			<view class="margin-top-sm bg" v-for="(item,index) in order" :key="index" @click="goDetail(item)"
				style="border-radius: 24rpx;">
				<view class="padding flex justify-between" style="color: #AC75FE;">
					<view>
						<view v-if="item.status==1">进行中</view>
						<view v-if="item.status==2">匹配成功</view>
						<view v-if="item.status==3">已取消</view>
					</view>
					<view>{{item.createTime}}</view>
				</view>
				<view style="width: 650upx;height: 1upx;background: #e5e5e5;margin: 0upx auto;"></view>
				<view class="padding">
					<view class="flex justify-between">
						<view class="text-lg text-bold">
							{{item.gameName}}<text v-if="item.myArea">-{{item.myArea}}</text>
						</view>

						<view class="flex text-sm align-center">
							<view class="text-xl" style="color: #FF6F1B;">{{item.money}}
								<image src="../../static/share/icon_QQkongjian.png"
									style="width: 20rpx;height: 20rpx;margin: 0rpx 5rpx;"></image></text>
							</view>
						</view>
					</view>
					<view class="flex align-center margin-top-sm">
						<view class="radius margin-right-xs" v-if="item.sex == 0"
							style="background: #f2f2f2;padding:2px 8px 5px 8px;color: #000000;">
							不限
						</view>
						<view v-if="item.sex == 1" class="radius margin-right-xs"
							style="display: inline-block;background: #e7fbff;padding: 2upx 8upx;color: #6EE3FB;">
							<u-icon name="man" color="#6EE3FB" size="24"></u-icon>
							男生
						</view>
						<view v-if="item.sex == 2" class="radius margin-right-xs"
							style="display: inline-block;background: #FBE8EE;padding: 2upx 8upx;color: #FF71A1;">
							<u-icon name="woman" color="#FF71A1" size="24"></u-icon>
							女生
						</view>
						<view class="titBox" v-if="item.myLevel||item.myArea">玩家段位 {{item.myLevel}} <text
								v-if="item.myArea">/ {{item.myArea}}</text></view>
					</view>
					<view class="margin-top" v-if="item.orderLevel">
						期望TA：{{item.orderLevel}}</view>
					<view class="flex justify-between align-end">
						<view>派单时间：{{item.orderTakingTime}}</view>
						<view class="btn" @click="goDetail(item)">详情</view>
					</view>
				</view>
			</view>
		</view>
		<empty v-if="order.length == 0"></empty>
	</view>
</template>

<script>
	import empty from '@/components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				show: false,
				page: 1,
				limit: 10,
				count: '',
				// status: 0,
				order: [],
				show: false
			}
		},
		onLoad() {

			this.getOrderDet()
		},
		onShow() {
			this.getOrderDet()
		},
		methods: {
			//跳转详情
			goDetail(e) {
				uni.navigateTo({
					url: '/package/pages/matching/orderDeatil?fastOrderId=' + e.fastOrderId
				})
			},
			getOrderDet() {
				// this.$queue.showLoading("加载中...");
				let data = {
					page: this.page,
					limit: this.limit,
					status: 0
				}
				this.$Request.get('/app/fastOrder/selectFastOrderPage', data).then(res => {
					if (res.code === 0) {
						if (this.page == 1) {
							this.order = res.data.list
						} else {
							res.data.list.forEach(d => {

								this.order.push(d);
							});
						}
						this.count = res.data.totalCount;
					} else {
						uni.hideLoading();
						this.$queue.showToast(res.msg)
					}
					uni.stopPullDownRefresh();
					uni.hideLoading();
				});
			}
		},
		onReachBottom: function() {
			if (this.order.length == this.count) {
				uni.showToast({
					title: '已经到底了',
					icon: 'none'
				})
			} else {
				this.page = this.page + 1;
				this.getOrderDet();
			}
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getOrderDet();
		}
	}
</script>

<style>
	.bg {
		background: #FFFFFF;
	}

	.jbBg {
		background-image: linear-gradient(to right, #fdf3fe, #f5f0ff, #f3f7ff);
		border: 1rpx solid #eee5ff;
	}

	.titBox {
		background: linear-gradient(90deg, #9F55FE 0%, #33A3FF 100%);
		color: #FFFFFF;
		border-radius: 8upx;
		padding: 7rpx 20rpx;
	}

	.btn {
		/* 	background: linear-gradient(94deg, #E6CCA7 0%, #D0B391 100%);
		color: #5D3D17; */
		color: #AC75FE;
		background: #EADEFF;
		padding: 10rpx 40rpx;
		border-radius: 42rpx;
	}
</style>
<template>
	<view class="">
		<u-navbar title="红娘牵线" :is-back="true" :border-bottom="false" :background="background"
			title-color="#111224"></u-navbar>
		<view class="bgjb"></view>

		<view class="cenoten">
			<view class="box">
				<view class="flex align-center justify-between">
					<view class="flex align-center">
						<image src="../../../static/logo.png" style="width: 90rpx;height: 90rpx;border-radius: 55rpx;">
						</image>
						<view class="margin-left-xs">
							<view class="text-lg">昵称</view>
							<view class="margin-top-xs" style="color: #A6A6A6;">个人标签</view>
						</view>
					</view>
					<view class="flex align-center">
						<view>
							<image src="../static/phone.png" style="width: 67rpx;height:67rpx"></image>
						</view>
						<view class="margin-left-sm">
							<image src="../static/wx.png" style="width: 67rpx;height:67rpx"></image>
						</view>
					</view>
				</view>

				<view class="remkbg">
					<view class="triangle"></view>
					<view class="tit">TA的成功按例</view>
					<view class="coyt">
						<text></text>
						恭喜会员502*45与410*55牵线成功！
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				background: {
					backgroundImage: 'linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)'
				},
			}
		}
	}
</script>

<style lang="less">
	page {
		background: #f8fafc;
	}

	.bgjb {
		width: 100%;
		height: 531rpx;
		background-image: url('../../../static/images/bgImg.png');
		background-size: 100% 100%;
		position: fixed;
		top: 80rpx;
		left: 0;
		right: 0;
		z-index: 0;
		// background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
	}

	.cenoten {
		position: relative;
		z-index: 99;
	}

	.box {
		margin: 25rpx 30rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
	}

	.remkbg {
		background-color: #F8F8F8;
		border-radius: 24rpx;
		padding: 30rpx;
		margin-top: 25rpx;
		position: relative;

		.triangle {
			width: 0;
			height: 0;
			border-left: 16rpx solid transparent;
			border-right: 16rpx solid transparent;
			border-bottom: 25rpx solid #F8F8F8;
			position: absolute;
			top: -16rpx;
			left: 45rpx;
			
			
		}

		.tit {
			font-size: 30rpx;
			font-weight: 600;
		}

		.coyt {
			display: flex;
			align-items: center;
			margin-top: 20rpx;

			text {
				display: inline-flex;
				width: 8rpx;
				height: 8rpx;
				border-radius: 88rpx;
				background: #F99DB5;
				margin-right: 10rpx;
			}
		}
	}
</style>
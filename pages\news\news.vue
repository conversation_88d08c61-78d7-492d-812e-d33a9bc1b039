<template>
  <div
    class="page"
    :class="{ show: render, 'pdb': ispdb}"
    :style="{ 'padding-top': navBar.navBarHeight + 'px' }"
  >
    <scroll-view
      class="content"
      :scroll-y="true"
      :refresher-enabled="true"
      :enable-back-to-top="true"
      :refresher-triggered="refresher"
      @refresherrefresh="reset()"
      @scrolltolower="next()"
    >
      <div
        @click="toChat(item)"
        class="content-item"
        v-for="item in records"
        :key="item.targetUserId"
      >
        <img
          class="content-item-head"
          mode="aspectFill"
          :src="item.targetHead?(item.targetHead + '?imageMogr2/scrop/100x100'):defaultHead"
        />
        <div class="content-item-info">
          <p class="title">
            <span class="nickname">
                            {{item.targetNickname||'游客用户'}}
                        </span>
            <span class="time">
              {{ item.startSessionFlag===undefined?"我抽到的":item.startSessionFlag ? "我抽到的" : "抽到我的"}} |
              {{ item.timeText }}
            </span>
          </p>
          <p class="label">
            <i
              class="sex"
              :style="{ background: item.targetSex ? '#76D2FF' : '#FF99C2' }"
            >
              <img
                mode="aspectFill"
                :src="
                  item.targetSex
                    ? 'https://photo.zastatic.com/images/common-cms/it/20211123/1637661108469_528871_t.png'
                    : 'https://photo.zastatic.com/images/common-cms/it/20211123/1637661134225_995262_t.png'
                "
              />{{ item.targetAge || "" }}
            </i>
            <i class="city" v-if="item.targetUserPlace">
              <img
                mode="aspectFill"
                src="https://photo.zastatic.com/images/common-cms/it/20211123/1637661087482_742531_t.png"
              />
              {{ item.targetUserPlace }}
            </i>
            <i class="logout" v-if="item.targetDelFlag">已注销</i>
          </p>
          <p class="news-content">
            {{ item.lastRecordContent || "你们已经可以开始聊天了" }}
          </p>
          <i class="news-unread" v-if="item.unReadCount > 0">{{
            item.unReadCount > 99 ? "99+" : item.unReadCount
          }}</i>
        </div>
      </div>
    </scroll-view>
    <!-- 关注公众号 -->
    <BaseNewsRemind ref="newsRemind"></BaseNewsRemind>
    <div
      class="prompt flex"
      v-if="isShowSubscribePop"
      @click="goSubscribe"
    >
      <span class="subscribeText">关注公众号接收消息更及时</span>
      <div class="blob">立即关注</div>
    </div>
    <BaseEmpty
      v-if="!records.length"
      class="component_base_empty position-center"
      :showloadBtn="true"
      @reload="extract()"
      img="https://pubser-res.zhenai.com/other/temp/202111/26/15521646535488.png"
    >
      <template v-slot:title>暂无消息</template>
      <template v-slot:message>{{
        showVersionApp == -1 ? "" : "快去抽取桃花签，解锁您的桃花运"
      }}</template>
      <template v-slot:btntitle>去抽桃花签</template>
    </BaseEmpty>
    <BasePhoneLogin ref="phoneLogin"></BasePhoneLogin>
  </div>
</template>

<script>
import * as api from "&/index.js";
import * as tools from "~utils/tools";
import * as socket from "~utils/socket.js";
import * as config from '&/config.js'
export default {
  data() {
    return {
      render: 0,
      refresher: true,
      ispdb: false,
    };
  },
  computed: {
    showVersionApp() {
      return this.$store.state.mpVersionClient ==
        this.$store.state.severVersionClient
        ? -1
        : 1;
    },
    userIdentity() {
      return this.$store.state.user.userIdentity;
    },
    userinfo() {
      return this.$store.state.user.info;
    },
    mustInfo() {
      return this.$store.state.user.mustInfo;
    },
    navBar() {
      return this.$store.state.navBar;
    },
    records() {
      return this.$store.state.im.list;
    },
    isEnd() {
      return this.$store.state.im.isEnd;
    },
    defaultHead(){
      return this.$store.state.user.defaultHead
    },
    isShowSubscribePop(){
      return !this.userinfo.subscribe && this.records.length
    }
  },
  async onCreatedLogin() {
    // 获取总未读数
    socket.unReadCount();

    this.pushData_(1, "消息页UV", this.$store.getters.fromMsg);
  },
  // 加载完会话列表后再渲染
  onImlist() {
    this.render = 1;
  },
  methods: {
    goSubscribe() {
      this.pushData_(8, "点击【立即关注】");
      let url = encodeURIComponent(config.gzhAr);
      uni.navigateTo({
        url: `/pages/other/webview/index?url=${url}`,
      });
    },
    subscribeFocus() {
      if(!this.userinfo.subscribe) {
          this.pushData_(7, "【立即关注】曝光");
          if(this.records.length) {
            this.ispdb = true // 显示下边距
          }
      } else {
        this.ispdb = false
        this.$refs.newsRemind.close()
      }
    },
    // 未放桃花签去放桃花签
    extract() {
      wx.switchTab({
        url: "/pages/tabbar/index/index",
      });
    },
    // 进入消息列表
    async toChat(item) {
      this.pushData_(
        2,
        "点击消息",
        item.targetUserId,
        item.startSessionFlag ? 0 : 1
      );
      wx.navigateTo({
        url: `/pages/user/chat/chat?userId=${item.targetUserId}&sessionUuid=${item.sessionUuid}`,
      });
    },
    // 分页查询列表
    next: tools.debounce(
      function (e) {
        // 无数据了直接退出
        if (this.isEnd) return;
        // 否则分页查询
        this.$store.dispatch("getList");
      },
      500,
      false
    ),
    // 重置下拉刷新列表
    async reset() {
      // 获取未读数
      socket.unReadCount();
      this.refresher = true;
      // 否则分页查询
      await this.$store.dispatch("reset");
      this.refresher = false;
    },
    // 静默更新列表
    update() {
      this.$store.dispatch("reset");
      // 获取未读数
      socket.unReadCount();
    },
    pushData_(accessPoint, accessPointDesc, extString1 = "", extString2 = "") {
      this.pushData({
        resourceKey: "za-Matchmaker_msg_message",
        accessPoint,
        accessPointDesc,
        extString1,
        extString2,
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.page {
  height: 100%;
  box-sizing: border-box;
  color: $ColorGreyScale1;
  background: #fff;
  opacity: 0;
  overflow-y: hidden;
}
.pdb {
  padding-bottom: 80rpx;
}
.show {
  opacity: 1;
}
.content {
  height: 100%;
}
.content-item {
  height: 184rpx;
  padding: 20rpx 32rpx;
  display: flex;
  transition-duration: 0.3s;
  .content-item-head {
    flex-shrink: 0;
    width: 144rpx;
    height: 144rpx;
    border-radius: 200rpx;
  }
  .content-item-info {
    width: 518rpx;
    margin-left: 24rpx;
    position: relative;
    > p.title {
      display: flex;
      margin-top: 12rpx;
      .nickname {
        min-width: 218rpx;
        flex: 1;
        font-size: 34rpx;
        line-height: 34rpx;
        height: 34rpx;
        color: $ColorGreyScale1;
        @include text-overflow();
      }
      .time {
        margin-left: 32rpx;
        margin-top: 6rpx;
        line-height: 24rpx;
        height: 24rpx;
        font-size: 24rpx;
        color: $ColorGreyScale3;
        overflow: hidden;
        text-align: right;
      }
    }
    .label {
      margin-top: 16rpx;
      overflow: hidden;
      > i {
        @include center();
        padding: 0 8rpx;
        float: left;
        color: #ffffff;
        border-radius: 17rpx;
        line-height: 28rpx;
        height: 28rpx;
        text-align: center;
        > img {
          display: inline-block;
          width: 20rpx;
          height: 20rpx;
          margin-right: 2rpx;
        }
      }
      .sex {
        font-size: 20rpx;
      }
      .city {
        background: #9373ff;
        font-size: 18rpx;
        margin-left: 12rpx;
      }
      .logout {
        background-color: #b1b2be;
        width: 84rpx;
        height: 28rpx;
        line-height: 28rpx;
        border-radius: 17rpx;
        font-weight: 400;
        color: #ffffff;
        font-size: 20rpx;
        margin-left: 12rpx;
      }
    }
    .news-content {
      margin-top: 16rpx;
      width: 450rpx;
      height: 30rpx;
      font-size: 26rpx;
      font-weight: 400;
      color: #878a98;
      line-height: 30rpx;
      @include text-overflow();
    }
    .news-unread {
      display: block;
      position: absolute;
      right: 0;
      bottom: 9rpx;
      background: red;
      border-radius: 50rpx;
      padding: 0 8rpx;
      min-width: 32rpx;
      height: 32rpx;
      line-height: 32rpx;
      color: #ffffff;
      font-weight: 800;
      font-size: 20rpx;
      text-align: center;
    }
  }
}
.content-item:active {
  background-color: rgba(0, 0, 0, 0.05);
}
.prompt {
  width: 100%;
  height: 92rpx;
  background: #f5f0ff;
  margin: 0rpx auto;
  margin-bottom: 32rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #6f4cff;
  padding: 0 40rpx;
  border-radius: 32rpx;
  position: fixed;
  bottom: -35rpx;
  &::before {
    content: "";
    background-image: url(https://photo.zastatic.com/images/common-cms/it/20211206/1638788471854_478900_t.png);
    display: inline-block;
    width: 40rpx;
    height: 40rpx;
    background-size: cover;
    margin-right: 16rpx;
  }
}
.subscribeText {
  margin: 0 100rpx 0 8rpx;
}
.blob {
  width: 144rpx;
  height: 56rpx;
  line-height: 56rpx;
  background-color: #6f4cff;
  border-radius: 30rpx;
  color: #ffffff;
  font-weight: 800;
  font-size: 24rpx;
  text-align: center;
}
</style>

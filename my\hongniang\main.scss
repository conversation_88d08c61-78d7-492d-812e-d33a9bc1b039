.row{
    display: flex;
    flex-direction: row;
    align-items: center;
}

.flex-item {
  flex-grow: 1;
  flex-shrink: 1;
//   background-color: lightblue;
//   text-align: center;
//   padding: 10px;
}


.admin-page{
    
  

    .static{
        margin:0 35rpx;
		position: relative;
		z-index:5;
		.userInfo{
		    font-family: PingFang SC;
		    font-weight: 800;
		    font-size: 32rpx;
		    color: #333333;
		    width: 600rpx;
		    display: flex;
		    // margin: 35rpx;
		    align-items: center;
		    .avatar{
		        width: 73rpx;
		        height: 74rpx;
		        border-radius: 200rpx;
		        margin-right:20rpx;
		    }
		    .chat{
		        width: 39rpx;
		        height: 38rpx;
		        margin-left: auto;
		        margin-right: 23rpx;
		
		    }
			.nickname{
			    font-family: PingFang SC;
			    font-weight: 800;
			    font-size: 32rpx;
			    color: #FFFFFF;
			} 
			.auth{
				margin-left: 20rpx;
				width: 105rpx;
				height: 36rpx;
				background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				border-radius: 8rpx;
				font-family: <PERSON><PERSON>ang SC;
				font-weight: 500;
				font-size: 22rpx;
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: center;
				image{
					width: 26rpx;
					height: 26rpx;
				}
			           
			     }
			}
     
          
          
            .loginDay{
                margin-left: auto;
                color: #fff;
                font-family: PingFang SC;
                font-weight: 800;
                font-size: 26rpx;
                color: #FFFFFF;
                margin-bottom: 50rpx;
                .nums{
                    color: #FF6499;
                    font-size: 30rpx;
                    margin: 0 10rpx;
                }
            }
            .item{
                // margin-left: 30rpx;
                // margin-right: 50rpx;
				width: 333rpx;
				height: 260rpx;
				background: linear-gradient(0deg, #FFFFFF 42%, #EBF2FF 100%);
				border-radius: 24rpx;
				border: 1px solid #FFFFFF;
				// display: flex;
				// align-items: center;
				// justify-content: space-between;
				padding: 0 18rpx 0 29rpx;
            }
			.item3{
				width: 333rpx;
				height: 170rpx;
			}
			.item2{
				width: 333rpx;
				height: 120rpx;
			    // margin-left: 30rpx;
			    // margin-right: 50rpx;
				background: linear-gradient(0deg, #FFFFFF 42%, #FFE0EC 100%);
				border-radius: 24rpx;
				border: 1px solid #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 29rpx;
			}
			.item6{
				margin-top:20rpx;
				background: linear-gradient(0deg, #FFFFFF 42%, #EDE5F8 100%);
				width: 333rpx;
				height: 120rpx;
				border-radius: 24rpx;
				border: 1px solid #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 29rpx;
			}
            .val{
              font-family: DINPro;
              font-weight: 500;
              font-size:38rpx;
              color: #000000;
            }
			.vals{
				font-family: PingFang SC;
				font-weight: 500;
				font-size: 24rpx;
				color: #999999;
				margin-top: 10rpx;
			}
            .key{
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 26rpx;
                color: #1A1A1A;
            }
        .left,.right{
            .box1{
                height: 230rpx;
                margin-bottom: 20rpx;
                background-image: url("../static/admin/fx.png");
                background-size: 100% 100%;
                border-radius: 30rpx;
            }
            .box2{
                height: 106rpx;
            }
            .box1,.box2{
                width: 333rpx;
                border-radius: 10rpx;
                box-sizing: border-box;
                .icon{
					
                    width: 33rpx;
                    height: 33rpx;
                    margin-right: 17rpx;
                }
                .key{
                    font-family: PingFang SC;
                    font-weight: 500;
                    font-size: 26rpx;
                    color: #666666;
                }
                .val{
                    font-family: DINPro;
                    font-weight: 500;
                    font-size: 30rpx;
                    color: #000000;
                    margin-left: 50rpx;
                }

                &.b1{
                    background: #E7F0FF;
                    // background: linear-gradient(0deg, #F5F5F5 21%, #FFFFFF 100%);
                }
                &.b2{
                    background: #F1F2FF;
                }
                &.b3{
                    background: #FFEFE8;
                }
                &.b4{
                    background: #EAFFFF;
                }
            }
            .box2{
                padding: 20rpx 30rpx;
            }
           
        }
        .right{
            .box1{
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                background: #0000 !important ;
            }
        }
        .left{
            .box1{
                .key{
                    margin-left: 77rpx;
                   padding-top: 30rpx;
                }
                .val{
                    margin-left: 77rpx;
                    font-size: 41rpx;
                    margin-top: 10rpx;
                }
            }
        }
    }
    .libox{
		position: relative;
		top: 0rpx;
		left:0;
		right:0;
		z-index:5;
        .tit{
            display: flex;
            justify-content:space-between;
		    margin: 40rpx 40rpx 10rpx;
            font-family: PingFang SC;
            font-weight: bold;
            font-size: 32rpx;
            color: #333333;
            .send{
                .icon{
                    width: 60rpx;
                    height: 60rpx;
                    margin-right: 10rpx;
                }
                font-family: PingFang SC;
                font-size: 26rpx;
                color: #333333;
            }
        }
    }
}

.qxList-page{
    .header_nav{
        position: sticky;
        top: 80rpx;
        z-index: 99999;
        background-color: #fff;
        justify-content: space-around;
        .item{
			
			
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            color: #666666;
            display: flex;
            flex-direction: columns;
            align-items: center;
            flex-direction: column;
            &::after{
                content: " ";
                display: flex;
                width: 24rpx;
                height: 10rpx;
                border-radius: 3rpx;
                background-color: #fff;
                margin-top: 10rpx;
            }
            &.cur{
                font-family: PingFang SC;
                font-weight: bold;
                font-size: 32rpx;
                color: #FF6499;
                &::after{
                    background-color: #FF6499;
                }
            }
        }
		
    }
}

.changeType-page{
    
    .box{
        position: relative;
        top:500rpx;
        z-index: 999;
		.xin{
		    width: 137rpx;
		    height: 218rpx;
		    position: absolute;
		    left: 180rpx;
		    top: -60rpx;
		}
        .zi1{
            font-family: PingFang SC;
            font-weight: 800;
            font-size: 58rpx;
            color: #333333;
            margin-left: 54rpx;
        }
        .zi2{
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 28rpx;
            color: #333333;
            margin-left: 47rpx;
            margin-top: 20rpx;
        }
        .box1{
            // background-color: #ffa;
            padding: 0 30rpx ;
            width: 100%;
            margin-top: 64rpx;
            .type{
                width: 686rpx;
                height: 244rpx;
                background: #FFFFFF;
                // box-shadow: 0rpx 13rpx 38rpx 0rpx rgba(255,127,171,0.21);
                border-radius: 25rpx;
                font-family: PingFang SC;
                font-weight: bold;
                font-size: 30rpx;
                color: #333333;
				display: flex;
				align-items: center;
                padding: 0 45rpx;
				.tit{
					font-family: PingFang SC;
					font-weight: 500;
					font-size: 24rpx;
					color: #999999;
				}
            }
        }
    }
}
.changeUser-page{
   padding-bottom: 180rpx;
    .userList{
        margin-top: 90rpx;
    }
    .submit{
        width: 690rpx;
        height: 100rpx;
        background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
        border-radius: 50rpx;
        margin: 30rpx auto 0px;
        // position: sticky;
        // bottom: 0px;
        
        font-family: PingFang SC;
        font-weight: bold;
        font-size: 32rpx;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        position: fixed;
        bottom: 30rpx;
        left: 30rpx;
		z-index:9999;
        &.kong{
            position: sticky;
            opacity: 0;
        }
     }
}

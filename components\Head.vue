<template name="Head">
	<view>
		<view class="head_box" v-for="(item,index) in list" :key="item.id">
			<view>
				<block v-if="className">
					<view v-if="item.status == 0" class="padding-bottom-sm margin-bottom-sm"
						style="color: green;font-weight: bold;border-bottom: 1px solid #fafafa;">
						审核中
					</view>
					<view v-if="item.status == 1" class="padding-bottom-sm margin-bottom-sm"
						style="color: green;font-weight: bold;border-bottom: 1px solid #fafafa;">
						已发布
					</view>
					<view v-if="item.status == 2" class="padding-bottom-sm margin-bottom-sm"
						style="color: red;font-weight: bold;border-bottom: 1px solid #fafafa;">
						已拒绝
					</view>
				</block>
				<view class="head_left">
					<view class="flex align-center">
						<view class="head_img" @click="clickItem(1,index)">
							<image :src="item.avatar ? item.avatar : '/static/logo.png'"></image>
						</view>
						<view class="margin-left-sm">
							<view class="flex align-center margin-top-xs">
								<view class="head_name">{{item.userName}}</view>
								<image src="../static/images/my/rzicon.png" class="renz"></image>
							</view>
							<view class="tablin" v-if="item.userData">
								<view>{{ item.userData.age}}岁</view>
								<view class="line"></view>
								<view>{{ item.userData.userHeight}}cm</view>
								<view class="line"></view>
								<view>{{ item.userData.education }}</view>
								<view class="line" v-if="item.userData.career"></view>
								<view v-if="item.userData.career">{{ item.userData.career }}</view>
							</view>
						</view>
					</view>
					<view v-if="item.userId != userId&&tabIndex!=2">
						<view class="guanz" v-if="!className&&item.isCare==0" @click="clickItem(9,index)">关注</view>
						<view class="guanz" v-if="!className&&item.isCare==1" @click="clickItem(9,index)">取消关注</view>
					</view>
					<view v-if="item.userId != userId&&tabIndex==2">
						<!-- <view class="guanz" v-if="!className&&item.isCare==0" @click="clickItem(9,index)">关注</view> -->
						<view class="guanz" @click="clickItem(9,index)">取消关注</view>
					</view>

					<view class="flex align-center justify-end" style="flex:1"
						v-if="className&&item.status == 2||item.status == 0">
						<view v-if="item.status == 0" style="color: #AC75FE;font-size: 28upx;"
							@click="clickItem(5,index)">
							编辑
						</view>
						<view style="color:#AC75FE;font-size: 28upx;" class="margin-left-sm"
							v-if="className&&item.status == 2||item.status == 0" @tap="clickItem(4,index)">
							删除
						</view>
					</view>

				</view>

				<view class="head_conten" @tap="clickItem(0,index)">
					<text>{{item.trendsContent}}</text>
				</view>

				<view class="head_image" @tap="clickItem(0,index)" v-if="item.trendsImage">
					<view v-for="imgs in item.trendsImage" :class="item.trendsImage.length>1?'img2':'img1'">
						<image :src="imgs" mode="aspectFill" style="border-radius: 12rpx;"></image>
					</view>
					<view :class="item.trendsImage.length==2?'img2':''"></view>
				</view>

				<view class="  margin-tb" style="color: red;" v-if="item.status==2&&item.auditContent">
					拒绝理由：{{item.auditContent}}
				</view>


				<view class="flex align-center justify-between margin-top" v-if="!className&&item.status==1">
					<view class="rq">{{item.createTime}}</view>
					<view class="flex align-center">
						<view class="btnw" @click="clickItem(10,item)">红娘牵线</view>
					</view>
				</view>
			</view>

		</view>
	</view>
</template>

<script>
	export default {
		props: {
			//背景颜色
			list: {
				type: Array,
				default: []
			},
			className: {
				type: Boolean,
				default: false
			},
			userId: {
				type: [Number, String],
				default: "",
			},
			tabIndex: {
				type: [Number, String],
				default: "0",
			},
		},
		methods: {
			clickItem(ind, item) {
				this.$emit('click', {
					index: ind,
					id: item
				});
			}
		}
	}
</script>

<style lang="less">
	page {
		background: #F5F5F5
	}

	.head_box {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 30rpx;
		padding: 30rpx;

		.head_left {
			display: flex;
			align-items: center;
			justify-content: space-between;

			.head_img {
				width: 74rpx;
				height: 74rpx;
				border-radius: 50%;

				image {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}

			.head_name {
				font-family: PingFang SC;
				font-weight: bold;
				color: #1E1E1E;

			}

			.renz {
				width: 39rpx;
				height: 39rpx;
				margin-left: 10rpx;
			}

			.tablin {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;

				.line {
					width: 1rpx;
					height: 19rpx;
					// border: 1px solid #CCCCCC;
					background-color: #CCCCCC;
					margin: 0 10rpx;
				}
			}

			.guanz {
				border: 1px solid #FF7CA2;
				border-radius: 35rpx;
				font-size: 24rpx;
				font-family: PingFang SC;
				// font-weight: bold;
				color: #FF769D;
				padding: 10rpx 25rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.head_conten {
			letter-spacing: 2rpx;
			font-size: 28rpx;
			color: #000000;
			padding: 20rpx 0;
		}

		.head_image {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			.img1 {
				width: 420rpx;
				height: 320rpx;
				border-radius: 12rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.img2 {
				width: 195rpx;
				height: 195rpx;
				border-radius: 12rpx;
				margin-bottom: 20rpx;

				image {
					width: 100%;
					height: 100%;
				}
			}
		}

		.rq {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
		}

		.btna {
			width: 156rpx;
			height: 64rpx;
			background: #FFF2F6;
			border: 1px solid #FF6F9C;
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FF709D;

			image {
				width: 40rpx;
				height: 34rpx;
				margin-right: 10rpx;
			}
		}

		.btnw {
			width: 156rpx;
			height: 64rpx;
			background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
			border-radius: 10rpx;
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 30rpx;
		}

	}
</style>
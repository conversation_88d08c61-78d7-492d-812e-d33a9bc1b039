<template>
	<view class="content">
		<u-sticky>
			<me-tabs v-model="tabIndex" nameKey='title' :tabs="tabs" @change="tabChange"></me-tabs>
		</u-sticky>
		
		<view class="part2" v-if="list.length!=0">
			<Head :list="list" @click="clickItem" :userId="userId" :className="true"></Head>
		</view>
		<view class="part3" v-if="shangxianSelect != '否'" @click="bindfb">
			<image src="../../../static/images/my/dtfb.png"></image>
		</view>

		<!-- 加载更多提示 -->
		<view class="s-col is-col-24" v-if="list.length > 0">
			<load-more :status="loadingType" :contentText="contentText"></load-more>
		</view>
		<!-- 加载更多提示 -->
		<empty v-if="list.length === 0" content="暂无动态信息,快去发布吧!" show="false"></empty>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>



	</view>
</template>

<script>
	import Head from '../../../components/Head.vue'
	import empty from '../../../components/empty.vue'
	import meTabs from "@/components/mescroll-uni/me-tabs/me-tabs.vue";
	export default {
		components: {
			empty,
			Head,
			meTabs
		},
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				trendsId: '',
				shangxianSelect: '否',
				renzheng: 0,

				tabBars: [{
						name: '全部',
						id: 'hot',
						state: '-1'
					},
					{
						name: '我的关注',
						id: 'yule',
						state: '1'
					}
				],
				gambitId: 0,
				title: '',
				content: '',
				contentImg: '',
				list: [],
				followUserId: 0,
				isEnable: '否',
				scrollTop: false,
				page: 1,
				size: 10,
				loadingType: 0,
				scrollTop: false,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				tabs: [{
					title: '全部',
					status: ''
				}, {
					title: '审核中',
					status: 0
				}, {
					title: '已发布',
					status: 1
				}, {
					title: '已拒绝',
					status: 2
				}],
				tabIndex: 0,

				pagess: 0,
				userId: ''
			}
		},
		onLoad(d) {
			// #ifdef MP-WEIXIN
			this.shangxianSelect = this.$queue.getData('shangxianSelect');
			// #endif
			// #ifndef MP-WEIXIN
			this.shangxianSelect = '是';
			// #endif

			let a = this.$queue.getData("isEnable")
			if (a) {
				this.isEnable = a;
			}
		},
		onShow() {
			this.userId = this.$queue.getData('userId');
			this.page = 1
			uni.showLoading({
				title: '加载中...'
			});
			this.getList();
			// this.getRenZheng();
		},
		onPageScroll: function(e) {
			this.scrollTop = e.scrollTop > 200;
		},
		methods: {
			// 切换菜单
			tabChange(e) {
				// this.goods = []; // 置空列表,显示加载进度条
				console.log(e)
				this.page = 1;
				this.status = e
				this.getList()
			},
			getRenZheng() {
				this.$Request.get("/app/userCertification/queryInsert").then(res => {
					if (res.code == 0) {
						// 0审核中 1通过 2拒绝 
						if (res.data == null) {
							this.renzheng = 0 //未实名
						} else if (res.data.status == 0) {
							this.renzheng = 1 //审核中
						} else if (res.data.status == 1) {
							this.renzheng = 2 //已实名
						} else if (res.data.status == 2) {
							this.renzheng = 3 //已拒绝
						}
					}
				});
			},
			//确认
			meHandleBtn() {
				let that = this
				if (this.meIndex == 'm1') {
					that.$queue.showLoading('删除中...');
					that.$Request.getT('/app/trends/deleteTrends?trendsId=' + that.trendsId).then(res => {
						if (res.code == 0) {
							uni.hideLoading();
							that.page = 1;
							that.getList();
						} else {
							uni.hideLoading();
							that.$queue.showToast(res.msg);
						}
					});
				}
				if (this.meIndex == 'm2') {
					uni.navigateTo({
						url: "/my/renzheng/index"
					});
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			deleteSave(trendsId) {
				let that = this;
				this.meShowModel = true
				this.meTitle = '删除提示'
				this.meContent = '您确认要删除当前动态吗？'
				this.meIndex = 'm1'
				this.trendsId = trendsId
			},
			topScrollTap: function() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				});
			},
			tabClick(e) {
				this.tabIndex = e;
				this.page = 1;
				this.getList();
			},
			clickItem: function(options) {
				console.log(options)
				if (options.index == 0) {
					uni.navigateTo({
						url: '/package/pages/detail/listDetails?trendsId=' + this.list[options.id].trendsId
					});
				} else if (options.index == 1) {
					let token = this.$queue.getData('token');
					if (token) {
						//去关注页面
						uni.navigateTo({
							url: '/my/gird/guanzhuDetail?userId=' + this.list[options.id].userId
						});

					} else {
						this.goLoginInfo();
					}
				} else if (options.index == 2) {
					// this.getMessageList2();
				} else if (options.index == 3) {
					this.saveLove(this.list[options.id].trendsId);
				} else if (options.index == 4) {
					this.deleteSave(this.list[options.id].trendsId);
				} else if (options.index == 5) {
					uni.navigateTo({
						url: '/package/pages/releaseone/releaseone?trendsId=' + this.list[options.id].trendsId
					})
				}
			},
			//统一登录跳转
			goLoginInfo() {
				uni.navigateTo({
					url: '/pages/public/login'
				});
			},
			saveLove(trendsId) {
				// if (this.renzheng != 2) {
				// 	this.meShowModel = true
				// 	this.meTitle = '温馨提示'
				// 	this.meContent = '您还未认证陪玩官,请先认证'
				// 	this.meIndex = 'm2'
				// 	return;
				// }
				let userId = this.$queue.getData('userId');
				let data = {
					trendsId: trendsId,
					byUserId: userId
				}
				this.$Request.postT('/app/trendsLike/saveTrendsLike', data).then(res => {
					if (res.code == 0) {
						this.page = 1;
						this.getList();
					} else {
						this.$queue.showToast(res.msg);
					}
				});
			},
			getList() {
				this.loadingType = 1;
				let userId = this.$queue.getData('userId');
				let status = this.tabs[this.tabIndex].status
				this.$Request.getT('/app/trends/getMyTrendsList?page=' + this.page + '&limit=' + this.size + '&status=' +
					status, {
						releaseType: 1 //发布类型 1用户发布 2红娘发布
					}).then(res => {
					uni.hideLoading();
					uni.stopPullDownRefresh()
					if (res.code == 0) {
						this.pagess = res.data.pages
						if (this.page == 1 || res.data) {
							this.list = [];
						}
						res.data.records.forEach(d => {
							if (d.trendsImage) {
								d.trendsImage = d.trendsImage.split(',');
							}
							this.list.push(d);
						})
						if (res.data.records.length === this.size) {
							this.loadingType = 0;
						} else {
							this.loadingType = 3;
						}
					} else {
						this.loadingType = 2;
					}

				});
			},
			bindfb() {
				let token = this.$queue.getData('token');
				if (token) {
					// if (this.renzheng != 2) {
					// 	this.meShowModel = true
					// 	this.meTitle = '温馨提示'
					// 	this.meContent = '您还未认证陪玩官,请先认证'
					// 	this.meIndex = 'm2'
					// 	this.maskcloseAble = false
					// 	return;
					// }
					uni.navigateTo({
						url: '/package/pages/releaseone/releaseone'
					})
				} else {
					this.goLoginInfo();
				}
			}
		},
		onReachBottom: function() {
			if (this.page < this.pagess) {
				this.page = this.page + 1;
				this.getList();
			}

		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getList();
		}
	}
</script>

<style lang="less">
	@import '../../../static/less/index.less';
	@import '../../../static/css/index.css';

	.content {
		width: 100%;
	}

	.part3 {
		position: fixed;
		bottom: 150rpx;
		right: 30rpx;
		z-index: 999;
	}

	.part3 image {
		width: 133rpx;
		height: 133rpx;
	}


	.part2 {
		// margin-top: 120upx;
	}
</style>
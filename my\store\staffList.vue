<template>
	<view class="container">
		<!-- 添加员工按钮 -->
		<view class="add-staff" @click="showAddStaffModal">+</view>

		<!-- 员工列表 -->
		<view class="staff-list">
			<view class="staff-item" v-for="(staff, index) in staffList" :key="index">
				<view class="staff-avatar">
					<image :src="staff.avatar" mode="aspectFill"></image>
				</view>
				<view class="staff-info">
					<view class="staff-name">{{ staff.name }}</view>
					<view class="staff-join-time">加入时间：{{ staff.joinTime }}</view>
				</view>
			</view>
		</view>

		<!-- 添加员工弹窗 -->
		<u-modal v-model="showModal" :title="modalTitle" :show-cancel-button="true"
			@cancel="handleCancel" @confirm="handleConfirm" :confirm-text="'确定'"
			:cancel-text="'取消'">
			<view class="modal-content">
				<view class="form-item">
					<view class="form-label">姓名：</view>
					<input class="form-input" v-model="newStaff.name" placeholder="请输入姓名" />
				</view>
				<view class="form-item">
					<view class="form-label">手机号：</view>
					<input class="form-input" v-model="newStaff.phone" placeholder="请输入手机号" type="number" />
				</view>
			</view>
		</u-modal>
	</view>
</template>

<script>
export default {
	data() {
		return {
			storeId: '',
			storeName: '',
			showModal: false,
			modalTitle: '添加员工',
			newStaff: {
				name: '',
				phone: ''
			},
			staffList: [
				{
					name: '李雅婷',
					phone: '13812345678',
					joinTime: '2024-08-15',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415594653_449462_t.jpg'
				},
				{
					name: '张美琳',
					phone: '13923456789',
					joinTime: '2024-09-22',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415602977_80839_t.jpg'
				},
				{
					name: '陈思雨',
					phone: '13734567890',
					joinTime: '2024-10-08',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415612687_602843_t.jpg'
				},
				{
					name: '王欣怡',
					phone: '13645678901',
					joinTime: '2024-11-12',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415616821_498235_t.jpg'
				},
				{
					name: '刘晓敏',
					phone: '13556789012',
					joinTime: '2024-12-03',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415623240_5599_t.jpg'
				},
				{
					name: '赵婉儿',
					phone: '13467890123',
					joinTime: '2025-01-18',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415627344_989576_t.jpg'
				},
				{
					name: '孙丽娜',
					phone: '13378901234',
					joinTime: '2025-02-25',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415645556_264714_t.jpg'
				},
				{
					name: '周雨萱',
					phone: '13289012345',
					joinTime: '2025-03-14',
					avatar: 'https://photo.zastatic.com/images/common-cms/it/20250528/1748415650672_879266_t.jpg'
				}
			]
		}
	},
	onLoad(options) {
		// 获取传递的门店信息
		if (options.storeId) {
			this.storeId = options.storeId
			this.storeName = options.storeName
		}
		this.loadStaffData()
	},
	methods: {
		loadStaffData() {
			// 这里可以调用API获取员工数据
			// this.$Request.get("/app/store/staff", {
			//     storeId: this.storeId
			// }).then(res => {
			//     if (res.code == 0) {
			//         this.staffList = res.data.list
			//     }
			// });
		},
		showAddStaffModal() {
			this.showModal = true
			this.newStaff = {
				name: '',
				phone: ''
			}
		},
		handleCancel() {
			this.showModal = false
			this.newStaff = {
				name: '',
				phone: ''
			}
		},
		handleConfirm() {
			// 验证输入
			if (!this.newStaff.name.trim()) {
				uni.showToast({
					title: '请输入姓名',
					icon: 'none'
				})
				return
			}
			if (!this.newStaff.phone.trim()) {
				uni.showToast({
					title: '请输入手机号',
					icon: 'none'
				})
				return
			}

			// 验证手机号格式
			const phoneReg = /^1[3-9]\d{9}$/
			if (!phoneReg.test(this.newStaff.phone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				})
				return
			}

			// 添加员工到列表
			const newStaffMember = {
				name: this.newStaff.name,
				phone: this.newStaff.phone,
				joinTime: this.getCurrentDate(),
				avatar: this.getRandomAvatar()
			}

			this.staffList.unshift(newStaffMember)

			// 这里可以调用API保存员工数据
			// this.$Request.post("/app/store/addStaff", {
			//     storeId: this.storeId,
			//     name: this.newStaff.name,
			//     phone: this.newStaff.phone
			// }).then(res => {
			//     if (res.code == 0) {
			//         uni.showToast({
			//             title: '保存成功',
			//             icon: 'success'
			//         })
			//         this.loadStaffData()
			//     }
			// });

			// 关闭弹窗并显示成功提示
			this.showModal = false
			uni.showToast({
				title: '保存成功',
				icon: 'success'
			})

			// 清空表单
			this.newStaff = {
				name: '',
				phone: ''
			}
		},
		getCurrentDate() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			return `${year}-${month}-${day}`
		},
		getRandomAvatar() {
			// 生成23-50之间的随机数字，避免与现有员工头像重复
			const randomNum = Math.floor(Math.random() * 28) + 23
			return `https://randomuser.me/api/portraits/women/${randomNum}.jpg`
		}
	}
}
</script>

<style scoped>
page {
	background: #f5f5f5;
}

.container {
	min-height: 100vh;
	padding: 20rpx;
}

/* .add-staff {
	background: linear-gradient(90deg, #FF6B9D, #FF8FA3);
	color: #FFFFFF;
	font-size: 32rpx;
	font-weight: bold;
	height: 80rpx;
	border-radius: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 8rpx rgba(255, 107, 157, 0.3);
} */

.staff-list {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}
.add-staff {
	position: fixed;
	top: 85%;
	right: 32rpx;
	z-index: 10000;
	/*border: 1px solid #dddddd;*/
	width: 100rpx;
	height: 100rpx;
	border-radius: 50%;
	text-align: center;
	line-height: 100rpx;
	font-size: 48rpx;
	color:#ffffff;
	background: #dd6161;
}
.staff-item {
	background: #FFFFFF;
	border-radius: 12rpx;
	padding: 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.staff-avatar {
	width: 80rpx;
	height: 80rpx;
	margin-right: 30rpx;
}

.staff-avatar image {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.staff-info {
	flex: 1;
}

.staff-name {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 8rpx;
}

.staff-join-time {
	font-size: 28rpx;
	color: #666666;
}

.modal-content {
	padding: 30rpx;
}

.form-item {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.form-label {
	width: 120rpx;
	font-size: 28rpx;
	color: #333333;
}

.form-input {
	flex: 1;
	height: 80rpx;
	padding: 0 20rpx;
	border: 2rpx solid #e0e0e0;
	border-radius: 8rpx;
	font-size: 28rpx;
	background: #FFFFFF;
}

.form-input:focus {
	border-color: #FF6B9D;
}
</style>

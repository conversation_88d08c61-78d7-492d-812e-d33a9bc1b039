<template>
	<view>
		<view class="actbxo" v-for="(item,index) in list" :key="index" @click.stop="goDetail(item.activityId)">
			<view class="imgbox">
				<image :src="item.titleImg" mode="aspectFill"></image>
			</view>
			<view class="margin-left-sm flex-sub">
				<view class="flex align-center">
					<!-- "status": 1, //状态 1报名中 2已满员 3已结束 4报名已截止-->
					<view class="atcend" v-if="item.status==1">报名中</view>
					<view class="atcends" v-if="item.status==2">已满员</view>
					<view class="atcends" v-if="item.status==3">已结束</view>
					<view class="atcends" v-if="item.status==4">报名已截止</view>
					<view class="title text-cut">{{item.title }}</view>
				</view>
				<view class="datra">活动时间：{{ item.startTime.split(" ")[0] }}</view>

				<view class="flex margin-top-xl align-center justify-between ">
					<view class="flex  align-center ">
						<view class="text-cut flex align-center" style="max-width: 300rpx;"
							v-if="item.signUserList&&item.signUserList.length!=0">
							<view v-for="(ite,ind) in item.signUserList" :key="ind"
								style="width: 50rpx;height: 50rpx;border-radius: 50rpx;"
								:style="ind!=0?'margin-left: -20rpx;':''">
								<image :src="ite.avatar?ite.avatar:'../../static/logo.png'"
									style="width: 50rpx;height: 50rpx;border-radius: 50rpx;margin-left: 0rpx;">
								</image>
							</view>
						</view>
						<view v-else>
							<image src="../static/admin/moren.png" style="width: 50rpx;height: 50rpx;"></image>
						</view>
						<view class="margin-left-xs text-26" style="color: #999999;">
							{{item.signCount}}人已报名
						</view>
					</view>
					<view class="btn" @click.stop="endHd(item)" v-if="item.status==4">结束活动</view>
				</view>
			</view>
		</view>

		<view class="s-col is-col-24" v-if="list.length > 0">
			<load-more :status="loadingType" :contentText="contentText"></load-more>
		</view>

		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key

				list: [

				],
				page: 1,
				loadingType: 0,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				activityId: ''
			}
		},
		onLoad() {

		},


		// 页面周期函数--监听页面初次渲染完成
		onReady() {},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {
			this.getEventList()
		},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {
			this.page = 1
			this.getEventList()
		},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {
			if (this.loadingType == 0) {
				this.page += 1
				this.getEventList()
			}
		},
		// 页面处理函数--监听页面滚动(not-nvue)
		// onPageScroll(event) {},
		// 页面处理函数--用户点击右上角分享
		// onShareAppMessage(options) {},
		methods: {
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm1') {
					uni.showLoading({
						title: '结束报名中...',
						icon: 'none'
					})
					that.$Request.getT('/app/matchActivity/matchCloseActivity', {
						activityId: that.activityId
					}).then(res => {
						uni.hideLoading();
						if (res.code == 0) {
							uni.showToast({
								title: '结束成功',
								icon: 'none'
							})
							that.page = 1
							that.getEventList()

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					});
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (that.meIndex == 'm1') {

				}
			},
			endHd(item) {
				this.activityId = item.activityId
				this.meShowModel = true
				this.meTitle = '提示'
				this.meContent = '活动结束后不能重新开始，确定结束活动？'
				this.meIndex = 'm1'
				this.meShowCancel = true
			},
			goDetail(id) {
				uni.navigateTo({
					url: '/my/Events/detail?id=' + id + '&isHn=2'
				})
			},
			getEventList() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				this.$Request.get('/app/matchActivity/getMatcherSignActivityList', {
					page: this.page,
					limit: 10,
				}).then(res => {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.list = this.page == 1 ? res.data.records : [
							...this.list,
							...res.data.records
						]
						if (res.data.pages > res.data.current) {
							this.loadingType = 0
						} else {
							this.loadingType = 2
						}
					}
				});
			}
		}
	}
</script>

<style lang="less">
	page {
		// background: #F2F2F2;
		background: #FAFDFF;
	}

	.actbxo {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx;
		padding: 21rpx;
		display: flex;

		.imgbox {

			image {
				width: 260rpx;
				height: 201rpx;
				border-radius: 18rpx;
			}

		}

		.atcend {
			// width: 80rpx;
			// height: 30rpx;
			padding: 0 6rpx;
			background: #FF71A1;
			border-radius: 4rpx;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 22rpx;
			color: #FAFDFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.atcends {
			// width: 80rpx;
			// height: 30rpx;
			padding: 0 6rpx;
			background: #878CA2;
			border-radius: 4rpx;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 22rpx;
			color: #FAFDFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.title {
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #111224;
			margin-left: 10rpx;
			max-width: 210rpx;
		}

		.datra {
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-top: 10rpx;
		}
	}

	.btn {
		background: #FF71A1;
		color: #FFFFFF;
		border-radius: 50rpx;
		padding: 8rpx 20rpx;

	}
</style>
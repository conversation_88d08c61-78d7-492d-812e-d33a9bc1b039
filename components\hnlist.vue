<template>
	<view>


		<view class="listbox" v-for="(item,index) in list" :key="index" @click.stop="clickItem(0,item)">
			<view class="iteimg">
				<image :src="item.userImg?item.userImg[0]:'../static/logo.png'" mode="" alt="头像">
				</image>
			</view>
			<view class="rightcont">
				<view class="usrbname">{{ item.realName || "满目星辰" }}
					<image src="../static/images/my/rzicon.png"></image>
				</view>
				<view class="labl">
					<view class="sexicon" v-if="item.sex==1">
						<u-icon name="man" color="#FFFFFF"></u-icon>
						{{item.age}}岁
					</view>
					<view class="sexicons" v-if="item.sex==2">
						<u-icon name="woman" color="#FFFFFF"></u-icon>
						{{item.age}}岁
					</view>
					<view class="">
						{{ item.locationCity }}/{{ item.locationCounty }}
						<!-- 西安/新城区 -->
					</view>
				</view>
				<view class="tit">{{ item.feelingAngle || "阿萨" }}</view>
				<!-- <view class="tit">{{ item.idealAspect||"" }}</view> -->
			</view>
			<view class="btns" @click.stop="clickItem(1,item)">联系红娘</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			//列表
			list: {
				type: Array,
				default: []
			},

		},
		methods: {
			clickItem(ind, item) {
				this.$emit('click', {
					index: ind,
					id: item
				});
			}
		}
	}
</script>

<style lang="less">
	.listbox {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx;
		padding: 20rpx;
		display: flex;
		align-items: center;

		.iteimg {
			width: 200rpx;
			height: 200rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}
		}

		.rightcont {
			flex: 1;
			// height: 200rpx;
			// display: flex;
			// align-items: center;
			// flex-wrap: wrap;
			// align-content: space-between;
			margin-left: 18rpx;

			.usrbname {
				display: flex;
				align-items: center;

				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #292929;

				image {
					width: 40rpx;
					height: 40rpx;
					margin-left: 10rpx;
				}
			}

			.labl {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
				margin: 20rpx 0;

				.sexicon {
					background: #38CAFF;
					border-radius: 10rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					padding: 6rpx 10rpx;
					margin-right: 10rpx;
				}

				.sexicons {
					background: #edbef3;
					border-radius: 10rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					padding: 6rpx 10rpx;
					margin-right: 10rpx;
				}
			}

			.tit {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
			}
		}

		.btns {
			width: 163rpx;
			height: 69rpx;
			background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
			border-radius: 10rpx;
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;

		}
	}
</style>
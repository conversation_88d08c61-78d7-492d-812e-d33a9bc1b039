<template>
	<view>
		<view class="actbxo" v-for="(item,index) in list" :key="index" @click="goDetail(item.activityId)">
			<view class="imgbox">
				<image :src="item.titleImg" mode="aspectFill"></image>
			</view>
			<view class="margin-left-sm">
				<view class="flex align-center">
					<!-- "status": 1, //状态 1报名中 2已满员 3已结束 4报名已截止-->
					<view class="atcend" v-if="item.status==1">报名中</view>
					<view class="atcends" v-if="item.status==2">已满员</view>
					<view class="atcends" v-if="item.status==3">已结束</view>
					<view class="atcends" v-if="item.status==4">报名已截止</view>
					<view class="title text-cut">{{item.title }}</view>
				</view>
				<view class="datra">活动时间：{{ item.startTime.split(" ")[0] }}</view>

				<view class="flex margin-top-xl align-center  padding-right-sm">
					<view class="text-cut flex align-center" style="max-width: 300rpx;"
						v-if="item.signUserList&&item.signUserList.length!=0">
						<view v-for="(ite,ind) in item.signUserList" :key="ind"
							style="width: 50rpx;height: 50rpx;border-radius: 50rpx;"
							:style="ind!=0?'margin-left: -20rpx;':''">
							<image :src="ite.avatar?ite.avatar:'../../static/logo.png'"
								style="width: 50rpx;height: 50rpx;border-radius: 50rpx;margin-left: 0rpx;">
							</image>
						</view>
					</view>
					<view v-else>
						<image src="../static/admin/moren.png" style="width: 50rpx;height: 50rpx;"></image>
					</view>
					<view class="margin-left-xs  text-26" style="color: #999999;">
						{{item.signCount}}人已报名
					</view>
				</view>
			</view>
		</view>

		<empty v-if="list.length==0"></empty>
		<view class="s-col is-col-24" v-if="list.length > 0">
			<load-more :status="loadingType" :contentText="contentText"></load-more>
		</view>
	</view>
</template>

<script>
	import empty from '@/components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				list: [

				],
				page: 1,
				loadingType: 0,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
			}
		},
		onLoad() {

		},


		// 页面周期函数--监听页面初次渲染完成
		onReady() {},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {
			this.getEventList()
		},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {
			this.page = 1
			this.getEventList()
		},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {
			if (this.loadingType == 0) {
				this.page += 1
				this.getEventList()
			}
		},
		// 页面处理函数--监听页面滚动(not-nvue)
		// onPageScroll(event) {},
		// 页面处理函数--用户点击右上角分享
		// onShareAppMessage(options) {},
		methods: {

			goDetail(id) {
				uni.navigateTo({
					url: '/my/Events/detail?id=' + id + '&isHn=1'
				})
			},
			getEventList() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				this.$Request.get('/app/matchActivity/getUserMatchActivityList', {
					page: this.page,
					limit: 10,
					regionsProvince: ""
				}).then(res => {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						// 
						res.data.records.map((ite, index) => {
							if (ite.signUserList.length > 5) {
								ite.signUserList = ite.signUserList.splic(0, 4)
							}
						})
						this.list = this.page == 1 ? res.data.records : [
							...this.list,
							...res.data.records
						]
						if (res.data.pages > res.data.current) {
							this.loadingType = 0
						} else {
							this.loadingType = 2
						}
					}
				});
			}
		}
	}
</script>

<style lang="less">
	page {
		// background: #F2F2F2;
		background: #FAFDFF;
	}

	.actbxo {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx;
		padding: 21rpx;
		display: flex;

		.imgbox {

			image {
				width: 260rpx;
				height: 201rpx;
				border-radius: 18rpx;
			}

		}

		.atcend {
			width: 80rpx;
			height: 30rpx;
			background: #FF71A1;
			border-radius: 4rpx;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 22rpx;
			color: #FAFDFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.atcends {
			width: 80rpx;
			height: 30rpx;
			background: #878CA2;
			border-radius: 4rpx;
			font-family: PingFang SC;
			font-weight: 500;
			font-size: 22rpx;
			color: #FAFDFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.title {
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #111224;
			margin-left: 10rpx;
			width: 300rpx;
		}

		.datra {
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-top: 10rpx;
		}
	}
</style>
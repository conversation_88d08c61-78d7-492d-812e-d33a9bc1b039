<template>
	<view style="padding-bottom: 120rpx;">
		<view class="banerbox">
			<swiper indicator-dots autoplay circular>
				<swiper-item v-for="(item, index) in info.titleCarousel" :key="index">
					<image :src="item" mode="aspectFill"></image>
				</swiper-item>
			</swiper>

		</view>

		<view class="Imagebox">
			<view class="padding-lr-sm padding-tb">
				<view class="flex align-center justify-between">
					<view class="tite">{{info.title }}</view>
					<view v-if="info.money!=0">
						<text style="color: #FF71A1;">￥{{info.money}}</text>
					</view>
					<view style="color: #FF71A1;" v-else>免费</view>
				</view>
				<view class="actdata margin-top-xs">
					活动开始时间:{{ info.startTime }}
				</view>
				<view class="actdata margin-top-xs">
					活动结束时间:{{ info.endTime }}
				</view>
				<view class="actdata margin-top-xs">
					报名截止时间:{{ info.signTime }}
				</view>

				<view style="background: #EEEEEE;width: 100%;height: 1rpx;margin: 20rpx 0;"></view>
				<view class="flex align-center justify-between margin-tb-sm" v-if="isHn!=2">
					<view class="tx" v-if="info.signUserList&&info.signUserList.length!=0">
						<view class="hdman" v-for="(item,index) in info.signUserList" :key="index"
							:style="index!=0?'margin-left:-30rpx;':''">
							<image :src="item.avatar?item.avatar:'../../static/logo.png'"></image>
						</view>
					</view>
					<view v-else>
						<image src="../static/admin/moren.png" style="width: 50rpx;height: 50rpx;"></image>
					</view>
					<view style="color: #A29C9C;" class="text-26">{{ info.signCount}}人已报名</view>
				</view>
				<view class="flex align-center justify-between" @click="openMaps">
					<view>活动地址</view>
					<view class="flex align-center">
						<view class="text-26 text-cut">{{info.activityAddress }}</view>
						<image src="../../static/images/hn/add.png"
							style="width: 22rpx;height: 32rpx;margin-left: 12rpx;"></image>
					</view>
				</view>
			</view>
		</view>
		<!-- <view class="bmbox">
			<view class="flex align-center justify-between">
				<view class="name">当前报名</view>
				<view class="righttit">
					<image src="../static/hot.png"></image>
					{{ info.signCount}}人已报名
				</view>
			</view>
			<view class="tx">
				<view class="hdman" v-for="(item,index) in info.signUserList" :key="index"
					:style="index!=0?'margin-left:-30rpx;':''">
					<image :src="item.avatar ||'../../static/logo.png'"></image>
				</view>
			</view>
		</view> -->

		<view class="hdsm" v-if="isHn==2&&info.signUserList&&info.signUserList.length!=0">
			<view class="flex align-center justify-between">
				<view class="flex align-center ">
					<view>当前报名</view>
					<view style="color: #A29C9C;" class="text-26 margin-left-xs">
						<text style="color: #FF71A1;">{{ info.signCount}}人</text>已报名
					</view>
				</view>
				<view style="color: #666666;" class="text-sm" @click="gobmDetail()">查看全部</view>
			</view>
			<view class="margin-top-sm" v-for="(item,index) in info.signUserList" :key="index">
				<view class="flex align-center">
					<image :src="item.avatar?item.avatar:'../../static/logo.png'"
						style="width: 70rpx;height:70rpx;border-radius: 50%;">
					</image>
					<view class="margin-left-xs flex align-center justify-between" style="width: 89%;">
						<view>
							<view>{{item.userName}}</view>
							<view style="color: #666666;font-size: 22rpx;">报名时间:{{item.createTime}}</view>
						</view>
						<view @click="callPhone(item.phone)">
							<image src="../../my/static/phones.png" style="width: 40rpx;height: 38rpx;"></image>
						</view>
					</view>
				</view>
			</view>
		</view>


		<view class="hdsm">
			<view class="name">现场照片</view>
			<view class="deImg" v-if="!isMore">
				<block v-for="(item,index) in info.activityPhoto" :key="index">
					<image @click="preview(info.activityPhoto,index)" :src="item" v-if="index==0||index==1"
						mode="widthFix">
					</image>
				</block>
			</view>
			<view class="deImg" v-if="isMore">
				<block v-for="(item, index) in info.activityPhoto" :key="index">
					<image @click="preview(info.activityPhoto, index)" :src="item" mode="widthFix"></image>
				</block>
			</view>
			<view class="moretit" v-if="!isMore" @click="isMore = true">查看更多</view>
			<view class="moretit" v-if="isMore" @click="isMore = false">隐藏收起</view>
		</view>

		<view class="hdsm">
			<view class="name">活动说明</view>
			<view class="xtit">活动对象</view>
			<view class="margin-top-sm">
				{{ info.activityObject }}
			</view>
			<view class="xtit">活动费用</view>
			<view class="margin-top-sm">
				{{ info.activityMoneyDetail }}
			</view>
		</view>

		<view class="taber" v-if="isHn!=2">
			<view class="flex align-center" style="flex:1;">
				<view class="tabImg" @click="callPhone(info.phone)">
					<image src="../static/kefu.png"></image>
					<view>咨询</view>
				</view>
				<view class="tabImg" @click="showShare = true">
					<image src="../static/fenxiang.png"></image>
					<view>分享</view>
				</view>
			</view>
			<block v-if="info.isSign == 2">
				<view class="bmbtns no" v-if="info.status==1" @click="cancelbm">取消报名</view>
				<view class="bmbtns" v-if="info.status==2">已满员</view>
				<view class="bmbtns" v-if="info.status==3">活动已结束</view>
				<view class="bmbtns" v-if="info.status==4">报名已截止</view>
				<!-- <view class="bmbtn no">您已报名</view> -->
			</block>
			<block v-else>
				<view class="bmbtn" v-if="info.status==1" @click="joinActive">立即报名</view>
				<view class="bmbtn" v-if="info.status==2">已满员</view>
				<view class="bmbtn" v-if="info.status==3">活动已结束</view>
				<view class="bmbtns" v-if="info.status==4">报名已截止</view>
			</block>


		</view>

		<view class="taber" v-if="isHn==2&&info.status==4">
			<view class="jieshu" @click="endHd()">结束活动</view>
		</view>
		<!-- <u-popup :show="showShare" @close="showShare = false" mode="bottom" round="24">
			
		</u-popup> -->
		<u-popup v-model="showShare" mode="bottom" border-radius="24">
			<view class="" style="padding:35rpx 40rpx 45rpx 40rpx;">
				<view class="text-38 text-bold text-center">分享好友</view>

				<view class="flex align-center justify-between margin-top">
					<!-- #ifndef MP-WEIXIN -->
					<view @click="share()" class="flex-sub margin-right-sm padding-tb-sm text-center"
						style="background-color: #FFCB49;color: #FFFFFF;border-radius: 16rpx;">
						文案推广
					</view>
					<!-- #endif -->

					<!-- #ifdef MP-WEIXIN -->
					<button open-type="share" class="flex-sub margin-right-sm padding-tb-sm text-center"
						style="background-color: #FFCB49;color: #FFFFFF;border-radius: 16rpx;">
						文案推广
					</button>
					<!-- #endif -->

					<view @tap="showModal()" class="flex-sub margin-left-sm padding-tb-sm text-center"
						style="background-color: #557EFD;color: #FFFFFF;border-radius: 16rpx;">
						生成海报
					</view>
				</view>
			</view>
		</u-popup>

		<u-popup v-model="show" mode="bottom" border-radius="24">
			<view class="padding">
				<view class="text-center text-bold text-lg">支付方式</view>
				<view class="flex align-center justify-between" style="height: 100upx;padding: 30upx;"
					v-for="(item, index) in openLists" :key='index'>
					<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;"></image>
					<view style="font-size: 30upx;margin-left:0upx;width: 70%;">{{ item.text }}
					</view>
					<radio-group name="openWay" style="margin-left: 20upx;" @tap='selectWay(item)'>
						<label class="tui-radio">
							<radio color="#FF5974" :checked="openWay === item.id ? true : false" />
						</label>
					</radio-group>
				</view>

				<view class="btn margin-top" @click="pay">立即支付</view>
			</view>
		</u-popup>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>

		<!-- #ifndef MP-WEIXIN -->
		<tki-qrcode ref="qrcode" :val="urls" :size="200" background="#fff" foreground="#000" pdground="#000"
			:onval="true" :loadMake="true" @result="qrR" :show="false"></tki-qrcode>
		<view class="cu-modal" :class="modalName == 'Image' ? 'show' : ''" @tap="hideModal">
			<view class="cu-dialog" v-if="backgroundImage && erweimapath && haibaoShow" @tap="hideModal">
				<view class="bg-img">
					<wm-poster @success="posterSuccess" :imgSrc="backgroundImage" :Referrer="'我的邀请码:'+invitationCode"
						:QrSrc="erweimapath" :Title="tuiguang" :LineType="false"></wm-poster>
				</view>
			</view>
		</view>
		<!-- #endif -->

		<!-- #ifdef MP-WEIXIN -->
		<view @tap="hideModal" :class="modalName == 'Image' ? 'show' : ''" class="modal"
			style="text-align: center;display: flex;justify-content: center;">
			<view style="width:100%;margin: auto;">
				<image :src="h5SaveImg" mode="widthFix" style="width: 90%;"></image>
			</view>
		</view>
		<canvas canvas-id="poster" class="poster_canvas"></canvas>
		<!-- #endif -->

	</view>
</template>

<script>
	import configdata from '@/common/config.js';
	import wmPoster from '@/components/wm-poster/wm-posterorders.vue';
	export default {
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key

				list: [],
				isMore: false,
				info: {},
				show: false,
				openLists: [],
				openWay: "",
				mymoney: 0,
				SelKeFu: '',
				showShare: false,
				id: '',
				backgroundImage: '',
				tuiguang: '',
				tuiguang1: '',
				urls: '',
				erweimapath: '',
				poster: {},
				qrShow: false,
				haibaoImg: null,
				haibaoShow: false,
				modalName: '',
				invitationCode: '',
				isHn: ''
			}
		},
		onLoad(e) {
			if (e.isHn) {
				this.isHn = e.isHn
			}
			if (e.id) {
				this.id = e.id
				this.getEventInfo()
			}
			this.initPayInfo()
			// #ifdef MP-WEIXIN
			if (e.scene) {
				const scene = decodeURIComponent(e.scene);
				that.$queue.setData('inviterCode', scene.split(',')[0]);
			}
			// #endif

			// 获取邀请码保存到本地
			if (e.invitation) {
				that.$queue.setData('inviterCode', e.invitation);
			}


			this.invitationCode = this.$queue.getData('invitationCode');
			// #ifdef H5
			this.$Request.getT('/app/common/type/141').then(res => { //H5是否分享app页面
				if (res.code === 0) {
					if (res.data && res.data.value && res.data.value == '是') {
						this.$Request.getT('/app/common/type/25').then(ress => { //app下载地址
							if (ress.code === 0) {
								if (ress.data && ress.data.value) {
									this.urls = ress.data.value;
								}
							}
						});
					} else {
						this.urls = this.$queue.publicYuMing() + '/my/Events/detail?invitation=' + this
							.invitationCode + '&id=' + this.id;
					}
				}
			});

			//#endif
		},
		onShow() {
			this.SelKeFu = this.$queue.getData('SelKeFu');
			this.userId = uni.getStorageSync('userId')
			if (this.userId) {
				this.getUserInfo()
			}

		},
		onPullDownRefresh() {
			this.getEventInfo()
			uni.stopPullDownRefresh();
		},
		onShareAppMessage(res) {
			return {
				path: '/my/Events/detail?invitation=' + this.invitationCode + '&id=' + this
					.id, //这是为了传参   onload(data){let id=data.id;} 
				title: this.tuiguang,
				imageUrl: this.bgImg
			}
		},
		/*
		 * uniapp微信小程序分享页面到微信朋友圈
		 */
		onShareTimeline(res) {
			return {
				path: '/my/Events/detail?invitation=' + this.invitationCode + '&id=' + this
					.id, //这是为了传参   onload(data){let id=data.id;} 
				title: this.tuiguang,
				imageUrl: this.bgImg
			}
		},
		methods: {
			endHd() {
				this.meShowModel = true
				this.meTitle = '提示'
				this.meContent = '活动结束后不能重新开始，确定结束活动？'
				this.meIndex = 'm56'
				this.meShowCancel = true
			},
			openMaps() {
				let that = this
				uni.openLocation({
					latitude: Number(that.info.activityLat) - 0,
					longitude: Number(that.info.activityLng) - 0,
					name: that.info.activityAddress,
					// address: that.info.activityAddress,
					success: function() {
						console.log('success');
					}
				});
			},
			cancelbm(item) { //取消报名
				this.meShowModel = true
				this.meTitle = '提示'
				this.meContent = '取消报名后不能再次报名，确定取消报名吗？'
				this.meIndex = 'm66'
				this.meShowCancel = true
			},
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone //仅为示例
				});
			},
			gobmDetail() {
				let token = this.$queue.getData('token');
				if (token) {
					uni.navigateTo({
						url: '/my/Events/bmlist?activityId=' + this.info.activityId
					})
				} else {
					uni.navigateTo({
						url: '/pages/public/login'
					})
				}

			},
			posterSuccess(haibaoImg) {
				this.haibaoImg = haibaoImg;
				this.modalName = 'Image';
			},
			showModal() {
				this.showShare = false
				if (!this.haibaoImg) {
					this.haibaoShow = true;
					this.$queue.showLoading('海报生成中...');
				} else {
					this.modalName = 'Image';
				}
			},
			hideModal() {
				this.modalName = null;
			},
			qrR(path) {
				this.erweimapath = path;
			},
			share() {
				this.showShare = false
				this.sharurl();
			},
			sharurl() {
				let that = this;
				let relationId = this.invitationCode;
				this.meShowModel = true
				this.meTitle = '文案推广'
				this.meContent = this.tuiguang1 + relationId + '\n' + this.urls
				this.meIndex = 'm1'
				this.meShowCancel = true
				this.meCancelText = '关闭'
				this.meConfirmText = '一键复制'
			},
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm6') {
					uni.navigateTo({
						url: '/pages/public/login'
					})
				} else if (that.meIndex == 'm1') {
					let relationId = that.invitationCode;
					uni.setClipboardData({
						data: that.tuiguang1 + relationId + '\n' + that.urls,
						success: function() {
							console.log('success');
							that.$queue.showToast('文案复制成功');
						}
					});
				} else if (that.meIndex == 'm2') {
					uni.hideLoading();
					uni.openSetting();
				} else if (that.meIndex == 'm66') {
					uni.showLoading({
						title: '取消报名中...',
						icon: 'none'
					})
					that.$Request.postT('/app/signMatchActivity/cancelSignActivity', {
						activityId: that.id
					}).then(res => {
						uni.hideLoading();
						if (res.code == 0) {
							uni.showToast({
								title: '取消成功',
								icon: 'none'
							})
							that.getEventInfo()

						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					});
				} else if (that.meIndex == 'm7') {
					uni.navigateTo({
						url: '/my/wallet/Txmoney'
					})
				} else if (that.meIndex == 'm56') {
					uni.showLoading({
						title: '结束报名中...',
						icon: 'none'
					})
					that.$Request.getT('/app/matchActivity/matchCloseActivity', {
						activityId: that.id
					}).then(res => {
						uni.hideLoading();
						if (res.code == 0) {
							uni.showToast({
								title: '结束成功',
								icon: 'none'
							})
							that.getEventInfo()
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					});
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (that.meIndex == 'm1') {

				}
			},
			// 在线客服
			goChat() {
				let that = this
				console.log(that.SelKeFu, 'that.SelKeFu ')
				if (that.SelKeFu === '1') {
					uni.makePhoneCall({
						phoneNumber: that.$queue.getData('kefuPhone') //仅为示例
					})

				} else if (that.SelKeFu === '2') {
					// #ifdef MP-WEIXIN
					try {
						wx.openCustomerServiceChat({
							extInfo: {
								url: that.$queue.getData('SelKeFuLink')
							},
							corpId: that.$queue.getData('SelKeFuAppId'),
							success(res) {},
							fail(res) {
								console.error(res)
							}
						})
					} catch (error) {
						console.error("catchcatch" + error)
						uni.showToast({
							title: '请更新至微信最新版本'
						});
					}
					// #endif
					// #ifndef MP-WEIXIN
					let url = that.$queue.getData('SelKeFuLink');
					if (url.indexOf('/pages/') !== -1 || url.indexOf('/my/') !== -1 || url.indexOf('/package/') !== -1) {
						uni.navigateTo({
							url
						});
					} else {
						//#ifndef H5
						uni.navigateTo({
							url: '/pages/index/webView?url=' + url
						});
						//#endif
						//#ifdef H5
						window.location.href = url;
						//#endif
					}
					// #endif
				} else {
					let token = that.$queue.getData('token');
					if (token) {
						uni.navigateTo({
							url: '/my/setting/chat'
						});
					} else {
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '您还未登录,请先登录'
						this.meIndex = 'm1'
						this.meShowCancel = true
					}
				}
			},
			getEventInfo() {
				this.$Request.get('/app/matchActivity/getMatchActivityInfo', {
					activityId: this.id,
					userId: uni.getStorageSync('userId')
				}).then(res => {
					if (res.code === 0) {
						if (res.data.titleCarousel) {
							res.data.titleCarousel = res.data.titleCarousel.split(',')
						}
						if (res.data.activityPhoto) {
							res.data.activityPhoto = res.data.activityPhoto.split(',')
						}
						// if (res.data.startTime) {
						// 	res.data.startTime = res.data.startTime.substring(0, 10);
						// }
						// if (res.data.endTime) {
						// 	res.data.endTime = res.data.endTime.substring(0, 10);
						// }

						if (res.data.signUserList && res.data.signUserList.length != 0) {
							if (res.data.signUserList.length > 4) {
								res.data.signUserList = res.data.signUserList.splic(0, 4)
							}
							// 	res.data.signUserList.map((d, index) => {
							// 		d.phones = d.phone.substring(0, 3) + '****' + d.phone.substring(7, 11)
							// 	})
						}

						this.info = res.data
						this.backgroundImage = res.data.titleCarousel[0]
						this.tuiguang = res.data.title
						this.tuiguang1 = res.data.title
					}
				});
			},
			joinActive() {
				// this.info.isFree =1
				if (this.info.isFree == 2) {
					this.$Request.post('/app/signMatchActivity/signActivity', {
						activityId: this.info.activityId,
					}).then(res => {
						if (res.code == 0) {
							uni.showToast({
								title: '报名成功',
								icon: 'none',
								mask: true
							})
							this.getEventInfo()
							setTimeout(function() {
								uni.navigateTo({
									url: '/my/Events/huodong'
								})
							}, 1000)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none',
								mask: true
							})
						}
					})
				} else {
					this.show = true
					// 支付方式（免费的可不传） 0余额支付 1app 2微信小程序 3公众号支付 4支付宝APP支付 5支付宝H5支付
				}

			},
			initPayInfo() {
				// #ifdef APP-PLUS
				this.openLists = [{
					image: '../static/zhifubao.png',
					text: '支付宝',
					id: 1
				}, {
					image: '../static/icon_weixin.png',
					text: '微信',
					id: 2
				}, {
					image: '../../static/images/my/cz.png',
					text: '零钱',
					id: 3
				}];
				this.openWay = 1;
				// #endif

				// #ifdef MP-WEIXIN
				this.openLists = [{
					image: '../static/icon_weixin.png',
					text: '微信',
					id: 2
				}, {
					image: '../../static/images/my/cz.png',
					text: '零钱',
					id: 3
				}];
				this.openWay = 2;
				// #endif

				// #ifdef H5
				let ua = navigator.userAgent.toLowerCase();
				if (ua.indexOf('micromessenger') !== -1) {
					//公众号是否自动登录  333
					this.$Request.get('/app/common/type/333').then(res => {
						if (res.data && res.data.value && res.data.value == '是') {
							this.openLists = [{
								image: '../static/zhifubao.png',
								text: '支付宝',
								id: 1
							}, {
								image: '../static/icon_weixin.png',
								text: '微信',
								id: 2
							}, {
								image: '../../static/images/my/cz.png',
								text: '零钱',
								id: 3
							}];
							this.openWay = 2;
						} else {
							this.openLists = [{
								image: '../static/zhifubao.png',
								text: '支付宝',
								id: 1
							}, {
								image: '../../static/images/my/cz.png',
								text: '零钱',
								id: 3
							}];
							this.openWay = 1;
						}
					})
				} else {
					this.openLists = [{
						image: '../static/zhifubao.png',
						text: '支付宝',
						id: 1
					}, {
						image: '../../static/images/my/cz.png',
						text: '零钱',
						id: 3
					}];
					this.openWay = 1;
				}
				// #endif

			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					console.log(res);
					if (res.code === 0) {
						// res.data.money=10
						this.userInfo = res.data
						this.mymoney = res.data.money
					}
				})
			},
			selectWay: function(item) {
				this.openWay = item.id;
			},
			cellback(res) {
				console.log(res);

			},
			pay() {
				uni.showLoading({
					title: '支付中...'
				});
				this.show = false
				let id = this.info.activityId
				if (this.openWay == 1) { //支付宝支付
					// #ifdef H5
					let data = {
						classify: 5,
						activityId: id
					}
					this.$Request.postT("/app/signMatchActivity/signActivity", data).then(res => {
						if (res.code == 0) {
							const div = document.createElement('div')
							div.innerHTML = res.data //此处form就是后台返回接收到的数据
							document.body.appendChild(div)
							document.forms[0].submit()
							uni.hideLoading()
						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					});
					// #endif

					// #ifdef APP
					let data = {
						classify: 4,
						activityId: id
					}
					this.$Request.postT("/app/signMatchActivity/signActivity", data).then(ret => {
						this.isCheckPay(ret.code, 'alipay', ret.data);
					});
					// #endif
				} else if (this.openWay == 2) { //微信支付
					// #ifdef MP-WEIXIN
					let data = {
						classify: 2,
						activityId: id
					}
					this.$Request.postT("/app/signMatchActivity/signActivity", data).then(ret => {
						uni.hideLoading()
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: ret.data.timestamp,
							nonceStr: ret.data.noncestr,
							package: ret.data.package,
							signType: ret.data.signType,
							paySign: ret.data.sign,
							success: function(suc) {
								console.log('success:' + JSON.stringify(suc));
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								setTimeout(function() {
									uni.navigateTo({
										url: '/my/Events/huodong'
									})
								}, 1000)
							},
							fail: function(err) {
								console.log('fail:' + JSON.stringify(err));
								uni.showToast({
									title: '支付失败',
									icon: 'none'
								})
							}
						});
					});
					// #endif
					// #ifdef H5
					let data = {
						classify: 3,
						activityId: id
					}
					this.$Request.postT("/app/signMatchActivity/signActivity", data).then(res => {
						if (res.code == 0) {
							uni.hideLoading()
							this.callPay(res.data);

						} else {
							uni.showToast({
								icon: 'none',
								title: res.msg
							});
						}
					});
					// #endif
					// #ifdef APP
					let data = {
						classify: 1,
						activityId: id
					}
					this.$Request.postT("/app/signMatchActivity/signActivity", data).then(ret => {
						console.log(ret, 'retretretretretret')
						this.isCheckPay(ret.code, 'wxpay', JSON.stringify(ret.data));
					});
					// #endif
				} else if (this.openWay == 3) {
					if (this.mymoney >= this.info.money) {
						let data = {
							classify: 0,
							activityId: id
						}
						this.$Request.postT("/app/signMatchActivity/signActivity", data).then(res => {
							if (res.code == 0) {
								uni.hideLoading()
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								setTimeout(function() {
									uni.navigateTo({
										url: '/my/Events/huodong'
									})
								}, 1000)
							} else {
								uni.showToast({
									icon: 'none',
									title: res.msg
								});
							}
						});
					} else {
						uni.hideLoading()
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '零钱余额不足，请选去充值'
						this.meConfirmText = '去充值'
						this.meIndex = 'm7'
						this.meShowCancel = true
					}


				}
			},
			callPay: function(response) {
				console.log(response)
				if (typeof WeixinJSBridge === "undefined") {
					if (document.addEventListener) {
						document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);
					} else if (document.attachEvent) {
						document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));
						document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));
					}
				} else {
					console.log(1)
					this.onBridgeReady(response);
				}
			},
			onBridgeReady: function(response) {
				let that = this;
				if (!response.package) {
					return;
				}
				console.log(response, '++++++++')
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
						"appId": response.appid, //公众号名称，由商户传入
						"timeStamp": response.timestamp, //时间戳，自1970年以来的秒数
						"nonceStr": response.noncestr, //随机串
						"package": response.package,
						"signType": response.signType, //微信签名方式：
						"paySign": response.sign //微信签名
					},
					function(res) {
						console.log(res, '/*-/*-/*-')
						if (res.err_msg === "get_brand_wcpay_request:ok") {
							// 使用以上方式判断前端返回,微信团队郑重提示：
							//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
							uni.showLoading({
								title: '支付成功'
							});
							uni.hideLoading();
							setTimeout(function() {
								uni.navigateTo({
									url: '/my/Events/huodong'
								})
							}, 1000)
						} else {
							uni.hideLoading();
						}
						WeixinJSBridge.log(response.err_msg);
					}
				);
			},
			isCheckPay(status, name, order) {
				if (status == 0) {
					this.setPayment(name, order);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: '支付信息有误',
						icon: 'none'
					});
				}
			},
			setPayment(name, order) {
				console.log('*-*-*')
				uni.requestPayment({
					provider: name,
					orderInfo: order, //微信、支付宝订单数据
					success: function(res) {
						console.log(res)
						uni.hideLoading();

						setTimeout(function() {
							uni.navigateBack()
						}, 1000)
					},
					fail: function(err) {
						console.log(err)
						uni.hideLoading();
					},
					complete() {
						uni.hideLoading();
					}
				});
			},
			preview(tempFilePaths, current = 0) {
				uni.previewImage({
					urls: tempFilePaths,
					current,
					success: (result) => {},
					fail: (error) => {}
				})
			}
		}
	}
</script>

<style lang="less">
	page {
		background: #F5F7FF;
	}

	.banerbox {
		border-radius: 24rpx;
		margin: 20rpx 30rpx;

		swiper,
		image {
			width: 100%;
			height: 320rpx;
			border-radius: 24rpx;
		}
	}

	.Imagebox {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 0 30rpx;

		.tite {
			width: 590rpx;
			font-size: 34rpx;
			font-family: PingFang SC;
			font-weight: 800;
			color: #111224;
		}

		.actdata {
			font-family: PingFang SC;
			font-weight: 400;
			font-size: 26rpx;
			color: #666666;
		}

	}

	.tx {
		display: flex;
		align-items: center;

		.hdman {
			width: 52rpx;
			height: 52rpx;


			image {
				width: 100%;
				height: 100%;
				border-radius: 50%;


			}
		}
	}

	.hdsm {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx;
		padding: 30rpx 29rpx;

		.name {
			font-size: 30rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #333333;
		}

		.xtit {
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #333333;
			margin-top: 20rpx;
		}

		.deImg {
			width: 100%;
			margin-top: 30rpx;

			image {
				width: 100%;
				height: 325rpx;
				border-radius: 24rpx;
			}
		}

		.moretit {
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FF6F9C;
			text-align: center;
			padding-top: 20rpx;
		}
	}

	.taber {
		background-color: #FFFFFF;
		position: fixed;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 99;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx 30rpx;
		width: 100%;

		.tabImg {
			flex: 1;
			text-align: center;
			font-size: 24rpx;

			image {
				width: 42rpx;
				height: 42rpx;
			}
		}

		.bmbtn {
			width: 380rpx;
			height: 78rpx;
			background: #FF71A1;
			border-radius: 8rpx;

			margin-left: 30rpx;
			margin-right: 20rpx;
			// width: 470rpx;
			// height: 78rpx;
			// background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
			// border-radius: 39rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
		}

		.bmbtns {
			width: 380rpx;
			height: 78rpx;
			background: #CECECE;
			border-radius: 8rpx;

			margin-left: 30rpx;
			margin-right: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
		}

		.jieshu {
			width: 100%;
			height: 78rpx;
			background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
			border-radius: 8rpx;

			margin-left: 30rpx;
			margin-right: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 28rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
		}
	}

	.btn {

		height: 78rpx;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
		border-radius: 55rpx;

		margin-left: 30rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
	}
</style>
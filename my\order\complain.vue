<template>
	<view class="page" style="background-color: #F5F5F5;">
		<view class="feedback-title">
			<text>投诉内容</text>
		</view>
		<view class="feedback-body"><textarea placeholder="请输入投诉内容..." v-model="sendDate.content"
				class="feedback-textare" /></view>
		<!-- <view class="feedback-title"><text>联系方式</text></view>
		<view class="feedback-body"><input class="feedback-input" v-model="sendDate.contact" placeholder="方便我们联系你" /> -->
		<view class="feedback-title">
			<text>投诉图片</text>
		</view>
		<view class="release_image">
			<shmily-drag-image :list.sync="imageList" :number="6"></shmily-drag-image>
		</view>
		<button class="feedback-submit" @tap="send">提交投诉</button>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	import shmilyDragImage from '../components/shmily-drag-image/shmily-drag-image.vue'
	export default {
		components: {
			shmilyDragImage
		},
		data() {
			return {
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				msgContents: ['界面显示错乱', '启动缓慢，卡出翔了', 'UI无法直视，丑哭了', '偶发性崩溃'],
				stars: [1, 2, 3, 4, 5],
				imageList: [],
				byUserId: '',
				platform: 1,
				platformId: '',
				sendDate: {
					content: '',
					contact: ''
				}
			};
		},
		onLoad(e) {
			if (e.byUserId) {
				this.byUserId = e.byUserId;
			}
			if (e.platform) {
				this.platform = e.platform;
			}
			if (e.platformId) {
				this.platformId = e.platformId;
			}
		},
		methods: {
			//确认
			meHandleBtn() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			send() {
				//发送反馈
				let images = '';
				let userId = this.$queue.getData('userId');
				if (!this.sendDate.content) {
					uni.showToast({
						icon: 'none',
						title: '请输入投诉内容'
					});
					return;
				}
				if (this.$queue.getChatSearchKeys(this.sendDate.content)) {
					uni.showToast({
						title: "输入内容带有非法关键字请重新输入",
						mask: false,
						duration: 1500,
						icon: "none"
					});
					this.$Request.postT('/app/risk/insertRisk?riskType=4&content=' + this.sendDate.content).then(res => {

					})
					return;
				}
				if (this.imageList.length == 0) {
					uni.hideLoading();
					this.$queue.showToast('请添加图片!');
					return;
				} else {
					for (var i = 0; i < this.imageList.length; i++) {
						if (i === 0) {
							images = this.imageList[i];
						} else {
							images = images + ',' + this.imageList[i];
						}
					}
				}
				this.$queue.showLoading('加载中...');
				this.$Request.postJson('/app/message/insertMessage', {
					content: this.sendDate.content,
					image: images,
					state: 3,
					platform: this.platform,
					userId: userId,
					byUserId: this.byUserId,
					platformId: this.platformId
				}).then(res => {
					if (res.code === 0) {
						this.$queue.showToast('投诉成功，等待平台处理！')
						setTimeout(function() {
							uni.navigateBack();
						}, 2000);
					} else {
						uni.hideLoading();
						this.meShowModel = true
						this.meTitle = '投诉失败'
						this.meContent = res.msg
						this.meIndex = 'm1'
					}
				});
			}
		}
	};
</script>

<style>
	@font-face {
		font-family: uniicons;
		font-weight: normal;
		font-style: normal;
		src: url('https://img-cdn-qiniu.dcloud.net.cn/fonts/uni.ttf') format('truetype');
	}



	page {
		background-color: #F5F5F5 !important;
	}

	view {
		font-size: 28upx;
	}

	.release_image {
		margin: 20rpx;
		width: 95%;
		/* margin: 0 auto; */
	}

	/*问题反馈*/
	.feedback-title {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		padding: 20upx;
		color: #8f8f94;
		font-size: 28upx;
	}

	.feedback-star-view.feedback-title {
		justify-content: flex-start;
		margin: 0;
	}

	.feedback-body {
		font-size: 32upx;
		padding: 16upx;
		margin: 16upx;
		border-radius: 16upx;
		background: #FFFFFF;

	}

	.feedback-textare {
		height: 200upx;
		font-size: 34upx;
		line-height: 50upx;
		width: 100%;
		box-sizing: border-box;
		padding: 20upx 30upx 0;
	}

	.feedback-input {
		font-size: 32upx;
		height: 60upx;
		padding: 15upx 20upx;
		line-height: 60upx;
	}


	.feedback-submit {
		background: #AC75FE;
		color: #ffffff;
		margin: 20upx;
		margin-top: 32upx;
	}
</style>
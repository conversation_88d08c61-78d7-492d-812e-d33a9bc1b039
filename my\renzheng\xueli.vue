<template>
  <view v-if="shangxianSelect != '否'">
    <view class="text-center text-red bg-white">{{ form.remek }}</view>
    <view class="padding">
      <view class="text-black padding bg radius">
        <u-form
          :model="form"
          ref="uForm"
          label-position="top"
          :label-style="lableStyle"
        >
          <u-form-item label="所在学校" :border-bottom="true">
            <u-input
              placeholderStyle="color:#999999"
              :customStyle="customStyle3"
              placeholder="请填写学校名称"
              v-model="form.schoolName"
            />
          </u-form-item>
          <view class="margin-tb-xl"></view>
          <u-form-item label="入学时间" :border-bottom="false">
            <u-input
              placeholder="请填写（必填）"
              placeholderStyle="color:#999999"
              :disabled="true"
              @click="show = true"
              :customStyle="customStyle3"
              v-model="form.enrollmentTime"
              height="60"
            />
          </u-form-item>
        </u-form>
      </view>
      <view class="text-white padding bg radius margin-top">
        <view class="">上传校园卡/学生证/录取通知书</view>
        <!-- <view class="margin-top flex align-center justify-center"
					style="background: #F5F5F5; width: 150rpx;height: 150rpx;position: relative;">
					<view class="text-center" @click="addImages(1)" v-if="!form.schoolImg">
						<image src="../static/photo.png" mode="widthFix" style="width: 54rpx;"></image>
						<view class="text-sm text-gray margin-top-xs">添加图片</view>
					</view>
					<image @click="disabled? '':addImages(1)" v-else :src="form.schoolImg"
						style="width: 100%;height: 320rpx;"></image>
				</view> -->
        <view class="flex" style="overflow: hidden; flex-wrap: wrap">
          <view v-if="schoolImg.length">
            <view class="margin-top flex margin-right-sm flex-wrap">
              <view
                class="flex"
                v-for="(item, index) in schoolImg"
                :key="index"
                style="
                  width: 200rpx;
                  height: 200rpx;
                  margin-right: 5rpx;
                  position: relative;
                  margin-bottom: 10rpx;
                "
              >
                <image :src="item" style="width: 100%; height: 100%"></image>
                <view
                  style="
                    z-index: 9;
                    position: absolute;
                    top: -15rpx;
                    right: -15rpx;
                  "
                  @click="removeImg(index, 2)"
                >
                  <u-icon
                    name="close-circle-fill"
                    color="#AC75FE"
                    size="50rpx"
                  ></u-icon>
                </view>
              </view>
            </view>
          </view>
          <view
            class="margin-top"
            @click="addImage(2)"
            v-if="schoolImg.length <= 8"
          >
            <view
              style="width: 200rpx; height: 200rpx; background: #f5f5f5"
              class="flex justify-center align-center"
            >
              <view>
                <view class="text-center">
                  <image
                    src="../static/photo.png"
                    style="width: 65rpx; height: 55rpx"
                  >
                  </image>
                </view>
                <view class="text-center">添加图片</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <u-button
        v-if="!disabled"
        @click="submit"
        class="margin-top"
        :custom-style="customStyle"
        shape="square"
        :hair-line="false"
        >提交审核</u-button
      >
    </view>

    <u-picker v-model="show" mode="time" @confirm="confirm"></u-picker>
  </view>
</template>

<script>
import configdata from "../../common/config.js";
export default {
  data() {
    return {
      shangxianSelect: "否",
      form: {
        schoolName: "",
        enrollmentTime: "",
        schoolImg: "",
        userType: 2,
        id: "",
      },
      disabled: false,
      schoolImg: [],

      lableStyle: {
        color: "#000000",
        fontSize: "28upx",
      },
      customStyle: {
        backgroundColor: "linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);",
        color: "#FFFFFF",
        border: 0,
      },
      customStyle3: {
        color: "#000000",
        border: 0,
      },
      count: "",
      show: false,
    };
  },
  onLoad() {
    // #ifdef MP-WEIXIN
    this.shangxianSelect = this.$queue.getData("shangxianSelect");
    // #endif
    // #ifndef MP-WEIXIN
    this.shangxianSelect = "是";
    // #endif
    this.getUserInfo();
  },
  methods: {
    confirm(e) {
      console.log(e);
      this.form.enrollmentTime = e.year + "-" + e.month + "-" + e.day;
    },
    // 上传主页图删除
    removeImg(index, ind) {
      if (ind == 1) {
        this.skillImg.splice(index, 1);
      } else if (ind == 2) {
        this.lifeImg.splice(index, 1);
      }
    },
    submit() {
      console.log(this.form);
      this.form.schoolImg = this.schoolImg;
      this.form.schoolImg = this.form.schoolImg.toString();
      if (!this.form.schoolName) {
        uni.showToast({
          title: "请输入学校名称",
          icon: "none",
          duration: 1000,
        });
        return;
      }
      if (!this.form.enrollmentTime) {
        uni.showToast({
          title: "请选择入学时间",
          icon: "none",
          duration: 1000,
        });
        return;
      }
      if (!this.form.schoolImg) {
        uni.showToast({
          title: "上传校园卡/学生证/录取通知书",
          icon: "none",
          duration: 1000,
        });
        return;
      }

      this.$Request
        .postJson("/app/userCertification/saveUserCertification", this.form)
        .then((res) => {
          if (res.code == 0) {
            uni.showToast({
              title: "认证提交成功！",
              icon: "none",
            });
            setTimeout(function () {
              uni.navigateBack();
            }, 1000);
          } else {
            uni.showToast({
              title: res.msg,
              icon: "none",
            });
          }
        });
    },
    getUserInfo() {
      this.$Request
        .get("/app/userCertification/getMyUserCertification")
        .then((res) => {
          if (res.code == 0 && res.data) {
            this.form.schoolName = res.data.schoolName
              ? res.data.schoolName
              : "";
            this.form.enrollmentTime = res.data.enrollmentTime
              ? res.data.enrollmentTime
              : "";
            this.form.id = res.data.id ? res.data.id : "";
            // this.form.back = res.data.back ? res.data.back : ''

            if (res.data.schoolImg) {
              this.schoolImg = res.data.schoolImg.split(",");
            }
            // if (res.data.skillImg) {
            // 	this.skillImg = res.data.skillImg.split(',')
            // }
            // this.form.details = res.data.details
            if (res.data.isSubmit == 0) {
              if (res.data.status == 0 || res.data.status == 1) {
                this.disabled = true;
              }
            } else {
              this.disabled = true;
            }
            if (res.data.status == 2) {
              this.form.remek = res.data.remek ? res.data.remek : "";
            }
          }
        });
    },
    // 图片上传
    addImage(e) {
      var num = this.schoolImg.length;
      this.count = 9 - num;
      let that = this;
      uni.chooseImage({
        count: this.count,
        sourceType: ["album", "camera"],
        success: (res) => {
          for (let i = 0; i < res.tempFilePaths.length; i++) {
            that.$queue.showLoading("上传中...");
            uni.uploadFile({
              // 上传接口
              url: that.config("APIHOST1") + "/alioss/upload", //真实的接口地址
              filePath: res.tempFilePaths[i],
              name: "file",
              success: (uploadFileRes) => {
                // console.log(JSON.parse(uploadFileRes.data).data)
                that.schoolImg.push(JSON.parse(uploadFileRes.data).data);
                uni.hideLoading();
              },
            });
          }
        },
      });
    },
    config: function (name) {
      var info = null;
      if (name) {
        var name2 = name.split("."); //字符分割
        if (name2.length > 1) {
          info = configdata[name2[0]][name2[1]] || null;
        } else {
          info = configdata[name] || null;
        }
        if (info == null) {
          let web_config = cache.get("web_config");
          if (web_config) {
            if (name2.length > 1) {
              info = web_config[name2[0]][name2[1]] || null;
            } else {
              info = web_config[name] || null;
            }
          }
        }
      }
      return info;
    },
  },
};
</script>

<style>
.bg {
  background-color: #ffffff;
}

::v-deep.u-form-item {
  padding: 0 !important;
  line-height: 10px !important;
}
</style>

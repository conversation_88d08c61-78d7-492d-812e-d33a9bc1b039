<template>
	<view class="page">
		<u-navbar title="超级推荐" :is-back="true" :border-bottom="false" :background="background"
			title-color="#111224"></u-navbar>
		<view class="bgimg">
			<image src="../static/tdbg.png" mode="" style="width: 100%;height: 100vh;"></image>
		</view>
		<view class="tuibox">
			<view class="flex align-center margin-bottom-xs">
				<image src="../static/nengl.png" style="width: 40rpx;height: 40rpx;"></image>
				<view class="text-lg text-bold margin-lr-xs">10倍曝光</view>
				<view @click="shows = true">
					<u-icon name="question-circle" color="#46444F" size="40"></u-icon>
				</view>
			</view>
			<view style="color: #B5B6BA;">曝光量提升10倍，并在嘉宾<text style="color: #333;">首页置顶</text>展示</view>
			<view style="color: #B5B6BA;">持续24小时，助获大量喜欢</view>
			<view class="margin-tb-sm">
				<image src="../static/nengvg.png" style="width: 100%;height: 278rpx;"></image>
			</view>

			<view class="btn" @click="show = true" v-if="updateRecommendCount==0">立即开启（{{money}}元
				)
			</view>
			<view class="btn" @click="userDete" v-else>立即开启（{{updateRecommendCount}}次)
			</view>
		</view>


		<u-popup v-model="shows" mode="center" border-radius="24" width="500rpx" :closeable="true">
			<view class="padding">
				<view class="text-lg text-center">10倍曝光说明</view>
				<view class="" style="padding:45rpx 0 10rpx;">
					{{conten}}
				</view>
			</view>
		</u-popup>


		<u-popup v-model="show" mode="bottom" border-radius="24">
			<view class="padding">
				<view class="text-center text-bold text-lg">支付方式</view>
				<view class="flex align-center justify-between" style="height: 100upx;padding: 30upx;"
					v-for="(item,index) in openLists" :key='index'>
					<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;"></image>
					<view style="font-size: 30upx;margin-left:0upx;width: 70%;">{{item.text}}
					</view>
					<radio-group name="openWay" style="margin-left: 20upx;" @tap='selectWay(item)'>
						<label class="tui-radio">
							<radio color="#333333" :checked="openWay === item.id ? true : false" />
						</label>
					</radio-group>
				</view>

				<view class="btn margin-top" @click="pay">立即支付</view>
			</view>
		</u-popup>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				background: {
					backgroundColor: '#FDD8EC'
				},
				money: '',
				show: false,
				openLists: [],
				openWay: 1,
				updateRecommendCount: '',
				shows: false,
				conten: '',
				mymoney: 0,
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key

				statusStauts: '',
			}
		},
		onLoad() {


			// #ifdef APP-PLUS
			this.openLists = [{
				image: '../../../static/images/my/zhifubao.png',
				text: '支付宝',
				id: 1
			}, {
				image: '../../../static/images/my/icon_weixin.png',
				text: '微信',
				id: 2
			}, {
				image: '../../../static/images/my/cz.png',
				text: '零钱',
				id: 3
			}];
			this.openWay = 1;
			// #endif

			// #ifdef MP-WEIXIN
			this.openLists = [{
				image: '../../../static/images/my/icon_weixin.png',
				text: '微信',
				id: 2
			}, {
				image: '../../../static/images/my/cz.png',
				text: '零钱',
				id: 3
			}];
			this.openWay = 2;
			// #endif

			// #ifdef H5
			let ua = navigator.userAgent.toLowerCase();
			if (ua.indexOf('micromessenger') !== -1) {
				//公众号是否自动登录  416
				this.$Request.get('/app/common/type/416').then(res => {
					if (res.data && res.data.value && res.data.value == '是') {
						this.openLists = [{
							image: '../../../static/images/my/zhifubao.png',
							text: '支付宝',
							id: 1
						}, {
							image: '../../../static/images/my/icon_weixin.png',
							text: '微信',
							id: 2
						}, {
							image: '../../../static/images/my/cz.png',
							text: '零钱',
							id: 3
						}];
						this.openWay = 2;
					} else {
						this.openLists = [{
							image: '../../../static/images/my/zhifubao.png',
							text: '支付宝',
							id: 1
						}, {
							image: '../../../static/images/my/cz.png',
							text: '零钱',
							id: 3
						}];
						this.openWay = 1;
					}
				})
			} else {
				this.openLists = [{
					image: '../../../static/images/my/zhifubao.png',
					text: '支付宝',
					id: 1
				}, {
					image: '../../../static/images/my/cz.png',
					text: '零钱',
					id: 3
				}];
				this.openWay = 1;
			}
			// #endif


		},
		onShow() {
			//开启价格
			this.$Request.getT('/app/common/type/313').then(res => {
				if (res.code == 0) {
					if (res.data && res.data.value) {
						this.money = res.data.value
					}
				}
			});

			//开启价格
			this.$Request.getT('/app/common/type/330').then(res => {
				if (res.code == 0) {
					if (res.data && res.data.value) {
						this.conten = res.data.value
					}
				}
			});
			this.getUserInfo()
			this.getRenZheng()
			this.taskData()
		},
		methods: {
			getRenZheng() {
				this.$Request.get("/app/userCertification/getMyUserCertification").then(res => {
					if (res.code == 0 && res.data) {
						// 0审核中 1通过 2拒绝 
						if (res.data.status == 0) {
							this.statusStauts = 1 //审核中
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 1) {
							this.statusStauts = 2 //已实名
							uni.setStorageSync('statusStauts', this.statusStauts)
						} else if (res.data.status == 2) {
							this.statusStauts = 3 //已拒绝
							uni.setStorageSync('statusStauts', this.statusStauts)
						}
					} else {
						this.statusStauts = -1 //未实名
						uni.setStorageSync('statusStauts', this.statusStauts)
					}
				});
			},
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm7') {
					uni.navigateTo({
						url: '/my/wallet/Txmoney'
					})
				} else if (that.meIndex == 'm6') {
					uni.navigateTo({
						url: '/my/renzheng/index'
					})
				}else if (that.meIndex == 'm4') {
					uni.navigateTo({
						url: '/my/setting/userinfo'
					})
				} 
			},
			//取消
			meHandleClose() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			// 获取余额
			taskData() {
				this.$Request.get("/app/userMoney/selectMyMoney").then(res => {
					if (res.code == 0 && res.data) {
						this.mymoney = res.data.money
					}
				});
			},
			userDete() {
				let that = this
				if (that.statusStauts == 1) { //待审核
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '实名认证审核中，请通过后去完善资料'
					that.meIndex = 'm6'
					that.meConfirmText = '去查看'
					that.meShowCancel = true
				} else if (that.statusStauts == 2) { //已通过
					let userId = uni.getStorageSync('userId')
					that.$Request.get("/app/userData/getUserDataInfo?userId=" + userId).then(res => {
						if (res.code == 0) {
							if (res.data) {
								that.$Request.postT("/app/userData/useRecommend").then(res => {
									if (res.code == 0) {
										uni.showToast({
											title: '推荐成功',
											icon: 'none'
										})
										that.getUserInfo()
									} else {
										uni.showToast({
											icon: 'none',
											title: res.msg
										});
									}
								});
							} else {
								that.firstlogin = false
								that.meShowModel = true
								that.meTitle = '提示'
								that.meContent = '完善个人资料可以被更多人看到哦'
								that.meConfirmText = '去完善'
								that.meIndex = 'm4'
								that.meShowCancel = true
							}

						}

					});

				} else if (that.statusStauts == 3) { //已拒绝
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '实名审核被拒绝，请先去认证'
					that.meIndex = 'm6'
					that.meConfirmText = '去认证'
					that.meShowCancel = true
				} else if (that.statusStauts == -1) { //已通过
					// uni.navigateTo({
					// 	url: '/my/renzheng/index'
					// })
					that.firstlogin = false
					that.meShowModel = true
					that.meTitle = '提示'
					that.meContent = '未实名认证，请先去实名认证'
					that.meConfirmText = '去认证'
					that.meIndex = 'm6'
					that.meShowCancel = true
				}


			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						this.updateRecommendCount = res.data.updateRecommendCount
					}
				});
			},
			selectWay: function(item) {
				this.openWay = item.id;
			},
			pay() {
				uni.showLoading({
					title: '支付中...'
				});
				this.show = false
				if (this.openWay == 1) { //支付宝支付
					// #ifdef H5
					let data = {
						classify: 5
					}
					this.$Request.postT("/app/userData/buyRecommend", data).then(res => {
						if (res.code == 0) {
							const div = document.createElement('div')
							div.innerHTML = res.data //此处form就是后台返回接收到的数据
							document.body.appendChild(div)
							document.forms[0].submit()
							uni.hideLoading()
						} else {
							uni.showToast({
								icon: 'none',
								title: '支付失败!'
							});
						}
					});
					// #endif

					// #ifdef APP
					let data = {
						classify: 4
					}
					this.$Request.postT("/app/userData/buyRecommend", data).then(ret => {
						console.log(ret)
						that.isCheckPay(ret.code, 'alipay', ret.data);
					});
					// #endif
				} else if (this.openWay == 2) { //微信支付
					// #ifdef MP-WEIXIN
					let data = {
						classify: 2
					}
					this.$Request.postT("/app/userData/buyRecommend", data).then(ret => {
						uni.hideLoading()
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: ret.data.timestamp,
							nonceStr: ret.data.noncestr,
							package: ret.data.package,
							signType: ret.data.signType,
							paySign: ret.data.sign,
							success: function(suc) {
								console.log('success:' + JSON.stringify(suc));
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								setTimeout(function() {
									uni.navigateBack()
								}, 1000)
							},
							fail: function(err) {
								console.log('fail:' + JSON.stringify(err));
								uni.showToast({
									title: '支付失败',
									icon: 'none'
								})
							}
						});
					});
					// #endif
					// #ifdef H5
					let data = {
						classify: 3
					}
					this.$Request.postT("/app/userData/buyRecommend", data).then(res => {
						if (res.code == 0) {
							uni.hideLoading()
							this.callPay(res.data);
						} else {
							uni.showToast({
								icon: 'none',
								title: '支付失败!'
							});
						}
					});
					// #endif
					// #ifdef APP
					let data = {
						classify: 1
					}
					this.$Request.postT("/app/userData/buyRecommend", data).then(ret => {
						console.log(ret, 'retretretretretret')
						this.isCheckPay(ret.code, 'wxpay', JSON.stringify(ret.data));
					});
					// #endif
				} else if (this.openWay == 3) {
					if (this.mymoney >= this.money) {
						let data = {
							classify: 0
						}
						this.$Request.postT("/app/userData/buyRecommend", data).then(ret => {
							uni.hideLoading()
							if (ret.code == 0) {
								uni.showToast({
									title: '支付成功',
									icon: 'success'
								})
								setTimeout(function() {
									uni.navigateBack()
								}, 1000)
							} else {
								uni.showToast({
									icon: 'none',
									title: ret.msg
								});
							}
						});
					} else {
						uni.hideLoading()
						this.meShowModel = true
						this.meTitle = '提示'
						this.meContent = '零钱余额不足，请选去充值'
						this.meConfirmText = '去充值'
						this.meIndex = 'm7'
						this.meShowCancel = true
					}

				}
			},
			callPay: function(response) {
				console.log(response)
				if (typeof WeixinJSBridge === "undefined") {

					if (document.addEventListener) {
						document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);
					} else if (document.attachEvent) {
						document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));
						document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));
					}
				} else {
					console.log(1)
					this.onBridgeReady(response);
				}
			},
			onBridgeReady: function(response) {
				let that = this;
				if (!response.package) {
					return;
				}
				console.log(response, '++++++++')
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
						"appId": response.appid, //公众号名称，由商户传入
						"timeStamp": response.timestamp, //时间戳，自1970年以来的秒数
						"nonceStr": response.noncestr, //随机串
						"package": response.package,
						"signType": response.signType, //微信签名方式：
						"paySign": response.sign //微信签名
					},
					function(res) {
						console.log(res, '/*-/*-/*-')
						if (res.err_msg === "get_brand_wcpay_request:ok") {
							// 使用以上方式判断前端返回,微信团队郑重提示：
							//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
							uni.showLoading({
								title: '支付成功'
							});
							uni.hideLoading();
							setTimeout(function() {
								uni.navigateBack()
							}, 1000)
						} else {
							uni.hideLoading();
						}
						WeixinJSBridge.log(response.err_msg);
					}
				);
			},
			isCheckPay(status, name, order) {
				if (status == 0) {
					this.setPayment(name, order);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: '支付信息有误',
						icon: 'none'
					});
				}
			},
			setPayment(name, order) {
				console.log('*-*-*')
				uni.requestPayment({
					provider: name,
					orderInfo: order, //微信、支付宝订单数据
					success: function(res) {
						console.log(res)
						uni.hideLoading();
						uni.showLoading({
							title: '支付成功'
						});
						setTimeout(function() {
							uni.navigateBack()
						}, 1000)
					},
					fail: function(err) {
						console.log(err)
						uni.hideLoading();
					},
					complete() {
						uni.hideLoading();
					}
				});
			},
		}
	}
</script>

<style lang="less">
	page {
		background: #D8E3FF;
	}


	.page {
		width: 100%;
		height: 1333rpx;
		// background: transparent;
		// background-image: url('../static/tdbg.png');
		// background-size: 100% 100%;
		// background-repeat: no-repeat;
		position: relative;
	}

	.bgimg {
		position: absolute;
		top: 0;
		z-index: 1;
		width: 100%;
		height: 100vh;
	}

	.tuibox {
		background: #FFFFFF;
		// margin:0rpx 30rpx 0;
		border-radius: 24rpx;
		padding: 40rpx 30rpx;
		width: 92%;
		margin: 0 auto;
		position: absolute;
		top: 500rpx;
		left: 0;
		right: 0;
		z-index: 99;
	}

	.btn {
		background: #171F2C;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 90rpx;
		border-radius: 55rpx;
		font-size: 30rpx;
	}
</style>
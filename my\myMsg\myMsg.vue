<template>
	<view style="padding-bottom: 40rpx;">
		<view class="list flex align-center justify-center">
			<view class="list-box">
				<view class="list-box-item" v-for="(item,index) in list" :key="index">
					<view class="list-box-item-top flex align-center justify-between">
						<view class="list-box-item-top-time">
							消息通知
						</view>
						<view v-if="Number(item.isSee) == 0" class="list-box-item-top-dian">

						</view>
					</view>
					<view class="list-box-item-contont">
						{{item.content}}
					</view>
					<view class="list-box-item-time flex align-center justify-end">
						{{item.createAt}}
					</view>
				</view>
			</view>
		</view>
		<u-loadmore v-if="list.length>0" :status="status" margin-top="20" />
		<empty v-if="list.length==0" />
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				page: 1,
				pages: 1,
				limit: 10,
				list: [],
				status: 'loadmore',
			};
		},
		onLoad() {
			this.getList()
		},
		onPullDownRefresh() {
			this.page = 1
			this.getList()
		},
		onReachBottom() {
			if (this.page < this.pages) {
				this.page += 1
				this.status = 'loading'
				this.getList()
			}
		},
		methods: {
			// 获取列表
			getList() {
				let data = {
					page: this.page,
					limit: this.limit
				}
				this.$Request.getT('/app/message/getUserMessageInfoList', data).then(res => {
					uni.stopPullDownRefresh()
					if (res.code == 0) {
						this.pages = res.data.pages
						if (this.page < this.pages) {
							this.status = 'loadmore'
						} else {
							this.status = 'nomore'
						}
						if (this.page == 1) {
							this.list = res.data.records
						} else {
							this.list = [...this.list, ...res.data.records]
						}
					}
				})
			},
		}
	}
</script>

<style lang="scss">
	.list {
		width: 100%;
		height: auto;

		.list-box {
			width: 686rpx;
			height: 100%;
		}

		.list-box-item {
			margin-top: 30rpx;
			background-color: #ffffff;
			border-radius: 16rpx;
			padding: 30rpx;
		}

		.list-box-item-top-dian {
			width: 20rpx;
			height: 20rpx;
			background-color: red;
			border-radius: 50%;
		}

		.list-box-item-contont {
			margin-top: 20rpx;
			color: #999999;
		}

		.list-box-item-time {
			margin-top: 20rpx;
			color: #999999;
		}
	}
</style>
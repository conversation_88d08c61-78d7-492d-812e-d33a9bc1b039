<template>
	<view v-if="xcxSelect!='否'">
		<view class="padding">
			<view class="bg radius padding">
				<view class="text-lg">可提金额</view>
				<view class="flex margin-top-sm">
					<view class="text-xxl">{{money}}元</view>
				</view>
			</view>
			<view class="bg radius padding margin-top">
				<view class="text-lg margin-bottom">提现金额</view>
				<input type="number" placeholder="请输入提现金额" v-model="moneyNum">
				<view class="margin-tb-xs" style="width: 100%;height: 1rpx;background: #F5F5F5;"></view>
				<view class=" text-grey ">{{placeholder}}</view>
			</view>


			<view class="part_three">
				<view class="three_name">提现方式</view>
				<view class="btn">
					<view class="btn_right" v-if="zhifub=='是'" :class="current==1?'btna':''" @click="bindToindex(1)">
						<image src="../static/zhifubao.png"></image>
						<text>支付宝</text>
					</view>
					<view v-if="values==2" class="btn_left" :class="current==2?'btna':''" @click="bindToindex(2)">
						<image src="../static/icon_weixin.png"></image>
						<text>微信</text>
					</view>
				</view>
			</view>

			<view class="btn1" @click="cashMoney">立即提现</view>
		</view>



		<view class="flex justify-around margin-top">
			<view @click="goNav('./zhifubao')">提现账号</view>
			<view v-if="values==2" class="box_right" @click="isShow">
				微信收款码
			</view>
			<view @click="goNav('./mymoneydetail')">钱包明细</view>
			<view @click="goNav('./cashList')">提现记录</view>
		</view>

		<!-- 微信收款码弹框 -->
		<u-popup v-model="show" mode="center" border-radius="14">
			<view class="padding">
				<view class="text-center text-lg text-bold flex justify-between">
					<view></view>
					<view>添加微信收款码</view>
					<view @click="show=false">X</view>
				</view>

				<view style="width: 80%;margin: 0 auto;">
					<view class="margin-top" @click.stop="weixin"
						style="border: 4rpx solid #010101;border-radius: 16rpx;overflow: hidden;">
						<image v-if="!wximg" src="../../static/erweima.png" mode="aspectFill"></image>
						<image v-else :src="wximg" mode="aspectFill"></image>
					</view>
				</view>

			</view>
		</u-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title_color: 1,
				money: 0,
				avatar: '',
				Profit: 0,
				moneyNum: null,
				thisSelect: {
					id: 1,
					num: '50',
					price: '50',
					isSelect: true
				},
				charge: 0, //提现手续费
				maxMoney: 0, //最高提现额度
				minMoney: 0, //最低提现额度
				placeholder: '',
				sp: 0,

				current: '1',
				values: '',
				show: false,
				wximg: '',
				zhifubaoName: '',
				zhifubao: '',
				zhifub: '是',
				xcxSelect: '是',
			}
		},
		onLoad() {
			// this.xcxSelect = uni.getStorageSync('xcxSelect')
			// if(this.xcxSelect!='否'){
			// 	uni.setNavigationBarTitle({
			// 		title:'提现'
			// 	})
			// }else{
			// 	uni.setNavigationBarTitle({
			// 		title:'用户协议'
			// 	})
				
			// }
			this.avatar = uni.getStorageSync('avatar')

			//判断微信提现方式
			this.$Request.getT('/app/common/type/355').then(res => {
				if (res.code == 0) {
					if (res.data && res.data.value) {
						if (res.data.value == '是') {
							this.values = 2
						} else {
							this.values = 1
						}

					}
				}
			})
			this.$Request.getT('/app/common/type/356').then(res => {
				if (res.code == 0) {
					if (res.data && res.data.value) {
						this.zhifub = res.data.value
					}
				}
			})
		},
		onShow() {
			// this.xcxSelect = uni.getStorageSync('xcxSelect')
			this.getCharge()
			this.getMinMoney()
			this.getMaxMoney()
			this.getUserInfo()
		},
		methods: {
			isShow() {
				this.getUserInfo()
				this.show = true
			},
			bindToindex(e) {
				this.current = e
				// console.log(e, this.current)
			},
			getUserInfo() {
				this.$Request.getT("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						this.money = res.data.balance //我的余额
						this.zhifubaoName = res.data.zhiFuBaoName
						this.zhifubao = res.data.zhiFuBao
						this.wximg = res.data.cashQrCode
						uni.setStorageSync('zhiFuBao', res.data.zhiFuBao)
						uni.setStorageSync('zhiFuBaoName', res.data.zhiFuBaoName)
					}
				});
			},
			// 提现手续费
			getCharge() {
				this.$Request.get("/app/common/type/152").then(res => {
					if (res.code == 0) {
						this.charge = res.data.value
						this.placeholder = '服务费:' + this.charge * 100 + '%' + ',最低提现:' +
							this.minMoney * 1 + ',最高提现:' + this.maxMoney * 1
					}
				});
			},
			// 最低提现额度
			getMinMoney() {
				this.$Request.get("/app/common/type/112").then(res => {
					if (res.code == 0) {
						this.minMoney = res.data.value
						this.placeholder = '服务费:' + this.charge * 100 + '%' + ',最低提现:' +
							this.minMoney * 1 + ',最高提现:' + this.maxMoney * 1
					}
				});
			},
			// 最高提现额度
			getMaxMoney() {
				this.$Request.get("/app/common/type/153").then(res => {
					if (res.code == 0) {
						this.maxMoney = res.data.value
						this.placeholder = '服务费:' + this.charge * 100 + '%' + ',最低提现:' +
							this.minMoney * 1 + ',最高提现:' + this.maxMoney * 1
					}
				});
			},
			cut(e) {
				this.title_color = e
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			active(e) {
				this.wallet.forEach(res => {
					if (res.id == e.id) {
						res.isSelect = true
						this.thisSelect = e
					} else {
						res.isSelect = false
					}
				})
			},
			selectWay: function(item) {
				this.openWay = item.id;
			},
			// 提现
			// 提现
			cashMoney() {
				if (this.current == 1) {
					if (!/^\d+$/.test(this.moneyNum)) {
						uni.showToast({
							icon: 'none',
							title: '请输入正确金额,不能包含中文,英文,特殊字符和小数'
						});
						return;
					}
					// if (!this.zhifubao) {
					// 	uni.navigateTo({
					// 		url: '/pageA/wallet/zhifubao'
					// 	});
					// 	return
					// }

					if (Number(this.money) < Number(this.moneyNum)) {
						uni.showToast({
							icon: 'none',
							title: '可提现金额不足'
						});
						return;
					}

					if (Number(this.moneyNum) < Number(this.minMoney)) {
						uni.showToast({
							icon: 'none',
							title: '提现金额不能小于' + this.minMoney + '元'
						});
						return;
					}

					if (Number(this.moneyNum) > Number(this.maxMoney)) {
						uni.showToast({
							icon: 'none',
							title: '提现金额不能大于' + this.maxMoney + '元'
						});
						return;
					}



					let zhiFuBao = uni.getStorageSync('zhiFuBao')
					let zhiFuBaoName = uni.getStorageSync('zhiFuBaoName')

					if (!zhiFuBao && !zhiFuBaoName) {
						uni.showModal({
							title: '提示',
							content: '请先绑定提现账号',
							confirmText: '去添加',
							complete(res) {
								if (res.confirm) {
									uni.navigateTo({
										url: './zhifubao'
									})
								}
							}
						})
						return
					}

					let that = this
					that.sp = (that.moneyNum * this.charge).toFixed(2)
					uni.showModal({
						title: '提示',
						content: '本次提现' + that.moneyNum + '元，服务费' + this.sp + '元，是否确认提现？',
						success: function(res) {
							if (res.confirm) {
								that.$Request.getT("/app/cash/cashMoney", {
									money: that.moneyNum,
									classify: 1
								}).then(res => {
									if (res.code == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg
										})
										that.moneyNum = null
									} else {
										uni.showToast({
											icon: 'none',
											title: res.msg
										})
									}
									that.getUserInfo()
								});
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				} else {
					if (!/^\d+$/.test(this.moneyNum)) {
						uni.showToast({
							icon: 'none',
							title: '请输入正确金额,不能包含中文,英文,特殊字符和小数'
						});
						return;
					}
					// if (!this.zhifubao) {
					// 	uni.navigateTo({
					// 		url: '/pageA/wallet/zhifubao'
					// 	});
					// 	return
					// }

					if (Number(this.money) < Number(this.moneyNum)) {
						uni.showToast({
							icon: 'none',
							title: '可提现金额不足'
						});
						return;
					}

					if (Number(this.moneyNum) < Number(this.minMoney)) {
						uni.showToast({
							icon: 'none',
							title: '提现金额不能小于' + this.minMoney + '元'
						});
						return;
					}

					if (Number(this.moneyNum) > Number(this.maxMoney)) {
						uni.showToast({
							icon: 'none',
							title: '提现金额不能大于' + this.maxMoney + '元'
						});
						return;
					}
					// if (this.moneyNum*1+this.charge*this.moneyNum > this.money*1) {
					// 	uni.showToast({
					// 		icon: 'none',
					// 		title: '您的手续费不足'
					// 	})
					// 	return
					// }
					let that = this
					if (!that.wximg) {
						uni.showModal({
							title: '提现提示',
							content: '请上传微信收款码',
							showCancel: true,
							cancelText: '取消',
							confirmText: '上传',
							success: res => {
								if (res.confirm) {
									that.show = true;
								}
							},
							fail: () => {},
							complete: () => {}
						});
						return;
					}

					that.sp = (that.moneyNum * this.charge).toFixed(2)
					uni.showModal({
						title: '提示',
						content: '本次提现' + that.moneyNum + '元，服务费' + this.sp + '元，是否确认提现？',
						success: function(res) {
							if (res.confirm) {
								that.$Request.getT("/app/cash/cashMoney", {
									money: that.moneyNum,
									classify: 2
								}).then(res => {
									if (res.code == 0) {
										uni.showToast({
											icon: 'none',
											title: res.msg
										})
										that.moneyNum = null
									} else {
										uni.showToast({
											icon: 'none',
											title: res.msg
										})
									}
									that.getUserInfo()
								});
							} else if (res.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				}
			},
			weixin() {
				let that = this
				uni.chooseImage({
					count: 1, //默认9
					sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
					sourceType: ['album', 'camera'], //从相册选择
					success: (res) => {
						console.log('`````````````', res.tempFilePaths[0])
						that.$queue.showLoading("上传中...");
						for (let i = 0; i < 1; i++) {
							uni.uploadFile({ // 上传接口
								url: that.config("APIHOST1") + '/alioss/upload',
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									let img = JSON.parse(uploadFileRes.data).data
									// this.img.push()
									console.log(img)
									that.show = false
									let userId = that.$queue.getData('userId');

									let data = {
										cashQrCode: img
									}
									that.$Request.postJson('/app/user/updateUser',
										data).then(
										res => {
											uni.hideLoading();
											that.$queue.showToast('上传成功，请重新操作提现')
											that.getUserInfo()
										});
								}
							});
						}
					}
				});
			},
			config: function(name) {
				var info = null;
				if (name) {
					var name2 = name.split("."); //字符分割
					if (name2.length > 1) {
						info = configdata[name2[0]][name2[1]] || null;
					} else {
						info = configdata[name] || null;
					}
					if (info == null) {
						let web_config = cache.get("web_config");
						if (web_config) {
							if (name2.length > 1) {
								info = web_config[name2[0]][name2[1]] || null;
							} else {
								info = web_config[name] || null;
							}
						}
					}
				}
				return info;
			}
		}
	}
</script>

<style>
	page {
		background-color: #F7F7F7;
	}



	.bgCol2 {
		color: #1FC657;
	}

	.bg {
		background-color: #FFFFFF;
	}

	.active {
		border: 1px solid #FF6F9C !important;
		color: #FF6F9C !important;
	}

	.title_btn {
		height: 78upx;
		line-height: 78upx;
		/* background: #f7f7f7; */
	}

	.btn1 {
		width: 100%;
		height: 88upx;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
		border-radius: 44upx;
		text-align: center;
		line-height: 88upx;
		margin-top: 40upx;
		font-size: 28upx;
		color: #FFF;
	}

	.part_three {
		margin: 0 auto;
		background: #FFFFFF;
		margin-top: 20rpx;
		border-radius: 20rpx;
	}

	.three_name {
		width: 90%;
		margin: 0 auto;
		font-size: 33rpx;
		color: black;
		line-height: 80rpx;
	}

	.btn {
		width: 96%;
		margin: 0 auto;
		display: flex;
		justify-content: space-between;
		padding-bottom: 30rpx;
	}

	.btn_left {
		flex: 1;
		/* width: 240rpx; */
		height: 90rpx;
		border: 1rpx solid #ccc;
		border-radius: 20rpx;
		margin-right: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 20rpx;
	}

	.btn_right {
		flex: 1;
		/* width: 240rpx; */
		height: 90rpx;
		border: 1rpx solid #ccc;
		border-radius: 20rpx;
		margin-left: 10rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		margin-right: 20rpx;
	}

	.beizhu {
		line-height: 55rpx;
		color: red;
		letter-spacing: 1rpx;
	}

	.btna {
		border: 1rpx solid #FF6F9C !important;
	}


	.btn_left image {
		width: 50rpx;
		height: 50rpx;
		margin-right: 12rpx;
	}

	.btn_right image {
		width: 50rpx;
		height: 50rpx;
		margin-right: 12rpx;
	}
</style>
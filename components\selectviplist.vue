<template>
	<view>
		<u-radio-group v-model="isCheck" @change="radioGroupChange" active-color="#FF6684">
			<view class="listbox" v-for="(item,index) in list" :key="index" @click="clickItem(index,item)">
				<view class="iteimg">
					<image :src="item.userImg ? item.userImg.split(',')[0] : '../static/logo.png'" mode=""></image>
				</view>
				<view class="rightcont">
					<view class="usrbname">{{ item.realName || " " }}
						<image src="../static/images/my/rzicon.png"></image>
					</view>
					<view class="labl">
						<view class="sexicon" v-if="item.sex == 1">
							<u-icon name="man" color="#FFFFFF"></u-icon>
							{{ item.age }}岁
						</view>
						<view class="sexicons" v-if="item.sex == 2">
							<u-icon name="woman" color="#FFFFFF"></u-icon>
							{{ item.age }}岁
						</view>
						<view class="">
							{{ item.locationCity }}
							/{{ item.locationCounty }}
						</view>
					</view>
					<view class="tit">{{ item.feelingAngle || "" }}</view>
				</view>
				<u-radio @change="radioChange" :name="item.userId"></u-radio>
			</view>
		</u-radio-group>
	</view>
</template>

<script>
	export default {
		props: {
			//列表
			list: {
				type: Array,
				default: []
			},
			selectUserId: {
				type: [Number, String],
				default: 0
			}

		},
		computed: {
			// mutatedUserId() {
			// 	// Your mutation logic here
			// 	// For example, let's say you want to toggle the value
			// 	return this.selectUserId;
			// },
			
		},
		created() {
			// this.isCheck = this.selectUserId
		},
		data() {
			return {
				isCheck: ''
			}
		},
		methods: {
			clickItem(ind, item) {
				this.$emit('click', {
					index: ind,
					item: item
				});
			},
			radioGroupChange(e) {
				console.log(e);
			},
			radioChange(e) {
				console.log(e);
			}
		}
	}
</script>

<style lang="less">
	
	.listbox {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx 0;
		padding: 20rpx;
		display: flex;
		align-items: center;
		width: 690rpx;

		.iteimg {
			width: 200rpx;
			height: 200rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}
		}

		.rightcont {
			flex: 1;
			// height: 200rpx;
			// display: flex;
			// align-items: center;
			// flex-wrap: wrap;
			// align-content: space-between;
			margin-left: 18rpx;

			.usrbname {
				display: flex;
				align-items: center;

				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #292929;

				image {
					width: 40rpx;
					height: 40rpx;
					margin-left: 10rpx;
				}
			}

			.labl {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
				margin: 20rpx 0 10rpx;

				.sexicon {
					background: #38CAFF;
					border-radius: 10rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					padding: 4rpx 10rpx;
					margin-right: 10rpx;
				}

				.sexicons {
					background: #edbef3;
					border-radius: 10rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					padding: 4rpx 10rpx;
					margin-right: 10rpx;
				}
			}

			.tit {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				text-align: left;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 3;
				overflow: hidden;
			}
		}

		.btns {
			width: 163rpx;
			height: 69rpx;
			background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
			border-radius: 10rpx;
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;

		}
	}
</style>
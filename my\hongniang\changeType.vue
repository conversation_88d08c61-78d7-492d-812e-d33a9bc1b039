<template>
	<div class="changeType-page">
		<u-navbar title=" " :is-back="true" :background="background" :border-bottom="false"></u-navbar>


		<view class="box">
			<view class="zi2">hi，很高兴见到你</view>
			<view class="zi1">请问你想？</view>
			<image class="xin" src="../static/changeType/xin.png" />
			<view class="box1 ">
				<view class="type " @click="sendRecord">
					<image src="../static/changeType/active.png" mode="scaleToFill"
						style="width: 172rpx;height:179rpx" />
					<view class="flex-sub " style="margin-left: 60rpx;">
						<view>
							<view>发动态</view>
							<view class="tit">分享灵感和日常，记录美好生活</view>
						</view>
					</view>
				</view>
				<view class="type margin-top-sm" @click="sendUser">
					<image src="../static/changeType/vip.png" mode="scaleToFill" style="width: 185rpx;height:132rpx" />
					<view class="flex-sub " style="margin-left: 50rpx;">
						<view>
							<view>发布会员信息</view>
							<view class="tit">发布信息，找到志同道合的朋友</view>
						</view>
					</view>
				</view>
			</view>
		</view>

	</div>
</template>

<script>
	import Vue from 'vue';
	export default Vue.extend({
		components: {},
		data() {
			return {
				background: {
					backgroundColor: '#FFF7FA',
					// backgroundImage: 'linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)'
				},

			}
		},
		computed: {},
		methods: {
			sendUser() {
				// uni.navigateTo({
				//     url: '/my/hongniang/changeUser'
				// })
				uni.navigateTo({
					url: '/my/hongniang/publishUser'
				})

			},
			sendRecord() {
				uni.navigateTo({
					url: '/my/hongniang/releaseone'
				})
			}
		},
		watch: {},

		// 页面周期函数--监听页面加载
		onLoad() {},
		// 页面周期函数--监听页面初次渲染完成
		onReady() {},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户下拉动作
		// onPullDownRefresh() { uni.stopPullDownRefresh(); },
		// 页面处理函数--监听用户上拉触底
		// onReachBottom() {},
		// 页面处理函数--监听页面滚动(not-nvue)
		// onPageScroll(event) {},
		// 页面处理函数--用户点击右上角分享
		// onShareAppMessage(options) {},
	})
</script>

<style scoped lang="scss">
	@import "./main.scss";

	page {
		background: #FFF7FA;
	}

</style>
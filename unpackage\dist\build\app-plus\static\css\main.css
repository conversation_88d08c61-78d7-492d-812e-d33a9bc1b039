
.content {
	display: flex;
	flex-direction: column;
	justify-content:center;
	/* margin-top: 128upx; */
}

/* 头部 logo */
.header {
	width:161upx;
	height:161upx;
	box-shadow:0upx 0upx 60upx 0upx rgba(0,0,0,0.1);
	border-radius:50%;
	background-color: #000000;
	margin-top: 128upx;
	margin-bottom: 72upx;
	margin-left: auto;
	margin-right: auto;
}
.header image{
	width:161upx;
	height:161upx;
	border-radius:50%;
}
/* 主体 */
.main1 {
	display: flex;
	flex-direction: column;
	padding-top: 200upx;
	padding-left: 70upx;
	padding-right: 70upx;
}
/* 主体 */
.main {
	display: flex;
	flex-direction: column;
	padding-top: 32upx;
	padding-left: 70upx;
	padding-right: 70upx;
}
.tips {
	color: #999999;
	font-size: 28upx;
	margin-top: 64upx;
	margin-left: 48upx;
}

/* 其他登录方式 */
.other_login{
	z-index: 88;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	margin-top: 60upx;
	text-align: center;
}
.login_icon{
	border: none;
	font-size: 64upx;
	margin: 0 64upx 0 64upx;
	color: rgba(0,0,0,0.7)
}
.wechat_color{
	color: #83DC42;
}
.weibo_color{
	color: #F9221D;
}
.github_color{
	color: #24292E;
}

/* 底部 */
.footer{
	text-align: center;
	display: flex;
	flex-direction: row;
	justify-content: center;
	font-size: 14px;
	color: #0055b8;
	height:20px;
	line-height: 20px;
}
/* 底部 */
.register_footer{
	margin-left: 74upx;
	width: 80%;
	margin-top: 32upx;
	position: absolute;
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	font-size: 28upx;
	color: rgba(0,0,0,0.7);
	text-align: center;
	height: 40upx;
	line-height: 40upx;
}
.back-btn {
	position: absolute;
	left: 40upx;
	z-index: 9999;
	padding-top: var(--status-bar-height);
	top: 60upx;
	font-size: 40upx;
	color: #000000;
}
.footer text{
	font-size: 24upx;
	margin-left: 15upx;
	margin-right: 15upx;
}
.my_top{
	margin-bottom: 12upx;
	width: 100%;
	height: 200upx;
	background:#97582B;
}

<template>
    <div class="page" :class="{show: render}">
        <div v-if="!targetUser.delFlag" class="content-user" @click="toUser()">
            <img class="content-user-head" mode="aspectFill" :src="targetHead">
            <div class="content-user-right">
                <p class="content-user-title">
                    <span class="content-user-nickname">
                        {{targetUser.nickname||'游客用户'}}
                    </span>
                </p>
                <p class="content-user-info">
                    <span v-if="targetUser.birthYear">{{targetUser.birthYear}}年</span>
                    <span v-if="targetUser.height">/{{targetUser.height}}cm</span>
                    <span v-if="targetUser.eduLevel">/{{targetUser.eduLevelText}}</span>
                    <span v-if="targetUser.incomeLevel">/{{targetUser.incomeLevelText}}</span>
                </p>
            </div>
        </div>
        <div v-if="targetUser.delFlag" class="content-input-logout">
            对方账号已注销,你来晚了
        </div>
        <div v-else class="content-input" :style="{'padding-bottom':isIphoneX?'68rpx':'20rpx'}">
            <input v-model="content" @focus="focus = true" :focus="focus" @confirm="content && send()" class="content-input-text" type="text" :hold-keyboard="true" :confirm-hold="true" confirm-type="发送" :cursor-spacing="10" :always-embed="true" placeholder="输入新消息...">
            <div class="content-input-button" :class="{'content-input-button-fill': content.trim()}" @click="content.trim() && send()">发送</div>
        </div>
        <scroll-view @scrolltolower="next()" :scroll-into-view="endUuid" :scroll-with-animation="false" class="scroll" :scroll-y="true" :enable-flex="true" :enable-back-to-top="false">
            <div class="content-list" @touchstart="focus = false" :style="targetUser.delFlag?'padding-bottom:0':''">
                <div class="content-list-item content-list-tips" v-if="isEnd">
                    <p>安全提示</p>
                    聊天过程中，谨防网络诈骗，凡是涉及到金钱往来都是骗子！谨慎交换微信、手机号码、qq等私人联系方式！
                </div>
                <!-- <img src=""></p> -->
                <div class="content-list-item" :id="item.recordUuid" v-for="(item,i) in records" :key="item.recordUuid">
                    <p class="content-list-item-time" v-if="(!i && isEnd) || item.createTime - records[i-1].createTime > 60000">{{item.timeText}}</p>
                    <div class="content-list-item-main" :style="{'flex-direction': item.startSendFlag?'row-reverse':'row'}">
                        <img class="content-list-item-head" @click="!item.startSendFlag && toUser()" mode="aspectFill" :src="item.startSendFlag?head:targetHead">
                        <div style="width: 28rpx;"> </div>
                        <p class="content-list-item-text" :class="item.startSendFlag?'r':'l'">
                            {{item.content}}
                        </p>
                    </div>
                </div>
            </div>
        </scroll-view>
        <p class="scroll-cover"></p>
    </div>
</template>
<script type="text/javascript">
    import * as api from '&/index.js'
    import * as tools from '~utils/tools'
    import * as socket from '~utils/socket.js'
    export default {
        data() {
            return {
                render: 0,
                // 对方信息
                targetUser: {},
                // 聊天记录
                records: [],
                pageSize: 15,
                pageNum: 1,
                isEnd: 0,
                // 自动滚动到的id位置
                endUuid: '',
                content: '',
                // 是否获取焦点
                focus: false
            }
        },
        props: {
            'userId':{
                type: String,
                default: ''
            },
            'sessionUuid':{
                type: String,
                default: ''
            },
        },
        computed: {
            userinfo(){
                return this.$store.state.user.info;
            },
            targetHead(){
                return this.targetUser.head?this.TPG(this.targetUser.head):this.defaultHead
            },
            head(){
                return (this.userinfo.currentHead?this.TPG(this.userinfo.currentHead):this.userinfo.wxHead) || this.defaultHead
            },
            isIphoneX(){
                return this.$store.state.systeminfo.isIphoneX
            },
            defaultHead(){
                return this.$store.state.user.defaultHead
            },
        },
        onLoad(){
            this.pushData({
                resourceKey: 'za-Matchmaker_msg_message',
                accessPoint: 3,
                accessPointDesc: '会话页UV',
                extString1: this.userId
            });
            // 监听新消息，包括接收和自己发送的
            socket.onNews((context)=>{
                // 如果是当前用户则添加一条记录
                if(this.userId == context.uid || this.userId == context.targetUserId){
                    // 向页面添加一条记录
                    this.addRecords(context)
                }
            },this._uid)
            // 消息标记已读
            socket.readChat(this.sessionUuid,this.userId)
        },
        onUnload(){
            // 关闭监听消息
            socket.offNews(this._uid)
            // 消息标记已读
            socket.readChat(this.sessionUuid,this.userId)
        },
        onCreatedLogin() {
            this.getUserInfo()
            this.getList()
        },
        methods: {
            // 发送消息
            async send(){
                let imReset = 0;
                if(this.records.length < 1){
                    imReset = 1;
                }
                const content = this.content
                this.content = ''
                // 生成唯一uuid
                const uuid = socket.guid()
                // 发送消息
                let item = await this.addChat(content,uuid)
                // 走消息通知逻辑、因为渲染都在通知逻辑里面
                socket.msgBusiness(item)
                // 如果是第一条聊天记录则刷新列表
                setTimeout(()=>{
                    //触发会话列表刷新
                    this.$store.state.im.resetCallback()
                },1000)
            },
            // 添加一条记录
            addRecords(item){
                this.records.push(item)
                // this.endUuid = item.recordUuid
            },
            // 下一页
            next: tools.debounce(function(e){
                if(this.isEnd){
                    // this.wxapi.toast('没有更多啦')
                    return
                }
                this.pageNum ++;
                this.getList();
            },500,false),
            getList(){
                // 暂存页数，防止多次触发和异步造成的作用域问题
                let pageNum = this.pageNum
                this.api.request({
                    url: api.chatRecord,
                    method: 'get',
                    data: {
                        pageNum: pageNum,
                        pageSize: this.pageSize,
                        targetUserId: this.userId,
                        sessionUuid: this.sessionUuid
                    }
                }).then((res) => {
                    if (res.code == 200 && res?.data) {
                        let data = res.data,
                            records = data.records;
                        // 首次覆盖、后续追加
                        if(records.length){
                            records.reverse()
                            if(pageNum == 1){
                                this.records = records
                            }else{
                                this.records = [].concat(records,this.records);
                            }
                        }
                        if(records.length < this.pageSize){
                            // 结束
                            this.isEnd = 1
                        }
                    } else {
                        this.wxapi.toast(res.msg);
                    }
                });
            },
            getUserInfo(){
                // 暂存页数，防止多次触发和异步造成的作用域问题
                this.api.request({
                    url: api.targetUserInfo,
                    method: 'get',
                    data: {
                        targetUserId: this.userId
                    }
                }).then((res) => {
                    if (res.code == 200 && res?.data) {
                        let data = res.data;
                        this.targetUser = data;
                        wx.setNavigationBarTitle({
                          title: data.nickname
                        })
                        this.render = 1
                    } else {
                        this.wxapi.toast(res.msg);
                    }
                })
            },
            // 跳转用户信息
            toUser(){
                this.pushData({
                    resourceKey: 'za-Matchmaker_msg_message',
                    accessPoint: 4,
                    accessPointDesc: '点击对话页顶部资料',
                    extString1: this.userId
                });
                uni.navigateTo({
                    url: `/pages/user/user_info/index?from=chat&userId=${this.userId}`
                })
            },
            // 发送消息告知服务器
            addChat(content,uuid){
                return new Promise((resolve,reject)=>{
                    this.api.request({
                        url: api.addChat,
                        method: 'post',
                        data: {
                            content: content,
                            sessionUuid: this.sessionUuid,
                            recordUuid: uuid,
                            targetUserId: this.userId,
                            sessionType: 2,
                            contentType: 1,
                        }
                    }).then((res) => {
                        if (res.code == 200 && res?.data) {
                            let data = res.data
                            // 发送im消息
                            socket.sendChat(this.userId,data.content,data.sessionUuid,data.recordUuid)
                            return resolve(res.data)
                        } else {
                            this.wxapi.toast(res.msg)
                        }
                    })
                })
            },
        },
  }
</script>
<style lang="scss" scoped>
.page{
    background-color: $ColorGreyScale6;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition-duration: .3s;
}
.content{
}
.scroll-content{
    height: 100%;
}
.scroll{
    transform: rotate(180deg);
    height: 100%;
}
.content-user{
    position: fixed;
    width: 100%;
    z-index: 9;
    top: 0;
    padding: 36rpx 32rpx;
    display: flex;
    align-items: center;
    background-color: white;
    min-height: 192rpx;
}
.show{
    opacity: 1;
    visibility: visible;
}
.content-user-head{
    width: 120rpx;
    height: 120rpx;
    border-radius: 200rpx;
    flex-shrink: 0;
}
.content-user-right{
    margin-left: 20rpx;
    width: inherit;
    .content-user-title{
        font-size: 32rpx;
        font-weight: 800;
        color: $ColorGreyScale1;
        line-height: 32rpx;
        position: relative;
        >.content-user-nickname{
            @include text-overflow();
            display: inline-block;
            max-width: 75%;
        }
    }
    .content-user-title::after{
        content: '';
        display: inline-block;
        position: absolute;
        width: 32rpx;
        height: 32rpx;
        background: url(https://photo.zastatic.com/images/common-cms/it/20211124/1637751834950_812104_t.png) no-repeat center center / 32rpx 32rpx;
    }
    .content-user-info{
        margin-top: 20rpx;
        font-size: 24rpx;
        color: $ColorGreyScale2;
    }
}
.content-list{
    padding: 160rpx 0 192rpx 0;
    min-height: 100%;
    display: flex;
    flex-direction: column-reverse;
    .content-list-item{
        transform: rotate(180deg);
        margin: 22rpx 0;
        padding: 0 32rpx;
        .content-list-item-time{
            width: 100%;
            text-align: center;
            font-size: 26rpx;
            font-weight: 400;
            color: #8D9299;
            line-height: 28rpx;
            padding-bottom: 40rpx;
        }
        .content-list-item-main{
            display: flex;
        }
        .content-list-item-head{
            width: 90rpx;
            height: 90rpx;
            border-radius: 200rpx;
            background-color: white;
        }
        .content-list-item-text{
            max-width: 498rpx;
            background-color: white;
            border-radius: 12rpx;
            padding: 24rpx;
            position: relative;
            word-break: break-word;
        }
        .content-list-item-text.l::after{
            content: '';
            position: absolute;
            top: 32rpx;
            width: 25rpx;
            height: 25rpx;
            left: -12rpx;
            background-color: white;
            border-radius: 4rpx;
            transform: rotate(45deg);
        }
        .content-list-item-text.r::after{
            content: '';
            position: absolute;
            top: 32rpx;
            width: 25rpx;
            height: 25rpx;
            right: -12rpx;
            background-color: white;
            border-radius: 4rpx;
            transform: rotate(45deg);
        }
    }
    .content-list-tips{
        text-align: center;
        font-size: 24rpx;
        color: #878A98;
        line-height: 28rpx;
        display: block;
        margin: 32rpx 0;
        p{
            height: 28rpx;
            font-size: 28rpx;
            font-weight: 800;
            color: $ColorGreyScale1;
            line-height: 28rpx;
            margin-bottom: 20rpx;
        }
    }
}
.content-input{
    background-color: white;
    position: fixed;
    z-index: 9;
    bottom: 0;
    width: 100%;
    // height: 112rpx;
    padding: 20rpx 0 68rpx 0;
    display: flex;
    .content-input-text{
        width: 582rpx;
        height: 72rpx;
        line-height: 72rpx;
        display: inline-block;
        border: 2rpx solid #E0E0E0;
        border-radius: 8rpx;
        padding: 0 24rpx;
        color: #000019;
        font-size: 24rpx;
        box-sizing: border-box;
        margin-left: 20rpx;
    }
    .content-input-button{
        display: inline-block;
        width: 116rpx;
        height: 72rpx;
        line-height: 72rpx;
        text-align: center;
        border-radius: 12rpx;
        font-size: 28rpx;
        color: white;
        margin-left: 20rpx;
        background-color: #EBEDF3;
        transition-duration: .3s;
    }
    .content-input-button-fill{
        background: #FF489D;
    }
}
.scroll-cover{
    width: 8rpx;
    height: 100%;
    position: fixed;
    left: 6rpx;
    top: 192rpx;
    background-color: #F5F6F9;
}
.content-input-logout{
    background-color: white;
    position: fixed;
    color:#878A98;
    z-index: 9;
    width: 100%;
    bottom: 0;
    height:112rpx;
    padding:0;
    font-size: 32rpx;
    line-height: 32rpx;
    @include center();
}
</style>

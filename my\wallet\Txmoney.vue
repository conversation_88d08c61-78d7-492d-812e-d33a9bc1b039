<template>
	<view>
		<!-- <view class="flex text-center text-white text-lg bg">
			<view class="title_btn flex-sub bg" @click="cut(1)" :class="title_color==1?'bgCol2': ''">我的余额
			</view>
			<view class="title_btn flex-sub bg" @click="cut(2)" :class="title_color==2?'bgCol2': ''">我的收益
			</view>
		</view> -->
		<view class="padding">
			<view class="flex  justify-between radius bg ">
				<view class=" u-flex u-p-l-30 u-p-t-30 u-p-b-30">
					<view class="u-m-r-20">
						<u-avatar :src="avatar" size="80"></u-avatar>
					</view>
					<view class="u-flex-1 ">
						<view class="u-font-16 text-bold">{{money}}元</view>
						<view class="u-font-14 u-m-t-10">可用于平台消费</view>
					</view>
				</view>
				<view class="margin-right margin-top-sm" @click="goNav('/my/wallet/mymoneydetail')">钱包明细
				</view>
			</view>
			<view class="margin-top radius bg flex justify-between flex-wrap padding-lr padding-bottom">
				<view v-for="(item,index) in wallet" :key='index'
					class="flex justify-between align-center padding-sm radius margin-top"
					style="color: #1E1F31;background-color: #F7F7F7; width: 46%;border: 1px solid #F7F7F7;"
					@click="active(item,index)" :class="wallIndex==index?'active':''">
					<view>
						{{item.money}}元
						<!-- <view class="" style="font-size: 24rpx;" v-if="item.tbCoupon">
							赠送{{item.couponNum}}张{{item.tbCoupon.money}}元优惠劵
						</view> -->
					</view>
				</view>
			</view>
			<view class="margin-top" style="width: 100%;padding-left: 40rpx;">
				充值后仅可用于消费，不支持退款。
			</view>
			<view class="bg margin-top padding-lr radius">
				<view>
					<view style="display: flex;height: 100upx;align-items: center;padding: 20upx 0;"
						v-for="(item,index) in openLists" :key='index'>
						<image :src="item.image" style="width: 55upx;height: 55upx;border-radius: 50upx;"></image>
						<view style="font-size: 30upx;margin-left: 20upx;width: 70%;">{{item.text}}
						</view>
						<radio-group name="openWay" style="margin-left: 20upx;" @tap='selectWay(item)'>
							<label class="tui-radio">
								<radio color="#5074FF" :checked="openWay === item.id ? true : false" />
							</label>
						</radio-group>
					</view>
				</view>
			</view>

			<view class="btn" @click="pay">确认支付</view>
		</view>
		<view class="flex justify-around margin-top text-white">
		</view>



	</view>
</template>

<script>
	export default {
		data() {
			return {
				title_color: 1,
				money: 0,
				avatar: '',
				Profit: 0,
				openLists: [{
					image: '../static/zhifubao.png',
					text: '支付宝',
					id: 1
				}, {
					image: '../static/icon_weixin.png',
					text: '微信',
					id: 2
				}],
				openWay: 1,
				wallet: [],
				wallIndex: 0,
				price: 0,
				moneyNum: null,

				charge: 0, //提现手续费
				maxMoney: 0, //最高提现额度
				minMoney: 0, //最低提现额度
				ratio: '', //金元比例
				placeholder: '',
				sp: 0,
				rechargeId: ''
			}
		},
		onLoad() {
			this.avatar = uni.getStorageSync('avatar')
			// #ifdef APP-PLUS
			this.openLists = [{
				image: '../static/zhifubao.png',
				text: '支付宝',
				id: 1
			}, {
				image: '../static/icon_weixin.png',
				text: '微信',
				id: 2
			}];
			this.openWay = 1;
			// #endif

			// #ifdef MP-WEIXIN
			this.openLists = [{
				image: '../static/icon_weixin.png',
				text: '微信',
				id: 2
			}];
			this.openWay = 2;
			// #endif

			// #ifdef H5
			let ua = navigator.userAgent.toLowerCase();
			if (ua.indexOf('micromessenger') !== -1) {
				//公众号是否自动登录  333
				this.$Request.get('/app/common/type/333').then(res => {
					if (res.data && res.data.value && res.data.value == '是') {
						this.openLists = [{
							image: '../static/zhifubao.png',
							text: '支付宝',
							id: 1
						}, {
							image: '../static/icon_weixin.png',
							text: '微信',
							id: 2
						}];
						this.openWay = 2;
					} else {
						this.openLists = [{
							image: '../static/zhifubao.png',
							text: '支付宝',
							id: 1
						}];
						this.openWay = 1;
					}
				})
			} else {
				this.openLists = [{
					image: '../static/zhifubao.png',
					text: '支付宝',
					id: 1
				}];
				this.openWay = 1;
			}
			// #endif
		},
		onShow() {
			this.getMoney();
			this.getUserInfo();
			this.getMoneyList();
		},
		methods: {
			getMoneyList() {
				this.$Request.getT("/app/rechargeConfig/getConfigInfoList").then(res => {
					if (res.code == 0) {
						this.wallet = res.data.records
						this.price = this.wallet[0].money
						this.rechargeId = this.wallet[0].rechargeId
					}
				});
			},
			getUserInfo() {
				this.$Request.getT("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						this.avatar = res.data.avatar ? res.data.avatar : '../../static/image/logo.png'
						uni.setStorageSync('avatar', res.data.avatar)
					}
				});
			},
			cut(e) {
				this.title_color = e
			},
			goNav(url) {
				uni.navigateTo({
					url
				})
			},
			active(e, index) {
				this.wallIndex = index
				this.price = e.money
				this.rechargeId = e.rechargeId
				// this.wallet = 
				// this.wallet.forEach(res => {
				// 	if (res.id == e.id) {
				// 		res.isSelect = true
				// 		this.thisSelect = e
				// 	} else {
				// 		res.isSelect = false
				// 	}
				// })
			},
			// 我的余额
			getMoney() {
				this.$Request.getT("/app/userMoney/selectMyMoney").then(res => {
					if (res.code == 0 && res.data) {
						console.log(res.data.money)
						this.money = res.data.money
					}
				});
			},
			selectWay: function(item) {
				this.openWay = item.id;
			},
			//h5支付
			payH5() {
				let that = this

				if (this.openWay == 1) { //h5支付宝
					let data = {
						money: that.price,
						classify: 5
						// rechargeId: that.rechargeId
					}
					this.$Request.postT("/app/userMoney/rechargeMoney", data).then(res => {
						if (res.code == 0) {
							const div = document.createElement('div')
							div.innerHTML = res.data //此处form就是后台返回接收到的数据
							document.body.appendChild(div)
							document.forms[0].submit()
							uni.hideLoading()
						} else {
							uni.showToast({
								icon: 'none',
								title: '支付失败!'
							});
						}
					});
				} else { //h5微信支付
					let data = {
						money: that.price,
						classify: 3
						// rechargeId: that.rechargeId
					}
					this.$Request.postT("/app/userMoney/rechargeMoney", data).then(res => {
						if (res.code == 0) {
							uni.hideLoading()
							that.callPay(res.data);
						} else {
							uni.showToast({
								icon: 'none',
								title: '支付失败!'
							});
						}
					});
				}
			},
			//app支付
			payApp() {
				let that = this

				if (this.openWay == 1) { //app支付宝
					let data = {
						money: that.price,
						classify: 4
						// rechargeId: that.rechargeId
					}
					that.$Request.postT("/app/userMoney/rechargeMoney", data).then(ret => {
						console.log(ret)
						that.isCheckPay(ret.code, 'alipay', ret.data);
					});
				} else { //app微信支付
					let data = {
						money: that.price,
						// rechargeId: that.rechargeId,
						classify: 1
					}
					that.$Request.postT("/app/userMoney/rechargeMoney", data).then(ret => {
						console.log(ret, 'retretretretretret')
						that.isCheckPay(ret.code, 'wxpay', JSON.stringify(ret.data));
					});
				}
			},
			//微信支付
			payMP(money) {
				let that = this
				let data = {
					money: that.price,
					// rechargeId: that.rechargeId,
					classify: 2
				}
				that.$Request.postT('/app/userMoney/rechargeMoney', data).then(ret => {
					uni.hideLoading()
					
					uni.requestPayment({
						provider: 'wxpay',
						timeStamp: ret.data.timestamp,
						nonceStr: ret.data.noncestr,
						package: ret.data.package,
						signType: ret.data.signType,
						paySign: ret.data.sign,
						success: function(suc) {
							console.log('success:' + JSON.stringify(suc));
							uni.showToast({
								title: '支付成功',
								icon: 'success'
							})
							setTimeout(d => {
								uni.navigateBack(1)
							}, 1000);
						},
						fail: function(err) {
							console.log('fail:' + JSON.stringify(err));
							uni.showToast({
								title: '支付失败',
								icon: 'none'
							})
						}
					});
				});
			},
			pay() {
				// console.log(this.thisSelect)
				// let data = {

				// }this.price
				uni.showLoading({
					title: '支付中...'
				});
				// H5
				// #ifdef H5
				this.payH5()
				// #endif
				//APP
				// #ifdef APP
				this.payApp()
				// #endif
				//小程序
				// #ifdef MP-WEIXIN
				this.payMP()
				// #endif
			},
			callPay: function(response) {
				console.log(response)
				if (typeof WeixinJSBridge === "undefined") {

					if (document.addEventListener) {
						document.addEventListener('WeixinJSBridgeReady', this.onBridgeReady(response), false);
					} else if (document.attachEvent) {
						document.attachEvent('WeixinJSBridgeReady', this.onBridgeReady(response));
						document.attachEvent('onWeixinJSBridgeReady', this.onBridgeReady(response));
					}
				} else {
					console.log(1)
					this.onBridgeReady(response);
				}
			},
			onBridgeReady: function(response) {
				let that = this;
				if (!response.package) {
					return;
				}
				console.log(response, '++++++++')
				WeixinJSBridge.invoke(
					'getBrandWCPayRequest', {
						"appId": response.appid, //公众号名称，由商户传入
						"timeStamp": response.timestamp, //时间戳，自1970年以来的秒数
						"nonceStr": response.noncestr, //随机串
						"package": response.package,
						"signType": response.signType, //微信签名方式：
						"paySign": response.sign //微信签名
					},
					function(res) {
						console.log(res, '/*-/*-/*-')
						if (res.err_msg === "get_brand_wcpay_request:ok") {
							// 使用以上方式判断前端返回,微信团队郑重提示：
							//res.err_msg将在用户支付成功后返回ok，但并不保证它绝对可靠。
							uni.showLoading({
								title: '支付成功'
							});
							setTimeout(function() {
								uni.hideLoading();
							}, 1000);
						} else {
							uni.hideLoading();
						}
						WeixinJSBridge.log(response.err_msg);
					}
				);
			},
			isCheckPay(status, name, order) {
				if (status == 0) {
					this.setPayment(name, order);
				} else {
					uni.hideLoading();
					uni.showToast({
						title: '支付信息有误',
						icon: 'none'
					});
				}
			},
			setPayment(name, order) {
				console.log('*-*-*')
				uni.requestPayment({
					provider: name,
					orderInfo: order, //微信、支付宝订单数据
					success: function(res) {
						console.log(res)
						uni.hideLoading();
						uni.showLoading({
							title: '支付成功'
						});
					},
					fail: function(err) {
						console.log(err)
						uni.hideLoading();
					},
					complete() {
						uni.hideLoading();
					}
				});
			},
		}
	}
</script>

<style>
	page {
		background-color: #F7F7F7;
	}

	.bgCol2 {
		color: #5074FF;
	}

	.bg {
		background-color: #FFFFFF;
	}

	.active {
		border: 1px solid #5074FF !important;
		color: #5074FF !important;
		background: #FFFFFF !important;
	}

	.title_btn {
		height: 78upx;
		line-height: 78upx;
		/* background: #f7f7f7; */
	}

	.btn {
		width: 100%;
		height: 88upx;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
		border-radius: 44upx;
		text-align: center;
		line-height: 88upx;
		margin-top: 40upx;
		font-size: 28upx;
		color: #FFF;
	}
</style>
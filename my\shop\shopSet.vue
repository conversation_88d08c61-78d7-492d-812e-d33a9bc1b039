<template>
  <view class="shop-set-container">
    <!-- 顶部卡片 -->
    <view class="top-card">
      <img
        class="top-card-img"
        src="https://photo.zastatic.com/images/common-cms/it/20250527/1748346190334_868461.png"
        alt=""
      />
    </view>

    <!-- 店铺信息 -->
    <view
      :class="isPayShop ? 'section shop-info disabled' : 'section shop-info'"
    >
      <view class="form"
        ><view class="form-item">
          <view class="label"> <text class="required">*</text>门店名称： </view>
          <input
            class="input"
            type="text"
            placeholder="请输入"
            v-model="shopInfo.name"
            placeholder-class="placeholder"
          />
        </view>
        <view class="form-item">
          <view class="label"> <text class="required">*</text>门店地址： </view>
          <uni-data-picker
            v-model="shopInfo.region"
            :localdata="regionData"
            @change="changeRegion"
            :map="{
              text: 'label',
              value: 'value',
              children: 'children',
            }"
          /> </view
      ></view>
    </view>

    <!-- 实体店婚介权益 -->
    <view class="section shop-benefits">
      <img
        class="shop-benefits-img"
        src="https://photo.zastatic.com/images/common-cms/it/20250527/1748346195961_755217.png"
        alt=""
      />
    </view>

    <!-- 支付方式 -->
    <view class="section payment" v-if="!isPayShop">
      <view class="section-title">支付方式</view>
      <view class="payment-item" @click="togglePayment">
        <view class="payment-left">
          <image
            src="https://photo.zastatic.com/images/common-cms/it/20250527/1748341227691_317879_t.png"
            class="payment-icon"
            mode="aspectFit"
          ></image>
          <text class="payment-text">支付宝</text>
        </view>
        <view class="payment-right">
          <view class="radio" :class="{ active: isAlipaySelected }">
            <view class="radio-inner" v-if="isAlipaySelected"></view>
          </view>
        </view>
      </view>
    </view>
    <!-- 立即开通按钮 -->
    <view class="bottom-btn" v-if="!isPayShop">
      <button class="open-btn" @click="openShop">立即开通</button>
    </view>
  </view>
</template>

<script>
import { region } from "./region.js";
console.log(region);
export default {
  data() {
    return {
      shopInfo: {
        name: "",
        address: "",
        region: null,
      },
      regionData: region,
      isAlipaySelected: true,
      isPayShop: false,
      benefits: [
        {
          icon: "/static/benefit1.png",
          text: "提供企业资源",
          color: "#8B7CF6",
        },
        {
          icon: "/static/benefit2.png",
          text: "运营方案支持",
          color: "#6366F1",
        },
        {
          icon: "/static/benefit3.png",
          text: "免费业务培训",
          color: "#8B7CF6",
        },
        {
          icon: "/static/benefit4.png",
          text: "提供市场策略",
          color: "#6366F1",
        },
        {
          icon: "/static/benefit5.png",
          text: "提供活动策划",
          color: "#8B7CF6",
        },
        {
          icon: "/static/benefit6.png",
          text: "帮助产户公关",
          color: "#6366F1",
        },
      ],
    };
  },
  onLoad() {
    this.isPayShop = uni.getStorageSync("isPayShop") || false;
  },
  methods: {
    togglePayment() {
      this.isAlipaySelected = !this.isAlipaySelected;
    },
    openShop() {
      if (!this.shopInfo.name.trim()) {
        uni.showToast({
          title: "请输入门店名称",
          icon: "none",
        });
        return;
      }
      if (!this.shopInfo.region) {
        uni.showToast({
          title: "请选择门店地址",
          icon: "none",
        });
        return;
      }

      // 这里处理开通店铺的逻辑
      uni.showModal({
        title: "确认开通",
        content: `确认支付26800元开通实体店吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用支付接口
            this.handlePayment();
          }
        },
      });
    },
    changeRegion(e) {
      const value = e.detail.value;
      this.shopInfo.address = value.map((item) => item.text).join("");
      this.shopInfo.addressData = value.map((item) => item.value);
    },
    handlePayment() {
      let userId = uni.getStorageSync("userId");
      console.log(userId, 33);
      // 支付逻辑
      uni.showLoading({
        title: "支付中...",
      });
      this.$Request
        .post(
          "/app/scShop/addShop",
          {
            userId,
            shopName: this.shopInfo.name,
            shopAddress: this.shopInfo.address,
            province: this.shopInfo.addressData[0] || null,
            city: this.shopInfo.addressData[1] || null,
            district: this.shopInfo.addressData[2] || null,
          },
          "application/json"
        )
        .then((res) => {
          uni.hideLoading();
          uni.showToast({
            title: "支付成功",
            icon: "success",
          });
          uni.setStorageSync("isPayShop", true);
          setTimeout(() => {
            uni.reLaunch({
              url: "/my/pay/paySuccess",
            });
          }, 1500);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.shop-set-container {
  min-height: 110vh;
  background-color: #f5f5f5;
  position: relative;
}

.top-card {
  width: 100%;
  height: 540rpx;
  position: absolute;
  top: 0;
  left: 0;
}

.top-card-img {
  width: 100%;
  height: 100%;
}
.shop-info {
  top: 540rpx;
}
.payment {
  top: 1244rpx;
}
.section {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  position: absolute;
  left: 0rpx;
  width: 100%;
  background: #fff;

  &.disabled {
    opacity: 0.6;
    background: #f8f9fa;
    pointer-events: none;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.05);
      border-radius: 16rpx;
      z-index: 1;
    }

    .form {
      background: #e9ecef;

      .input {
        background: #e9ecef;
        color: #6c757d;
        cursor: not-allowed;
      }

      .label {
        color: #6c757d;
      }
    }
  }

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 40rpx;
    text-align: center;
    position: relative;

    &::before,
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      width: 80rpx;
      height: 2rpx;
      background: #e5e5e5;
    }

    &::before {
      left: 0;
    }

    &::after {
      right: 0;
    }
  }
}
.form {
  background: #f2f3ff;
  padding: 20rpx 30rpx;
  border-radius: 16rpx;
}
.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  &:last-child {
    margin-bottom: 0;
  }

  .label {
    width: 170rpx;
    font-size: 28rpx;
    color: #666;

    .required {
      color: #ff4757;
      margin-right: 8rpx;
    }
  }

  .input {
    flex: 1;
    height: 74rpx;
    background: #f8f9fa;
    border-radius: 12rpx;
    padding: 0 24rpx;
    font-size: 28rpx;
    color: #333;
    background: none;
    border: 1rpx solid #e5e5e5;
  }

  .placeholder {
    color: #999;
  }
}

.shop-benefits {
  width: 100%;
  height: 428rpx;
  top: 818rpx;
  padding: 0rpx;
}
.shop-benefits-img {
  width: 100%;
  height: 100%;
}
.payment-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;

  .payment-left {
    display: flex;
    align-items: center;

    .payment-icon {
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;
    }

    .payment-text {
      font-size: 32rpx;
      color: #333;
    }
  }

  .payment-right {
    .radio {
      width: 40rpx;
      height: 40rpx;
      border: 4rpx solid #ddd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        border-color: #6366f1;

        .radio-inner {
          width: 20rpx;
          height: 20rpx;
          background: #6366f1;
          border-radius: 50%;
        }
      }
    }
  }
}

.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;

  .open-btn {
    width: 100%;
    height: 88rpx;
    background: #8295ea;
    color: white;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;

    &::after {
      border: none;
    }
  }
}
</style>

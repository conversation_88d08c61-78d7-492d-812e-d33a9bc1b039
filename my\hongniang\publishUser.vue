<template>
	<view>

		<view class="usermain">
			<view class="padding-lr padding-top  title">认证信息</view>
			<!-- <view class="flex flex-wrap padding-lr padding-tb-sm"></view> -->
			<view class="usermain-item item-padding ">
				<view>真实姓名</view>
				<view>
					<view class="cu-form-group">
						<input type="nickname" :disabled="false" v-model="formData.realName" placeholder="请填写真实姓名" />
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>手机号</view>
				<view>
					<view class="cu-form-group">
						<input type="tel" :disabled="false" maxlength="11" v-model="formData.phone"
							placeholder="请填写手机号" />
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>身份证号</view>
				<view>
					<view class="cu-form-group">
						<input type="idcard" :disabled="false" maxlength="18" v-model="formData.idNumber"
							placeholder="请填写身份证号" @confirm="confirmCard" @input="confirmCard"/>
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding upimg">
				<view>身份证正面图片</view>
				<view class="flex flex-wrap padding-lr2 padding-tb-sm" @click="uploadImgs('idCardFront')">
					<image v-if="formData.idCardFront"
						:src="formData.idCardFront ? formData.idCardFront : '../../static/logo.png'" class=""
						style="width: 202rpx;height:202rpx;border-radius: 16rpx;"></image>
					<button v-else>
						<view class="huibox">
							<u-icon name="plus" color="#CCCCCC" size="60"></u-icon>
						</view>
					</button>
				</view>
			</view>
			<view class="usermain-item item-padding upimg">
				<view>身份证反面图片</view>
				<view class="flex flex-wrap padding-lr2 padding-tb-sm" @click="uploadImgs('idCardVerso')">
					<image v-if="formData.idCardVerso"
						:src="formData.idCardVerso ? formData.idCardVerso : '../../static/logo.png'" class=""
						style="width: 202rpx;height:202rpx;border-radius: 16rpx;"></image>
					<button v-else>
						<view class="huibox">
							<u-icon name="plus" color="#CCCCCC" size="60"></u-icon>
						</view>
					</button>
				</view>
			</view>


		</view>


		<view class="usermain">
			<view class="padding-lr padding-top  title">个人形象展示</view>
			<!-- <view class="flex flex-wrap padding-lr padding-tb-sm"></view> -->
			<view class="flex flex-wrap padding-lr padding-tb-sm">
				<view class="margin-right-sm flex flex-wrap ">
					<view v-for="(item, index) in userImglist" :key="index"
						style="position: relative;margin-right:10rpx">
						<image :src="item ? item : '../../static/logo.png'" class=""
							style="width: 200rpx;height:200rpx;border-radius: 16rpx;"></image>
						<view style="z-index: 9;position: absolute;top: -15rpx;right: -15rpx;"
							@click="removeImg(index, 1)">
							<u-icon name="close-circle-fill" color="#AC75FE" size="50rpx"></u-icon>
						</view>
					</view>
					<button @click="uploadImg" class="" v-if="userImglist.length <= 8"
						:class="userImglist>0?'margin-left-xs':''">
						<view class="huibox ">
							<u-icon name="plus" color="#CCCCCC" size="60"></u-icon>
						</view>
					</button>
				</view>

			</view>
			<view class="padding-lr flex align-center" style="font-weight: 400;font-size: 24rpx;padding-bottom: 20rpx;">
				<u-checkbox style="" v-model="checked" label-size="24">是否对外可见</u-checkbox>
			</view>
		</view>

		<view class="usermain">
			<view class="padding-lr padding-top  title">基本信息</view>
			<!-- <view class="usermain-item item-padding ">
                <view>姓名</view>
                <view>
                    <view class="cu-form-group">
                        <input type="nickname" :disabled="false" v-model="formData.realName" placeholder="请填写姓名" />
                    </view>
                </view>
            </view> -->
			<!-- <view class="usermain-item item-padding ">
				<view>手机号</view>
				<view>
					<view class="cu-form-group">
						<input type="number" :disabled="disabled" v-model="phone" placeholder="请填写手机号" />
					</view>
				</view>
			</view> -->
			<view class="usermain-item item-padding ">
				<view>年龄</view>
				<view>
					<view class="cu-form-group">
						<input type="number" v-model="formData.age" maxlength="2" :disabled="false"
							placeholder="请填写年龄" />
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>性别</view>
				<view>
					<view class="cu-form-group">
						<u-radio-group v-model="formData.sex">
							<u-radio shape="circle" :disabled="false" active-color="#FF6684" :name="1">男</u-radio>
							<u-radio shape="circle" :disabled="false" active-color="#FF6684" :name="2">女</u-radio>
						</u-radio-group>
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>生日</view>
				<view>
					<view class="cu-form-group">
						<u-input v-model="formData.birthday" placeholder="请选择生日" inputAlign="right" :disabled="true" @click="birthdayShow = true"/>
						<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
					</view>

				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>身高(CM)</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.userHeight" maxlength="3" placeholder="请输入身高" />
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>体重(kg)</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.userWeight" maxlength="3" placeholder="请输入体重" />
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>星座</view>
				<view class="flex align-center">
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.starSign" placeholder="请选择星座" :disabled="true"
							@click="starSignshow = true" />
					</view>
					<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>学历</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.education" placeholder="请选择学历" :disabled="true"
							@click="xlshow = true" />
						<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>职业</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.career" placeholder="请填写职业(选填)" />
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>公司</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.corporateName" placeholder="请填写公司(选填)" />
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>家乡</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="home" placeholder="请选择家乡地址" :disabled="true"
							@click="showhome = true" />
						<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>所在地</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="location" placeholder="请选择所在地" :disabled="true"
							@click="showlocation = true" />
						<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
					</view>
				</view>
			</view>


		</view>

		<view class="usermain">
			<view class="usermain-item item-padding ">
				<view>月收入</view>
				<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.income" placeholder="请填写月收入(选填)" :disabled="true"
							@click="incomeshow = true" />
						<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>婚姻状况</view>
				<view>
					<view class="cu-form-group">
						<u-radio-group v-model="formData.marriageStatus">
							<u-radio shape="circle" active-color="#FF6684" :name="1">未婚</u-radio>
							<u-radio shape="circle" active-color="#FF6684" :name="2">离异</u-radio>
						</u-radio-group>
					</view>
				</view>
			</view>
			<view class="usermain-item item-padding ">
				<view>有无房产</view>
				<!-- <view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.hasHouse" placeholder="请选择有无房产" />
						<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
					</view>
				</view> -->
				<view class="cu-form-group">
					<u-radio-group v-model="formData.hasHouse">
						<u-radio shape="circle" active-color="#FF6684" :name="1">有</u-radio>
						<u-radio shape="circle" active-color="#FF6684" :name="0">无</u-radio>
					</u-radio-group>
				</view>
			</view>
			<view class="usermain-item item-padding " style="border-bottom:none;">
				<view>购车情况</view>
				<!-- 	<view>
					<view class="cu-form-group">
						<u-input inputAlign="right" v-model="formData.vehicle" placeholder="请选择购房情况" />
						<image src="../../static/images/my/right_icon.png" style="width: 12rpx;height: 25rpx;"></image>
					</view>
				</view> -->
				<view class="cu-form-group">
					<u-radio-group v-model="formData.vehicle">
						<u-radio shape="circle" active-color="#FF6684" :name="1">有</u-radio>
						<u-radio shape="circle" active-color="#FF6684" :name="0">无</u-radio>
					</u-radio-group>
				</view>
			</view>
		</view>

		<view class="usermain">
			<view class="padding-lr padding-top  title">兴趣爱好</view>
			<view class="padding-lr-xs padding-bottom-xs">
				<u-input v-model="formData.interest" type="textarea" :auto-height="true" placeholder="多说几点，让TA更了解你！"
					:clearable="false" />
			</view>
		</view>
		<view class="usermain">
			<view class="padding-lr padding-top  title">关于我</view>
			<view class="padding-lr-xs padding-bottom-xs">
				<u-input v-model="formData.myIntro" type="textarea" :auto-height="true" placeholder="多说几点，让TA更了解你！"
					:clearable="false" />
			</view>
		</view>

		<view class="usermain">
			<view class="padding-lr padding-top  title">感情观</view>
			<view class="padding-lr-xs padding-bottom-xs">
				<u-input v-model="formData.feelingAngle" type="textarea" :auto-height="true" placeholder="走心填写，邂逅更适合的ta"
					:clearable="false" />
			</view>
		</view>
		<view class="usermain">
			<view class="padding-lr padding-top  title">心仪的TA</view>
			<view class="padding-lr-xs padding-bottom-xs">
				<u-input v-model="formData.idealAspect" type="textarea" :auto-height="true"
					placeholder="描绘出你心中理想的对象，说不定ta就在前方" :clearable="false" />
			</view>
		</view>
		<!-- 	<view class="usermain">
			<view class="padding-lr padding-top  title">微信号</view>
			<view class="padding-lr-xs padding-bottom-xs">
				<u-input v-model="formData.wxNumber" type="textarea" :auto-height="true" placeholder="作为对方获取联系你的方式"
					:clearable="false" />
			</view>
		</view> -->
		<view class="footer-btn">
			<view class="usermain-btn" @click="messagebtn()">确认发布</view>
		</view>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>

		<!-- 选择生日 -->
		<u-picker v-model="birthdayShow" mode="time" @confirm="confirmbirth"></u-picker>

		<!-- 学历 -->
		<u-select v-model="xlshow" :list="xllist" mode="single-column" @confirm="confirmxl"></u-select>

		<!-- 家乡 -->
		<u-picker mode="region" v-model="showhome" @confirm="confirmhome"></u-picker>

		<!-- 所在的 -->
		<u-picker mode="region" v-model="showlocation" @confirm="confirmlocation"></u-picker>
		<!-- 婚姻状况 -->
		<!-- <u-select v-model="marriageStatusshow" :list="marriageStatuslist" @confirm="confirmmarriageStatus"></u-select> -->

		<!-- 月收入 -->
		<u-select v-model="incomeshow" :list="incomelist" mode="single-column" @confirm="confirmincome"></u-select>

		<!-- 星座 -->
		<u-select v-model="starSignshow" :list="starSignlist" mode="single-column"
			@confirm="confirmstarSign"></u-select>
	</view>
</template>

<script>
	import configdata from '../../common/config.js';
	import {
		showLoading
	} from '../../common/queue.js';
	export default {
		data() {
			return {
				activeColor: "#FF6684",
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				phone: '',
				checked: true, //是否对外显示
				formData: {
					dataId: '',
					age: '', //年龄
					userImg: [], //个人形象照
					realName: '', //昵称
					sex: 1, //性别
					birthday: '', //生日
					userHeight: '', //身高
					userWeight: '', //体重
					education: '', //学历
					career: '', //职业
					corporateName: '', //公司名称
					homeProvince: '', //家乡省
					homeCity: '', //家乡市
					homeCounty: '', //家乡区
					starSign: '', //星座
					locationProvince: '', //所在地省
					locationCity: '', //所在地市
					locationCounty: '', //所在地区
					income: '', //月收入
					marriageStatus: 1, //婚姻状况
					hasHouse: 1, //有无房产
					vehicle: 1, //购车情况
					feelingAngle: '', //感情观
					interest: '', //兴趣爱好
					idealAspect: '', //心仪的ta
					myIntro: '', //关于我
					wxNumber: '', //微信号
					"phone": "", //手机号
					"idNumber": "", //证件号码
					"idCardFront": "", //身份证正面图片
					"idCardVerso": "", //身份证反面图片
				},
				userImglist: [], //个人形象照
				// sex: 1,
				birthdayShow: false,
				home: '', //家乡
				location: '', //所在地
				count: 1,
				xlshow: false,
				xllist: [],
				showhome: false,
				showlocation: false,
				// marriageStatuslist: [{
				// 	value: 1,
				// 	label: '未婚'
				// }, {
				// 	value: 2,
				// 	label: '离异',
				// }],
				// marriageStatusshow: false,
				incomelist: [],
				incomeshow: false,
				starSignshow: false,
				starSignlist: [],
				disabled: false,
				disableds: false

			};
		},
		onLoad(e) {
			// uni.showLoading({
			//     title: '加载中...'
			// })
			// this.$Request.getT('/app/common/type/188').then(res => {
			// 	if (res.code == 0) {
			// 		if (res.data && res.data.value) {
			// 			if (res.data.value == '是') {
			// 				this.disableds = true
			// 			} else {
			// 				this.disableds = false
			// 			}
			// 		}
			// 	}
			// });
			// this.getUserInfos()
			// this.getUserInfo()
			this.getxllist()
			this.getincomelist()
			this.getstarSign()

			// this.avatar = uni.getStorageSync('avatar')
		},
		methods: {
			confirmCard(e) {
				console.log(e.detail.value)
				this.getbridth(e.detail.value)
			},
			getBirthdayByIdCard(idCard) {
				// 身份证号码
				let birthDatePattern = /^(\d{6})(\d{8})(\d{3})(\d{1})$/;
				if (birthDatePattern.test(idCard)) {
					let matchResult = birthDatePattern.exec(idCard);
					return matchResult[2]; // 第二个分组是出生日期
				}
				return null;
			},
			getbridth(idCard) {
				// 示例身份证号

				let birthday = this.getBirthdayByIdCard(idCard);
				console.log(birthday);
				if (birthday) {
					let newbirthday = birthday.slice(0, 4) + '-' + birthday.slice(4, 6) + '-' + birthday.slice(6, 8)
					console.log(newbirthday);
					this.formData.birthday = newbirthday
				}

			},
			getUserInfos() {
				let userId = uni.getStorageSync('userId')
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						this.formData.realName = res.data.realName
						this.formData.age = res.data.age
						this.formData.sex = res.data.sex
						this.formData.birthday = res.data.birthday

					}
					uni.hideLoading();
				});


			},
			// 选择星座
			confirmstarSign(e) {
				this.formData.starSign = e[0].label
			},
			//获取星座
			getstarSign() {
				this.$Request.getT('/app/dict/getDictList?name=星座').then(res => {
					if (res.code == 0) {
						res.data.map(item => {
							item.label = item.value
						})
						this.starSignlist = res.data

					}
				});
			},
			//选择月收入
			confirmincome(e) {
				console.log(e)
				this.formData.income = e[0].label
			},
			getincomelist() { //月收入
				this.$Request.getT('/app/dict/getDictList?name=月收入').then(res => {
					if (res.code == 0) {
						res.data.map(item => {
							item.label = item.value
						})
						this.incomelist = res.data

					}
				});
			},
			//选择婚姻状况
			confirmmarriageStatus(e) {
				this.formData.marriageStatus = e[0].label
			},
			//选择所在的
			confirmlocation(e) {
				console.log(e)
				this.formData.locationProvince = e.province.label
				this.formData.locationCity = e.city.label
				this.formData.locationCounty = e.area.label
				this.location = e.province.label + ' ' + e.city.label + ' ' + e.area.label
			},
			//选择家乡
			confirmhome(e) {
				console.log(e)
				this.formData.homeProvince = e.province.label
				this.formData.homeCity = e.city.label
				this.formData.homeCounty = e.area.label
				this.home = e.province.label + ' ' + e.city.label + ' ' + e.area.label
			},
			//选择学历
			confirmxl(e) {
				console.log(e)
				this.formData.education = e[0].label
			},
			getxllist() { //学历
				this.$Request.getT('/app/dict/getDictList?name=学历').then(res => {
					if (res.code == 0) {
						res.data.map(item => {
							item.label = item.value
						})
						this.xllist = res.data

					}
				});
			},
			confirmbirth(e) {

				this.formData.birthday = e.year + '-' + e.month + '-' + e.day
			},
			uploadImg() {
				let that = this;
				that.count = 9 - that.userImglist.length
				console.log(that.count, '====')
				uni.chooseImage({
					count: that.count,
					sourceType: ['album', 'camera'],
					success: res => {
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							that.$queue.showLoading("上传中...");
							uni.uploadFile({ // 上传接口
								url: that.config("APIHOST1") + '/alioss/upload', //真实的接口地址
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									that.userImglist.push(JSON.parse(uploadFileRes.data).data)
									uni.hideLoading();
								}
							});
						}
					}
				})
			},
			uploadImgs(key) {
				let that = this;
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					success: res => {
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							that.$queue.showLoading("上传中...");
							uni.uploadFile({ // 上传接口
								url: that.config("APIHOST1") + '/alioss/upload', //真实的接口地址
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									console.log(JSON.parse(uploadFileRes.data));
									that.formData[key] = (JSON.parse(uploadFileRes.data).data)
									console.log(that.formData);
									uni.hideLoading();
								}
							});
						}
					}
				})
			},
			config: function(name) {
				var info = null;
				if (name) {
					var name2 = name.split("."); //字符分割
					if (name2.length > 1) {
						info = configdata[name2[0]][name2[1]] || null;
					} else {
						info = configdata[name] || null;
					}
					if (info == null) {
						let web_config = cache.get("web_config");
						if (web_config) {
							if (name2.length > 1) {
								info = web_config[name2[0]][name2[1]] || null;
							} else {
								info = web_config[name] || null;
							}
						}
					}
				}
				return info;
			},
			getUserInfo() {
				// this.$Request.get("/app/user/selectUserById").then(res => {
				// 	if (res.code == 0) {
				// 		this.phone = res.data.phone;
				// 	}
				// 	uni.hideLoading();
				// });
				let userId = uni.getStorageSync('userId')
				this.$Request.get("/app/userData/getUserDataInfo?userId=" + userId).then(res => {
					if (res.code == 0 && res.data) {
						res.data = {
							...res.data,
							"phone": "", //手机号
							"idNumber": "", //证件号码
							"idCardFront": "", //身份证正面图片
							"idCardVerso": "", //身份证反面图片
						}
						this.formData = res.data
						if (Number(res.data.isShow) == 0 || Number(res.data.isShow) == 1) {
							if (Number(res.data.isShow) == 1) {
								this.checked = true
							} else {
								this.checked = false
							}
						}
						if (this.formData.userImg) {
							this.userImglist = this.formData.userImg.split(',')
						}

						this.location = res.data.locationProvince + ' ' + res.data.locationCity + ' ' + res.data
							.locationCounty

						this.home = res.data.homeProvince + ' ' + res.data.homeCity + ' ' + res.data.homeCounty

					}
					uni.hideLoading();
				});


			},
			//确认
			meHandleBtn() {
				let that = this
				if (that.meIndex == 'm1') {
					uni.showLoading({
						title: '提交中...',
					})

					that.$Request.postJson("/app/userData/matcherSaveUserData?matcherUserId=1", that.formData).then(
						res => {
							if (res.code === 0) {
								uni.showToast({
									title: '保存成功',
									icon: "none"
								})
								setTimeout(function() {
									uni.navigateBack()
								}, 1000)
							} else {
								uni.showToast({
									title: res.msg,
									icon: "none"
								})
							}
							uni.hideLoading()
						})
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (that.meIndex == 'm1') {

				}
			},
			// 保存
			messagebtn() {
				this.formData.userImg = this.userImglist
				this.formData.userImg = this.formData.userImg.toString()

				//  "phone": "", //手机号
				//     "idNumber": "", //证件号码
				//     "idCardFront": "", //身份证正面图片
				//     "idCardVerso": "", //身份证反面图片
				if (!this.formData.phone) {
					uni.showToast({
						title: '请输入手机号',
						icon: "none"
					})
				}
				if (!this.formData.idNumber) {
					uni.showToast({
						title: '请输入证件号码',
						icon: "none"
					})
				}
				if (!this.formData.idCardFront) {
					uni.showToast({
						title: '请上传身份证正面',
						icon: "none"
					})
				}
				if (!this.formData.idCardVerso) {
					uni.showToast({
						title: '请上传身份证反面',
						icon: "none"
					})
				}
				if (!this.formData.userImg) {
					// this.$queue.showToast('用户名不能为空');
					uni.showToast({
						title: "请上传个人形象展示图",
						icon: "none"
					})
					return
				}
				if (this.checked) {
					this.formData.isShow = 1
				} else {
					this.formData.isShow = 0
				}

				if (!this.formData.userHeight) {
					uni.showToast({
						title: "请输入身高",
						icon: "none"
					})
					return
				}
				if (!this.formData.userWeight) {
					uni.showToast({
						title: "请输入体重",
						icon: "none"
					})
					return
				}
				if (!this.formData.starSign) {
					uni.showToast({
						title: "请选择星座",
						icon: "none"
					})
					return
				}
				if (!this.formData.education) {
					uni.showToast({
						title: "请选择学历",
						icon: "none"
					})
					return
				}
				// if (!this.formData.career) {
				// 	uni.showToast({
				// 		title: "请填写职业",
				// 		icon: "none"
				// 	})
				// 	return
				// }
				// if (!this.formData.corporateName) {
				// 	uni.showToast({
				// 		title: "请填写公司",
				// 		icon: "none"
				// 	})
				// 	return
				// }
				if (!this.home) {
					uni.showToast({
						title: "请选择家乡地址",
						icon: "none"
					})
					return
				}
				if (!this.location) {
					uni.showToast({
						title: "请选择所在地",
						icon: "none"
					})
					return
				}
				// if (!this.formData.income) {
				// 	uni.showToast({
				// 		title: "请选择月收入",
				// 		icon: "none"
				// 	})
				// 	return
				// }

				if (!this.formData.interest) {
					uni.showToast({
						title: "请填写兴趣爱好",
						icon: "none"
					})
					return
				}
				if (!this.formData.myIntro) {
					uni.showToast({
						title: "请填写关于我",
						icon: "none"
					})
					return
				}

				if (!this.formData.feelingAngle) {
					uni.showToast({
						title: "请填写感情观",
						icon: "none"
					})
					return
				}
				if (!this.formData.idealAspect) {
					uni.showToast({
						title: "请填写心仪的TA",
						icon: "none"
					})
					return
				}
				// if (!this.formData.wxNumber) {
				// 	uni.showToast({
				// 		title: "请填写微信号",
				// 		icon: "none"
				// 	})
				// 	return
				// }

				this.meShowModel = true
				this.meTitle = '温馨提示'
				this.meContent = '确定保存信息？'
				this.meIndex = 'm1'
				this.meShowCancel = true
			},
			// 上传主页图删除
			removeImg(index, ind) {
				this.userImglist.splice(index, 1)
			},
		}

	};
</script>

<style lang="scss">
	page {
		background: #F2F2F7;
	}

	button {
		padding: 0;
		margin: 0
	}

	button::after {
		border: none;
		padding: 0 !important;
	}

	.title {
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #000000;
	}

	.usermain {
		background: #FFFFFF;
		/* color: #333333; */
		border-radius: 24rpx;
		margin: 30rpx;
	}

	.usermain-item {
		display: flex;
		align-items: center;
		margin: 0 40rpx;
		padding: 10rpx 0;
		justify-content: space-between;
		border-bottom: 1rpx solid #e8e8e8;

		/* border-bottom: 2rpx solid #f2f2f2; */
		&.upimg {
			flex-direction: column;
			align-items: flex-start;
		}
	}

	.huibox {
		width: 200rpx;
		height: 200rpx;
		background: #F0F1F5;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		z-index: 9999;
	}


	.usermain-item.item-padding {
		/* padding: 0; */
	}

	.cu-form-group {
		padding: 0;
		background: #FFFFFF;
		text-align: right;
	}

	.cu-form-group input {
		background: #FFFFFF;
		font-size: 28rpx;
		color: #000000;

	}

	.footer-btn {
		padding: 30rpx;
	}

	.footer-btn .usermain-btn {
		width: 100%;
		height: 100rpx;

		border-radius: 24rpx;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
		border-radius: 50rpx;
	}
</style>
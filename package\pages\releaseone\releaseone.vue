<template>
	<view class="release" v-if="shangxianSelect != '否'">
		
		<view style="background: #FFFFFF;border-radius: 24rpx;margin: 0 30rpx;">
			<textarea placeholder="分享新鲜事..." class="release_name" v-model="content"
				placeholder-style="color:#999999;"></textarea>
			
			<view class="release_image">
				<view class="flex" style="overflow: hidden;flex-wrap: wrap;">
					<view v-if="imageList.length">
						<view class="margin-top flex margin-right-sm flex-wrap">
							<view class="flex" v-for="(item,index) in imageList" :key="index"
								@click="priveImgs(index,imageList)"
								style="width: 200rpx;height: 200rpx;margin-right: 5rpx;position: relative;margin-bottom: 10rpx;">
								<image :src="item" style="width: 100%;height: 100%;"></image>
								<view style="z-index: 9;position: absolute;top: -15rpx;right: -15rpx;"
									@click.stop="removeImg(index)">
									<u-icon name="close-circle-fill" color="#AC75FE" size="50rpx"></u-icon>
								</view>
							</view>
						</view>
					</view>
					
					<view class="margin-top flex align-center justify-center" @click="addImage(1)"
						v-if="imageList.length<=8"
						style="background: #F5F5F5; width: 200rpx;height: 200rpx;position: relative;">
						<view class="text-center">
							<image src="../static/photo.png" mode="widthFix" style="width: 54rpx;"></image>
							<view class="text-sm text-gray margin-top-xs">添加图片</view>
						</view>
			
					</view>
			
				</view>
				<view class="margin-top-sm text-sm padding-bottom-sm"  style="color: red;">*添加图片只能上传9张图片</view>
			</view>
		</view>
		<!-- @tap="$u.throttle(submited, 500)" -->
		<view class="release_btn" @tap="$u.throttle(save, 3000)" v-if="!trendsId">我要发啦</view>
		<view class="release_btn" @tap="$u.throttle(bianji, 3000)" v-else>我要发啦</view>
		<!-- <view class="release_btn" @tap="save">我要发啦</view> -->
	</view>
</template>

<script>
	// import shmilyDragImage from '../../components/shmily-drag-image/shmily-drag-image.vue'
	import configdata from '@/common/config.js';
	export default {
		// components: {
		// 	shmilyDragImage
		// },
		data() {
			return {
				shangxianSelect: '否',
				trendsId: 0,
				content: '',
				videoUrl: '',
				imageList: [],
				imageLists: ''
			}
		},
		onLoad(d) {
			// #ifdef MP-WEIXIN
			this.shangxianSelect = this.$queue.getData('shangxianSelect');
			// #endif
			// #ifndef MP-WEIXIN
			this.shangxianSelect = '是';
			// #endif
			if (this.shangxianSelect === '否') {
				uni.setNavigationBarTitle({
					title: '设置'
				})
			} else {
				uni.setNavigationBarTitle({
					title: '发布动态'
				})
			}
			this.trendsId = d.trendsId;
			if (d.trendsId) {
				this.getCont()
			}
		},
		methods: {
			//预览图片
			priveImgs(index, url) {
				uni.previewImage({
					current: index,
					urls: url
				})
			},
			removeImg(index) {
				this.imageList.splice(index, 1)
			},
			getCont() {
				let userId = this.$queue.getData('userId');
				let data = {
					trendsId: this.trendsId,
					userId: userId
				}
				this.$Request.getT('/app/trends/getTrendsInfo', data).then(res => {
					if (res.code == 0) {
						this.content = res.data.trendsContent
						if (res.data.trendsImage) {
							this.imageList = res.data.trendsImage.split(',')
						}
						this.createTime = res.data.createTime
					} else {
						uni.hideLoading();
						this.$queue.showToast(res.msg);
					}
				});
			},
			bianji() {
				this.imageLists = this.imageList
				this.imageLists = this.imageLists.toString()
				this.$queue.showLoading('提交中...');
				// if (this.$queue.getChatSearchKeys(this.content)) {
				// 	uni.showToast({
				// 		title: "输入内容带有非法关键字请重新输入",
				// 		mask: false,
				// 		duration: 1500,
				// 		icon: "none"
				// 	});
				// 	this.$Request.postT('/app/risk/insertRisk?riskType=2&content=' + this.content).then(res => {

				// 	})
				// 	return;
				// }
				if (this.imageList.length == 0) {
					this.$queue.showToast('请添加图片!');
					return;
				}

				let userId = this.$queue.getData('userId');
				let data = {
					trendsContent: this.content,
					trendsImage: this.imageLists,
					trendsId: this.trendsId,
					releaseType: 1 // 发布类型 1用户发布 2红娘发布

				}
				this.$Request.postT('/app/trends/saveTrends', data).then(res => {
					if (res.code == 0) {
						uni.hideLoading();
						this.$queue.showToast('修改成功');
						setTimeout(d => {

							uni.redirectTo({
								url: '/package/pages/detail/dongtai'
							})
						}, 1000)
					} else {
						uni.hideLoading();
						this.$queue.showToast(res.msg);
					}
				});
			},
			save() {
				this.imageLists = this.imageList
				this.imageLists = this.imageLists.toString()
				this.$queue.showLoading('提交中...');

				if (this.content === '') {
					uni.hideLoading();
					this.$queue.showToast('请输入内容信息!');
					return;
				}

				if (this.imageList.length == 0) {
					this.$queue.showToast('请添加图片!');
					return;
				}

				// let userId = this.$queue.getData('userId');
				let data = {
					trendsContent: this.content,
					trendsImage: this.imageLists,
					releaseType: 1 // 发布类型 1用户发布 2红娘发布

				}
				this.$Request.postT('/app/trends/saveTrends', data).then(res => {
					if (res.code == 0) {
						uni.hideLoading();
						this.$queue.showToast('发布成功');
						setTimeout(d => {
							uni.redirectTo({
								url: '/package/pages/detail/dongtai'
							})
						}, 1000)
					} else {
						uni.hideLoading();
						this.$queue.showToast(res.msg);
					}
				});
			},
			// 删除
			delData() {
				this.videoUrl = ''
			},
			// 视频上传
			addImages(e) {
				let that = this
				uni.chooseVideo({
					count: 1,
					sourceType: ['album', 'camera'],
					success: res => {
						console.log(res)
						// for (let i = 0; i < res.tempFilePath.length; i++) {
						that.$queue.showLoading("上传中...");
						uni.uploadFile({ // 上传接口
							url: that.config("APIHOST1") + '/alioss/upload', //真实的接口地址
							filePath: res.tempFilePath,
							name: 'file',
							success: (uploadFileRes) => {
								console.log(JSON.parse(uploadFileRes.data).data)
								that.videoUrl = JSON.parse(uploadFileRes.data).data
								// that.form.videoUrl = JSON.parse(uploadFileRes.data).data
								// console.log(that.videoUrl)
								uni.hideLoading();
							}
						});
						// }
					}
				})
			},
			// 图片上传
			addImage(e) {
				if (e == 1) {
					var num = this.imageList.length
					this.count = 9 - num
				}
				let that = this
				uni.chooseImage({
					count: this.count,
					sourceType: ['album', 'camera'],
					success: res => {
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							that.$queue.showLoading("上传中...");
							uni.uploadFile({ // 上传接口
								url: that.config("APIHOST1") + '/alioss/upload', //真实的接口地址
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									console.log(JSON.parse(uploadFileRes.data).data)
									that.imageList.push(JSON.parse(uploadFileRes.data).data)
									console.log(that.imageList)
									uni.hideLoading();
								}
							});
						}
					}
				})
			},
			config: function(name) {
				var info = null;
				if (name) {
					var name2 = name.split("."); //字符分割
					if (name2.length > 1) {
						info = configdata[name2[0]][name2[1]] || null;
					} else {
						info = configdata[name] || null;
					}
					if (info == null) {
						let web_config = cache.get("web_config");
						if (web_config) {
							if (name2.length > 1) {
								info = web_config[name2[0]][name2[1]] || null;
							} else {
								info = web_config[name] || null;
							}
						}
					}
				}
				return info;
			}
		}
	}
</script>

<style scoped >
	page {
		background:#F9F9F9;
	}

	.release {
		width: 100%;
		position: relative;
	}

	.release_name {
		width: 95%;
		color: #333333;
		background: #FFFFFF;
		padding: 20upx;
		margin: 0 auto;
		height: 300rpx;
		margin: 20rpx;
		letter-spacing: 2rpx;
		border-radius: 15upx;
		font-size: 28rpx;
	}

	.release_image {
		margin: 20rpx;
		width: 90%;
		/* margin: 0 auto; */
	}

	.release_image image {
		width: 100px;
		height: 100px;
	}

	.release_btn {
		width: 90%;
		height: 98rpx;
		margin: 0 auto;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
		color: white;
		text-align: center;
		line-height: 98rpx;
		margin-top: 40px;
		border-radius: 55rpx;
	}
</style>
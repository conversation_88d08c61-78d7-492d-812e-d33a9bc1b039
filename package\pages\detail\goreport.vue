<template>
	<view class="bonus-main">
		<view class="list-item" v-if="matchdata.length>0" :show-arrow="false" v-for="(item, index) in matchdata"
			:key="index">
			<view class="list-item-wrap " style="background-color: #FFFFFF;" 
				:class="item.image && item.image.length>1?'img2':'img1'">
				<view class="" v-for="(imgs,inde) in item.image" :key="inde" @click="saveImg(item.image,inde)"
					:class="item.image.length>1?'img2':'img1'">
					<image :src="imgs" mode="aspectFill"> </image>
				</view>
				<view style="padding: 20rpx 30rpx;background-color: #FFFFFF;">
					<view class="list-title" style="color: #000000">投诉内容：{{ item.content }}</view>
					<view class="list-title" style="margin-top: 10upx;color: #000000;">被投诉人：{{ item.byUserName }}</view>
					<view style="display: flex;justify-content: space-between;margin-top: 10rpx;">
						<view>
							<text style="color: #000000;">投诉时间：{{ item.createAt }}</text>
						</view>
					</view>
					<view class="list-title" style="margin-top: 10upx;color: #000000;" v-if="item.status==0">状态：待处理
					</view>
					<view class="list-title" style="margin-top: 10upx;color: #000000;" v-if="item.status==1">状态：已驳回
						<!-- <text @click="disposeDelete(item)"
							style="float: right;background-color: #AC75FE;font-size: 28rpx;padding: 5rpx 30rpx;border-radius: 12rpx;color: #FFFFFF;">删除</text> -->
					</view>
					<view class="list-title" style="margin-top: 10upx;color: #000000;" v-if="item.status==2">状态：已封号
						<!-- <text @click="disposeDelete(item)"
							style="float: right;background-color: #AC75FE;font-size: 28rpx;padding: 5rpx 30rpx;border-radius:
						12rpx;color: #FFFFFF;">删除</text> -->
					</view>
					<view class="list-title" style="margin-top: 10upx;color: #000000;" v-if="item.status==3">状态：已删除
						<!-- <text @click="disposeDelete(item)"
							style="float: right;background-color: #AC75FE;font-size: 28rpx;padding: 5rpx 30rpx;border-radius:
						12rpx;color: #FFFFFF;">删除</text> -->
					</view>
					<view class="list-title" style="margin-top: 10upx;color: #000000" v-if="item.status==1">
						驳回内容：{{ item.auditContent }}</view>
					<view class="list-title" style="margin-top: 10upx;color: #000000" v-if="item.status==4">
						处理内容：{{ item.auditContent }}</view>
				</view>
			</view>
		</view>
		<!-- 加载更多提示 -->
		<view class="s-col is-col-24" v-if="matchdata.length > 0">
			<load-more :status="loadingType" :contentText="contentText"></load-more>
		</view>
		<!-- 加载更多提示 -->
		<empty v-if="matchdata.length == 0" des="暂无数据"></empty>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>

	</view>
</template>

<script>
	import empty from '../../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				infoId: '',
				showFlag: true,
				page: 1,
				size: 5,
				userId: '',
				type: 3,
				loadingType: 0,
				scrollTop: false,
				matchdata: [],
				totalPage: 0,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				}
			};
		},
		onLoad() {
			// this.mymatch()
		},
		onShow() {
			if (this.showFlag) {
				uni.showLoading({
					title: '加载中...'
				});
				this.mymatch();
			}
		},
		onPullDownRefresh() {
			this.page = 1;
			this.mymatch(this.tabIndex);
		},
		onReachBottom: function() {
			if (this.page < this.totalPage) {
				this.page = this.page + 1;
				this.mymatch(this.tabIndex);
			}

		},
		methods: {
			// 查看图片
			saveImg(imgs, index) {
				// console.log(imgs)
				this.showFlag = false; //不允许再次触发onshow
				let that = this;
				let imgArr = imgs
				// imgArr.push(imgs);
				// //预览图片
				uni.previewImage({
					urls: imgArr,
					current: imgArr[index]
				});
			},
			// // 查看图片
			// saveImg(imgs, index) {
			// 	this.showFlag = false; //不允许再次触发onshow
			// 	let imgsArray = [];
			// 	imgsArray = imgs;
			// 	uni.previewImage({
			// 		// current: 0,
			// 		current: imgsArray[index],
			// 		urls: imgsArray
			// 	});
			// },
			mymatch() {
				this.loadingType = 1;
				
				let userId = this.$queue.getData("userId");
				this.$Request.getT(
					`/app/message/selectMessageByUserId?page=${this.page}&limit=${this.size}&state=3`).then(
					res => {
						uni.stopPullDownRefresh()
						uni.hideLoading()
						if (res.code === 0) {
							this.totalPage = res.data.totalPage
							if (this.page === 1) {
								this.matchdata = [];
							}
							res.data.list.forEach(d => {
								if (d.image) {
									d.image = d.image.split(',');
								}
								this.matchdata.push(d);
							});
							if (res.data.list.length === this.size) {
								this.loadingType = 0
							} else {
								this.loadingType = 3
							}

						} else {
							this.loadingType = 2;
							uni.hideLoading();
						}
					});
			},
			//确认
			meHandleBtn() {
				let that = this
				if (this.meIndex == 'm1') {
					that.$Request.getT(`/userReport/delete?id=${that.infoId}`).then(res => {
						if (res.status === 0) {
							that.$queue.showToast('删除成功');
							that.page = 1
							that.matchdata = [];
							that.mymatch()
						}
					});
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			// 删除
			disposeDelete(item) {
				let that = this
				this.meShowModel = true
				this.meTitle = '删除提醒'
				this.meContent = '确定要删除此条信息？'
				this.meIndex = 'm1'
				this.infoId = item.id
			},
		}
	};
</script>

<style>
	page {
		background-color: #f5f5f5;
	}

	.list-item {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		margin: 20rpx 20rpx 20rpx 20rpx;
	}

	.img1 image {
		width: 100%;
		height: 400rpx;
	}

	.img2 {
		display: flex;
		flex-wrap: wrap;
	}

	.img2 image {
		width: 210rpx;
		height: 210rpx;
		margin-top: 15rpx;
		margin-right: 5rpx;
		margin-left: 15rpx;
	}
</style>
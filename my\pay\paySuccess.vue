<template>
  <view class="pay-success-container">
    <!-- 成功图标 -->
    <view class="success-icon-wrapper">
      <view class="success-icon">
        <view class="icon-bg">
          <view class="checkmark">✓</view>
        </view>
        <!-- 装饰性小圆点 -->
        <view class="dot dot-1"></view>
        <view class="dot dot-2"></view>
        <view class="dot dot-3"></view>
        <view class="dot dot-4"></view>
      </view>
    </view>

    <!-- 支付成功文字 -->
    <view class="success-title">支付成功</view>

    <!-- 订单信息 -->
    <view class="order-info">
      <view class="info-item">
        <text class="info-label">订单号：</text>
        <text class="info-value">{{ orderInfo.orderNo }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">支付时间：</text>
        <text class="info-value">{{ orderInfo.payTime }}</text>
      </view>
    </view>

    <!-- 返回按钮 -->
    <view class="bottom-btn">
      <button class="return-btn" @click="goBack">返回</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      orderInfo: {
        orderNo: "",
        payTime: "",
      },
    };
  },
  created() {
    // 页面创建时初始化数据
    this.initOrderInfo();
  },
  onLoad(options) {
    // 接收传递过来的订单信息（如果有的话，会覆盖默认生成的）
    if (options.orderNo) {
      this.orderInfo.orderNo = options.orderNo;
    }
    if (options.payTime) {
      this.orderInfo.payTime = options.payTime;
    }
  },
  methods: {
    // 初始化订单信息
    initOrderInfo() {
      this.orderInfo.orderNo = this.generateOrderNo();
      this.orderInfo.payTime = this.getCurrentTime();
    },

    // 生成随机18位订单号
    generateOrderNo() {
      let orderNo = "";
      for (let i = 0; i < 18; i++) {
        orderNo += Math.floor(Math.random() * 10);
      }
      return orderNo;
    },

    // 获取当前时间并格式化为 yyyy-MM-DD hh:mm:ss
    getCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    goBack() {
      // 返回到首页或指定页面
      uni.reLaunch({
        url: "/pages/my/index",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pay-success-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 60rpx;
  box-sizing: border-box;
}

.success-icon-wrapper {
  margin-top: 200rpx;
  margin-bottom: 80rpx;
  position: relative;
}

.success-icon {
  position: relative;
  width: 200rpx;
  height: 200rpx;

  .icon-bg {
    width: 160rpx;
    height: 160rpx;
    background: linear-gradient(135deg, #ffe4b5 0%, #ffd700 50%, #ffa500 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 20rpx;
    box-shadow: 0 8rpx 24rpx rgba(255, 165, 0, 0.3);

    .checkmark {
      color: white;
      font-size: 60rpx;
      font-weight: bold;
    }
  }

  // 装饰性小圆点
  .dot {
    position: absolute;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background: #ffb347;
    opacity: 0.6;
  }

  .dot-1 {
    top: 30rpx;
    right: 10rpx;
    background: #ff6b6b;
  }

  .dot-2 {
    top: 60rpx;
    left: 0rpx;
    background: #4ecdc4;
    width: 12rpx;
    height: 12rpx;
  }

  .dot-3 {
    bottom: 40rpx;
    right: 20rpx;
    background: #45b7d1;
    width: 10rpx;
    height: 10rpx;
  }

  .dot-4 {
    bottom: 20rpx;
    left: 15rpx;
    background: #96ceb4;
    width: 14rpx;
    height: 14rpx;
  }
}

.success-title {
  font-size: 48rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 120rpx;
}

.order-info {
  width: 100%;
  margin-bottom: 200rpx;
  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 40rpx;
    justify-content: center;

    &:last-child {
      margin-bottom: 0;
    }

    .info-label {
      font-size: 32rpx;
      color: #666;
      margin-right: 20rpx;
    }

    .info-value {
      font-size: 32rpx;
      color: #333;
    }
  }
}

.bottom-btn {
  position: fixed;
  bottom: 60rpx;
  left: 60rpx;
  right: 60rpx;

  .return-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
    color: white;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(255, 20, 147, 0.3);

    &::after {
      border: none;
    }

    &:active {
      transform: translateY(2rpx);
      box-shadow: 0 4rpx 12rpx rgba(255, 20, 147, 0.3);
    }
  }
}

// 添加一些动画效果
.success-icon {
  animation: bounceIn 0.8s ease-out;
}

.success-title {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.order-info {
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

.bottom-btn {
  animation: fadeInUp 0.6s ease-out 0.7s both;
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(60rpx);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>

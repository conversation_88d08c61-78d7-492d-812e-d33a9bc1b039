<template>
	<view>
		<view class="listbox" v-for="(item,index) in list" :key="index">
			<view class="hongnian">
				<image :src="item.avatar" v-if="item.avatar"></image>
				<image src="../static/logo.png" v-else></image>
				<view class="flex align-center justify-between " style="flex: 1;margin-left:15rpx">
					<view class="flex align-center">
						<view class="name">{{ item.userName}}</view>
						<view class="yrz">已认证</view>
					</view>
					<view class="btn" v-if="item.status==1" @click="clickItem(10,item)">联系红娘</view>
					<view class="btn" v-if="item.status==0" @click="clickItem(66,item)">编辑</view>
				</view>
			</view>
			<view class="conten">{{ item.trendsContent }}</view>
			<view class="huibix" v-if="item.userData" @click="clickItem(99,item)">
				<image :src="item.userData.userImg.split(',')[0]||'../static/logo.png'" class="usrtImg"></image>
				<view class="flex align-center justify-between " style="flex: 1;margin-left:15rpx">
					<view class="">
						<view>{{ item.userData.realName }}</view>
						<view class="tablin">
							<view>{{ item.userData.age}}岁</view>
							<view class="line"></view>
							<view>{{ item.userData.userHeight||''}}cm</view>
							<view class="line"></view>
							<view>{{ item.userData.education||'' }}</view>
							<view class="line" v-if="item.userData.career"></view>
							<view v-if="item.userData.career">{{ item.userData.career }}</view>
						</view>
					</view>
					<image src="/static/images/my/right_icon.png" style="width: 11rpx;height: 22rpx;"></image>
				</view>
			</view>
			<view class="dara">{{ item.createTime }}</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			//背景颜色
			list: {
				type: Array,
				default: []
			},
			className: {
				type: Boolean,
				default: false
			},
			userId: {
				type: [Number, String],
				default: "",
			},
		},
		data() {
			return {}
		},
		methods: {
			clickItem(ind, item) {
				this.$emit('click', {
					index: ind,
					id: item.userData,
					trendsId: item
				});
			}
		}
	}
</script>

<style lang="less">
	.listbox {
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
		margin: 10rpx 30rpx 30rpx;

		.hongnian {
			display: flex;
			align-items: center;

			image {
				width: 82rpx;
				height: 82rpx;
				border-radius: 55rpx;
			}

			.name {
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #333333;
			}

			.yrz {
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				background: #FF729E;
				border-radius: 4rpx;
				margin-left: 10rpx;
				padding: 5rpx 10rpx;
			}

			.btn {
				background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
				border-radius: 10rpx;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #FFFFFF;
				width: 156rpx;
				height: 64rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.conten {
			padding: 20rpx 0;
		}

		.huibix {
			background: #F5F5F5;
			border-radius: 12rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 25rpx;

			.usrtImg {
				width: 72rpx;
				height: 72rpx;
				border-radius: 50%;
			}

			.tablin {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;

				.line {
					width: 1rpx;
					height: 19rpx;
					// border: 1px solid #CCCCCC;
					background-color: #CCCCCC;
					margin: 0 10rpx;
				}
			}
		}

		.dara {
			font-size: 24rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			margin-top: 20rpx;
		}
	}
</style>
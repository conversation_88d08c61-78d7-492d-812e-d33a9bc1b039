<template>
	<view>
		<view class="bg padding">
			<view class="box">
				<view class="tit">总资产（元）</view>
				<view class="pri"><text>{{money}}</text> 元</view>
				<!-- <view class="margin-top-sm">累计充值:325.26元</view> -->
			</view>
			<view class="flex align-center justify-between padding-top">
				<view>
					<view class="padding-bottom-xs">总资产（元）</view>
					<view class="text-bold" style="font-size: 48rpx;">{{money}}</view>
				</view>
				<view class="flex align-center">
					<!-- <view class="tx" @click="gotx()">提现</view> -->
					<view class="cz" @click="gocz()">充值</view>
				</view>
			</view>
		</view>
		<view class="bg margin-top-sm">
			<view class="padding-tb-sm padding-lr flex align-center">
				<view class="lin"></view>
				<view>账单明细</view>
			</view>
			<view style="width: 100%;height: 1rpx;background: #E6E6E6;"></view>
			<scroll-view @scrolltolower="scrolltolower" scroll-y="true" class="lists">
				<block>
					<view class="flex align-center justify-between padding" v-for="(item,index) in moeylist"
						:key="index">
						<view>
							<view>{{item.title}}</view>
							<view class="text-sm margin-top-xs" style="color: #999999;">{{item.createTime}}</view>
						</view>
						<view class="text-bold" style="color: #333333;font-size: 38rpx;" v-if="item.type == 2">
							-{{item.money}}
						</view>
						<view class="text-bold" style="color: #333333;font-size: 38rpx;" v-else>+{{item.money}}</view>
					</view>
				</block>
				<u-empty v-if="moeylist.length==0" text="暂无明细" mode="list"></u-empty>

			</scroll-view>


		</view>
	</view>
</template>

<script>
	import empty from '@/components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				money: 0,
				page: 1,
				pages: 1,
				limit: 10,
				moeylist: [],
			}
		},
		onLoad() {

		},
		onShow() {

			this.taskData()
			this.getlist()
		},
		onPullDownRefresh() {
			this.page = 1
			this.taskData()
			this.getlist()
		},
		methods: {
			//加载更多
			scrolltolower() {
				if (this.page < this.pages) {
					this.page += 1
					this.getlist()
				}
			},
			getlist() {
				let data = {
					page: this.page,
					limit: this.limit,
				}
				this.$Request.getT('/app/userMoney/balanceDetailed', data).then(res => {
					if (res.code == 0) {
						this.pages = res.data.totalPage
						if (this.page == 1) {
							this.moeylist = res.data.records
						} else {
							this.moeylist = [...this.moeylist, ...res.data.records]
						}
						uni.stopPullDownRefresh();

					}
				});
			},
			// 获取余额
			taskData() {
				this.$Request.get("/app/userMoney/selectMyMoney").then(res => {
					if (res.code == 0 && res.data) {
						console.log(res.data.money)
						this.money = res.data.money
					}
				});
			},
			gocz() { // 充值
				uni.navigateTo({
					url: '/my/wallet/Txmoney'
				})
			},
			gotx() { // 提现 
				uni.navigateTo({
					url: '/my/wallet/index?index=2'
				})
			}
		}
	}
</script>

<style lang="less">
	page {
		background: #F5F5F5;
	}

	.lists {
		width: 100%;
		height: calc(100vh - 600rpx);
	}

	.bg {
		background: #FFFFFF;
	}

	.box {
		width: 690rpx;
		height: 312rpx;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);;
		border-radius: 16rpx;
		color: #FFFFFF;
		text-align: center;
		padding-top: 50rpx;

		.tit {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: 500;
		}

		.pri {
			margin-top: 20rpx;

			text {
				font-size: 68rpx;
				font-family: DINPro;
				font-weight: 500;
				color: #FFFFFF;
				margin-right: 10rpx;
			}
		}
	}

	.tx {
		width: 160rpx;
		height: 70rpx;
		border: 3rpx solid linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);;
		border-radius: 8rpx;
		text-align: center;
		line-height: 70rpx;
		color: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);;
	}

	.cz {
		width: 160rpx;
		height: 70rpx;
		border-radius: 8rpx;
		text-align: center;
		line-height: 70rpx;
		color: #FFFFFF;
		margin-left: 20rpx;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);;
	}

	.lin {
		width: 6rpx;
		height: 32rpx;
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);;
		margin-right: 15rpx;
	}
</style>
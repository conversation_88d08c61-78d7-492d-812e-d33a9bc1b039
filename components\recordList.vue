<template>
    <view>
        <view class="boxItem" v-for="(item, index) in list" :key="index">
            <view class="listbox"  @click="clickItem(index, item)">
                <view class="iteimg">
                    <image src="../static/logo.png" mode=""></image>
                </view>
                <view class="rightcont">
                    <view class="usrbname">盲目想吃
                        <image src="../static/images/my/rzicon.png"></image>
                    </view>
                    <view class="labl">
                        <view class="sexicon" v-if="item.sex == 1">
                            <u-icon name="man" color="#FFFFFF"></u-icon>
                            {{ item.age }}岁
                        </view>
                        <view class="sexicons" v-if="item.sex == 2">
                            <u-icon name="woman" color="#FFFFFF"></u-icon>
                            {{ item.age }}岁
                        </view>
                        <view class="">西安/新城区</view>
                    </view>
                </view>
                <view class="cell_me">
                     <image
                        src="@/my/static/admin/cell.png"
                        mode="scaleToFill"
                    />
                     <image
                            src="@/my/static/admin/wx.png"
                            mode="scaleToFill"
                        />
            
                </view>
                <!-- <view class="btns">联系她</view> -->
            </view>
            <view class="status" :class="item.status==2?'fail':''">
                <view class="title">{{ statusList[item.status||0]}}</view>
                <view class="content">恭喜会员梨涡允允与会员2419牵线成功！</view>
            </view>
        </view>
    
    </view>
</template>

<script>
export default {
    data() {
        return {
            statusList:[
                "牵线中...","牵线成功","牵线成功"
            ]
        }
    },
    props: {
        //列表
        list: {
            type: Array,
            default: []
        },

    },
    methods: {
        clickItem(ind, item) {
            this.$emit('click', {
                index: ind,
                id: item
            });
        }
    }
}
</script>

<style lang="less">

.boxItem{
    background: #FFFFFF;
    border-radius: 24rpx;
    margin: 20rpx 30rpx;
    padding: 20rpx;
    .status{
        width: 627rpx;
        height: 138rpx;
        background: #F8F8F8;
        border-radius: 14rpx;
        margin-left: 20rpx;
        padding:26rpx 37rpx;
        margin-top: 30rpx;
        &.fail{
            filter: grayscale(100%);
        }
        .title{
            font-family: PingFang SC;
            font-weight: bold;
            font-size: 30rpx;
            color: #FF6499;
            margin-bottom: 10rpx;
        }
        .content{
            &::before{
                width: 12rpx;
                height: 12rpx;
                background: linear-gradient(49deg, #FFC5D9 0%, #FF76A0 100%);
                border-radius: 50%;
                content: "";
                display:inline-block;
                margin-right: 20rpx;
            }
        }
    }
}
.listbox {
   
    display: flex;
    align-items: center;

    .iteimg {
        width: 100rpx;
        height: 100rpx;

        image {
            width: 100%;
            height: 100%;
            border-radius: 1112rpx;
        }
    }

    .rightcont {
        flex: 1;
        // height: 200rpx;
        // display: flex;
        // align-items: center;
        // flex-wrap: wrap;
        // align-content: space-between;
        margin-left: 18rpx;

        .usrbname {
            display: flex;
            align-items: center;

            font-size: 32rpx;
            font-family: PingFang SC;
            font-weight: bold;
            color: #292929;

            image {
                width: 40rpx;
                height: 40rpx;
                margin-left: 10rpx;
            }
        }

        .labl {
            display: flex;
            align-items: center;
            font-size: 24rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #999999;
            margin: 20rpx 0;

            .sexicon {
                background: #38CAFF;
                border-radius: 10rpx;
                font-size: 24rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                padding: 4rpx 10rpx;
                margin-right: 10rpx;
            }

            .sexicons {
                background: #edbef3;
                border-radius: 10rpx;
                font-size: 24rpx;
                font-family: PingFang SC;
                font-weight: 500;
                color: #FFFFFF;
                padding: 4rpx 10rpx;
                margin-right: 10rpx;
            }
        }

        .tit {
            font-size: 26rpx;
            font-family: PingFang SC;
            font-weight: 500;
            color: #666666;
        }
    }
    .cell_me{
        image{
            width: 67rpx;
            height: 67rpx;
            margin-left: 48rpx;
        }
    }
    .btns {
        width: 163rpx;
        height: 69rpx;
        background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
        border-radius: 10rpx;
        font-size: 26rpx;
        font-family: PingFang SC;
        font-weight: 500;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        justify-content: center;

    }
}
</style>
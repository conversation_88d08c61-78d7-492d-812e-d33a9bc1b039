<template>
	<view>

		<view class="box" v-for="(item,index) in list" :key="index">
			<image :src="item.avatar?item.avatar:'../../static/logo.png'"
				style="width: 75rpx;height: 75rpx;border-radius: 55%;"></image>
			<view class="flex align-center justify-between margin-left-xs" style="width: 85%;">
				<view>
					<view>{{item.userName}}</view>
					<view>报名时间:{{item.createTime}}</view>
				</view>
				<view @click="callPhone(item.phone)">
					<image src="../../my/static/phones.png" style="width: 40rpx;height: 38rpx;"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				list: [],
				page: 1,
				activityId: ''
			}
		},
		onLoad(d) {
			if (d.activityId) {
				this.activityId = d.activityId
			}
		},
		onShow() {
			if (this.activityId) {
				this.getlist()
			}
		},
		methods: {
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone //仅为示例
				});
			},
			getlist() {
				uni.showLoading({
					title: '加载中...',
					mask: true
				});
				this.$Request.get('/app/signMatchActivity/getActivityList', {
					page: this.page,
					limit: 10,
					activityId: this.activityId
				}).then(res => {
					uni.hideLoading();
					uni.stopPullDownRefresh();
					if (res.code == 0) {
						this.list = this.page == 1 ? res.data.records : [
							...this.list,
							...res.data.records
						]
						if (res.data.pages > res.data.current) {
							this.loadingType = 0
						} else {
							this.loadingType = 2
						}
					}
				});
			}
		},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {
			this.page = 1
			this.list()
		},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {
			if (this.loadingType == 0) {
				this.page += 1
				this.list()
			}
		},
	}
</script>

<style lang="less">
	page {
		background: #F7F6F5;
	}

	.box {
		width: 686rpx;
		height: 136rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		padding: 30rpx;
		margin: 20rpx auto;
		display: flex;
		align-items: center;
	}
</style>
.task{
	background: #FFFFFF;
	.tui-header{
		padding: 0 20rpx;
		display: flex;
		height: 86rpx;
		align-items: center;
    justify-content: space-between;
	width: 100%;
	position: fixed;
	top: 0rpx;
	z-index: 99;
	background: #FFFFFF;
		.no-message{
			color: orange;
      width: 100rpx;
		}
    .message{
      color: #2C405A;
      width: 100rpx;
    }
    .myButton{
      background: #2A62FF;
      width: 120rpx;
      height: 60rpx;
      color: #FFFFFF;
      font-size: 28rpx;
      line-height: 60rpx;
      text-align: center;
      border-radius: 8rpx;
      cursor: pointer;
    }
    .tui-search{
      margin: 0 10rpx;
      background: #e5e5e5;
      width: 500rpx;
      height: 50rpx;
      border-radius:16px;
      display: flex;
      line-height: 50rpx;
      color: #999999;
    }
	}
	.menu{
		border-radius: 16upx;
		margin: 16upx;
		padding: 30rpx 28rpx;
		display: flex;
		box-shadow: rgba(183, 183, 183, 0.3) 0px 1px 10px;
		align-items: center;
		justify-content: space-between;
		//height: 100rpx;
		.mItem{
			display: flex;
			 flex-direction: column;
			 align-items: center;
		}
		.mItem>image{
			height: 55upx;
			width: 55upx;
		}
		.mtext{
			margin-top: 16rpx;
			font-size: 24rpx;
		}
	}
	.myBlock{
		height: 18rpx;
		background: #dcdcdc;
	}
}

.list-item-wrap {
    display: flex;
    justify-content: space-between;
    color: #333333;
    font-size: 32upx;
    .list-title{
      font-size: 32upx;
      color: #000000;
      // white-space: nowrap; /* 规定文本是否折行 */
      overflow: hidden; /* 规定超出内容宽度的元素隐藏 */
      text-overflow: ellipsis;
      // width: 460rpx;
	display: -webkit-box;
	 -webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
    }
    .desc{
      font-size: 28upx;
      color: rgba(1,1,1,.5);
      margin-bottom: 8upx;
	  margin-top: 8upx;
    }
  }

.tui-tabs {
	flex: 1;
	flex-direction: column;
	overflow: hidden;
	background-color: #fafafa;
	/* #ifdef MP-ALIPAY || MP-BAIDU */
	height: 100vh;
	/* #endif */
}

.tui-scroll-h {
	width: 750rpx;
	height: 80rpx;
	background-color: #ffffff;
	flex-direction: row;
	/* #ifndef APP-PLUS */
	white-space: nowrap;
	/* #endif */
	/* #ifdef H5 */
	position: fixed;
	top: 44px;
	left: 0;
	z-index:999;
	/* #endif */
}

.tui-line-h {
	/* #ifdef APP-PLUS */
	height: 1rpx;
	background-color: #cccccc;
	/* #endif */
	position: relative;
}
/* #ifndef APP-PLUS*/
.tui-line-h::after {
	content: '';
	position: absolute;
	border-bottom: 1rpx solid #cccccc;
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	bottom: 0;
	right: 0;
	left: 0;
}
/* #endif */

.tui-tab-item {
	/* #ifndef APP-PLUS */
	display: flex;
	/* #endif */
	// flex-wrap: nowrap;
	// padding-left: 34rpx;
	// padding-right: 34rpx;
}

.tui-tab-item-title {
	color: #555;
	font-size: 30rpx;
	height: 80rpx;
	line-height: 80rpx;
	flex-wrap: nowrap;
	white-space: nowrap;
}

.tui-tab-item-title-active {
	border-bottom: 1px solid rgb(225, 10, 7);
	color: #E10A07;
	font-size: 32upx;
	font-weight: bold;
	border-bottom-width: 6upx;
	text-align: center;
}

.tui-swiper-box {
	flex: 1 !important;
	/* #ifdef H5 */
	margin-top: 80rpx;
	/* #endif */
}

.tui-swiper-item {
	flex: 1 !important;
	flex-direction: row;
}

.tui-scroll-v {
	flex: 1;
	/* #ifndef MP-ALIPAY */
	flex-direction: column;
	/* #endif */
	width: 750rpx;
}

.tui-update-tips {
	position: absolute;
	left: 0;
	top: 41px;
	right: 0;
	padding-top: 5px;
	padding-bottom: 5px;
	background-color: #fddd9b;
	align-items: center;
	justify-content: center;
	text-align: center;
}

.tui-update-tips-text {
	font-size: 14px;
	color: #ffffff;
}

.tui-refresh {
	width: 750rpx;
	height: 64px;
	justify-content: center;
}

.tui-refresh-view {
	flex-direction: row;
	flex-wrap: nowrap;
	align-items: center;
	justify-content: center;
}

.tui-refresh-icon {
	width: 20px;
	height: 20px;
	transition-duration: 0.25s;
	transition-property: transform;
	transform: rotate(0deg);
	transform-origin: 10px 10px;
}

.tui-refresh-icon-active {
	transform: rotate(180deg);
}

.tui-loading-icon {
	width: 20px;
	height: 20px;
	margin-right: 5px;
	color: #999999;
}

.tui-loading-text {
	margin-left: 2px;
	font-size: 14px;
	color: #999999;
}

.tui-loading-more {
	align-items: center;
	justify-content: center;
	padding-top: 15px;
	padding-bottom: 15px;
	text-align: center;
	position: relative;
}
.tui-loadmore-line {
	border-bottom-width: 1rpx;
	border-bottom-style: solid;
	border-bottom-color: #e5e5e5;
	width: 320rpx;
	position: absolute;
	z-index: -1;
}

.tui-loading-more-text {
	padding-left: 8rpx;
	padding-right: 8rpx;
	font-size: 28rpx;
	line-height: 28rpx;
	background-color: #fafafa;
	text-align: center;
	color: #999;
}
<template>
	<view class="container">
		<view class="wrapper">
			<view class="input-content">
				<view class="cu-form-group"
					style="border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px">
					<view class="title">手机号</view>
					<input type="number" :value="phone" placeholder="请输入新手机号" maxlength="11" data-key="phone"
						@input="inputChange" />
				</view>
				<view class="cu-form-group"
					style="border: 2upx solid whitesmoke;margin-bottom: 20px;border-radius: 30px">
					<text class="title">验证码</text>
					<input type="number" :value="code" placeholder="请输入验证码" maxlength="6" data-key="code"
						@input="inputChange" @confirm="toLogin" />
					<button class="send-msg" @click="sendMsg" :disabled="sending">{{sendTime}}</button>
				</view>
			</view>
			<button class="confirm-btn" @click="toLogin">保存</button>
		</view>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
		@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
		:cancel-text='meCancelText' :mask-close-able="false"></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex:'',//弹窗的key
				code: '',
				phone: '',
				password: '',
				sending: false,
				sendTime: '获取验证码',
				count: 60,
				logining: false
			}
		},

		methods: {
			//确认
			meHandleBtn(){
				let that = this
				if(this.meIndex=='m1'){
								
				}
			},
			//取消
			meHandleClose(){
				let that = this
				if(this.meIndex=='m1'){
								
				}
			},
			sendMsg() {
				const {
					phone
				} = this;
				if (!phone) {
					this.$queue.showToast("请输入手机号");
				} else if (phone.length !== 11) {
					this.$queue.showToast("请输入正确的手机号");
				} else {
					this.$queue.showLoading("正在发送验证码...");
					this.$Request.getT("/msg/sendMsg/" + phone + "/bind").then(res => {
						if (res.status === 0) {
							this.sending = true;
							this.$queue.showToast('验证码发送成功请注意查收');
							this.countDown();
							uni.hideLoading();
						} else {
							uni.hideLoading();
							this.meShowModel = true
							this.meTitle = '短信发送失败'
							this.meContent = res.msg ? res.msg : '请一分钟后再获取验证码',
							this.meIndex = 'm1'
						}
					});
				}
			},
			countDown() {
				const {
					count
				} = this;
				if (count === 1) {
					this.count = 60;
					this.sending = false;
					this.sendTime = '获取验证码'
				} else {
					this.count = count - 1;
					this.sending = true;
					this.sendTime = count - 1 + '秒后重新获取';
					setTimeout(this.countDown.bind(this), 1000);
				}
			},
			inputChange(e) {
				const key = e.currentTarget.dataset.key;
				this[key] = e.detail.value;
			},
			navBack() {
				uni.navigateBack();
			},


			navTo(url) {
				uni.navigateTo({
					url
				})
			},
			toLogin() {
				if (this.code == '') {
					this.$queue.showToast("请输入验证码");
				} else {
					let userId = this.$queue.getData("userId");
					const {
						phone,
						password,
						code
					} = this;
					if (!phone) {
						this.$queue.showToast("请输入手机号");
					} else {
						this.logining = true;
						this.$queue.showLoading("加载中...");
						this.$Request.getT("/user/changePhone", {
							userId: userId,
							phone: phone,
							msg: code
						}).then(res => {
							uni.hideLoading();
							if (res.status === 0) {
								uni.navigateTo({
									url: '/pages/my/userstatus'
								});
							} else {
								this.meShowModel = true
								this.meTitle = '绑定手机号失败'
								this.meContent = res.msg
								this.meIndex = 'm1'
							}
						});
					}
				}
			},
		},

	}
</script>

<style lang='scss'>
	page {
		background: #1c1b20;
	}

	.send-msg {
		border-radius: 30px;
		color: white;
		height: 30px;
		font-size: 14px;
		line-height: 30px;
		background: #5E81F9;
	}

	.container {
		padding-top: 32upx;
		position: relative;
		width: 100%;
		height: 100%;
		overflow: hidden;
		background: #1c1b20;
	}

	.wrapper {
		position: relative;
		z-index: 90;
		background: #1c1b20;
		padding-bottom: 20px;
	}


	.input-content {
		padding: 32upx 80upx;
	}


	.confirm-btn {
		width: 600upx;
		height: 80upx;
		line-height: 80upx;
		border-radius: 60upx;
		margin-top: 32upx;
		background: #5E81F9;
		color: #fff;
		font-size: 32upx;

		&:after {
			border-radius: 60px;
		}
	}
</style>

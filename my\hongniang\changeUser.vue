<template>
	<div class="changeUser-page">
		<view class="search-box">
			<u-search class="search" bg-color="#F2F2F2" :clearabled="false" :input-style="inputStyle" placeholder="输入用户姓名"
				v-model="searchKey" :show-action="false"></u-search>
		</view>

		<view class=" userList" v-if="list.length!=0">
			<list :list="list" :selectUserId="selectUserId" @click="clickItem"></list>
		</view>

		<empty v-if="list.length == 0"></empty>
		<view class="s-col is-col-24" v-if="list.length > 0">
			<load-more :status="loadingType" :contentText="contentText"></load-more>
		</view>
		<view class="tabber">
			<view class="submits" @click="enterSelect">确定选择</view>
		</view>


	</div>
</template>

<script>
	import list from "@/components/selectviplist.vue";
	import empty from '@/components/empty'

	import Vue from 'vue';
	export default Vue.extend({
		components: {
			list,
			empty
		},
		data() {
			return {
				inputStyle: {
					fontSize: '28rpx',
					width: '100%'
				},
				list: [],
				selectUserId: "",
				selectUsrInfo: {},
				page: 1,
				searchKey: "",
				loadingType: 0,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
			}
		},
		computed: {},
		methods: {
			clickItem(data) {
				this.selectUserId = data.item.userId
				this.selectUsrInfo = data.item
			},
			enterSelect() {
				console.log('666666666666')
				uni.$emit('selectedUser', this.selectUsrInfo)
				uni.navigateBack()
				// uni.navigateTo({ url: '/my/hongniang/publishUser' })
			},
			//获取数据
			getUserList() {
				uni.showLoading({
					title: '加载中',
					mask: true
				})
				this.$Request.getT('/app/userRoleMatchmaker/getUserRoleMatchmakerList', {
					page: this.page,
					limit: 10,
					searchKey: this.searchKey
				}).then(res => {

					if (res.code == 0) {
						this.list = this.page == 1 ? res.data.records : [
							...this.list,
							...res.data.records
						]
						if (res.data.pages > res.data.current) {
							this.loadingType = 0
						} else {
							this.loadingType = 2
						}
					}
					uni.stopPullDownRefresh();
					uni.hideLoading();
				})
			}
		},
		watch: {
			searchKey() {
				this.page = 1
				this.getUserList()
			}
		},

		// 页面周期函数--监听页面加载
		onLoad() {
			this.page = 1
			this.getUserList()
		},
		// 页面周期函数--监听页面初次渲染完成
		onReady() {},
		// 页面周期函数--监听页面显示(not-nvue)
		onShow() {},
		// 页面周期函数--监听页面隐藏
		onHide() {},
		// 页面周期函数--监听页面卸载
		onUnload() {},
		// 页面处理函数--监听用户下拉动作
		onPullDownRefresh() {
			this.page = 1
			this.getUserList()
		},
		// 页面处理函数--监听用户上拉触底
		onReachBottom() {
			if (this.loadingType == 0) {
				this.page += 1
				this.getUserList()
			}

		},
		// 页面处理函数--监听页面滚动(not-nvue)
		// onPageScroll(event) {},
		// 页面处理函数--用户点击右上角分享
		// onShareAppMessage(options) {},
	})
</script>

<style scoped lang="scss">
	@import "./main.scss";

	.search-box {
		background-color: #FFFFFF;
		width: 100%;
		padding: 15rpx 30rpx;
		position: fixed;
		/* #ifdef H5 */
		top: 85rpx;
		/* #endif */
		/* #ifndef H5 */
		top: 0rpx;
		/* #endif */
		left: 0rpx;
		right: 0;
		z-index: 9999;

		.search {
			width: 100%;
		}
	}

	.tabber {
		background: #FFFFFF;
		position: fixed;
		bottom: 0rpx;
		left: 0rpx;
		right: 0;
		z-index: 9999;
		padding: 30rpx;

		.submits {
			height: 100rpx;
			background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
			border-radius: 50rpx;

			font-family: PingFang SC;
			font-weight: bold;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;

		}
	}

	page{
		background: #F2F2F2;
	}
</style>
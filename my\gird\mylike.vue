<template>
	<view>
		<view class="margin-lr flex flex-wrap margin-top justify-between" v-if="list.length!=0">
			<view class="likebox" v-for="(item,index) in list" :key="index" v-if="item.userData"
				@click.stop="godetail(item)">
				<image :src="item.userData.userImg?item.userData.userImg:'../../static/logo.png'"></image>
				<view class="padding-tb-xs">
					<view class="title">
						{{item.userData.realName}}
						<view class="sex" v-if="item.userData.sex==1">
							<u-icon name="man" color="#FFFFFF" size="20"></u-icon>
						</view>
						<view class="sex" v-if="item.userData.sex==2">
							<u-icon name="woman" color="#FFFFFF" size="20"></u-icon>
						</view>
					</view>
					<view class="tit">
						{{item.userData.age}}岁/{{item.userData.userHeight}}cm/
						<text v-if="item.userData.locationCity!='市辖区'"> {{item.userData.locationCity}}</text>
						<text v-else> {{item.userData.locationProvince}}</text>
						<!-- {{item.userData.locationCity}} -->
					</view>
					<view style="width: 100%;height: 1px;background: #F2F2F2;"></view>
					<view class="chat" v-if="item.isGetPhone!=1">
						<u-icon name="chat-fill" color="#6367FF" size="40"></u-icon>
						<text>获取联系方式</text>
					</view>
					<view class="chat"  v-else @click.stop="callPhone(item)">
						<u-icon name="chat-fill" color="#6367FF" size="40"></u-icon>
						<text>联系TA</text>
					</view>
				</view>
			</view>

		</view>
		<empty v-if="list.length==0"></empty>
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				list: [],
				count: 0,
				page: 1,
				limit: 10

			}
		},
		onLoad() {
			
		},
		onShow() {
			this.getlist()
		},
		methods: {
			callPhone(item) {
				this.$Request.getT('/app/userGetPhoneRecord/getPostPushPhone?userId=' + item.userData.userId).then(
					res => {
						if (res.code == 0) {
							let phone = res.data
							uni.makePhoneCall({
								phoneNumber: phone
							})
						}
					});
			},
			godetail(item) {
				uni.navigateTo({
					url: '/package/pages/game/detail?byUserId=' + item.userData.userId
				})
			},
			getlist() {
				let userId = uni.getStorageSync('userId')
				this.$queue.showLoading("加载中...");
				let data = {
					page: this.page,
					limit: this.limit,
					// userId: userId,
					type: 2
				}
				this.$Request.getT("/app/scFollow/getMyScFollowList", data).then(res => {
					if (res.code === 0 && res.data) {

						res.data.records.forEach(d => {
							if (d.userData&&d.userData.userImg){
								let userImg = d.userData.userImg.split(',')
								d.userData.userImg = userImg[0]
							}
						});
						if (this.page == 1) {
							this.list = res.data.records
						} else {
							this.list = [...this.list, ...res.data.records]
						}
						// if (this.page == 1) {
						// 	this.list = res.data.records
						// } else {
						// 	res.data.records.forEach(d => {
						// 		this.list.push(d);
						// 	});
						// }
						this.count = res.data.totalCount;
					}
					uni.stopPullDownRefresh();
					uni.hideLoading();
				});
			}
		},
		onReachBottom: function() {
			if (this.list.length == this.count) {
				uni.showToast({
					title: '已经到底了',
					icon: 'none'
				})
			} else {
				this.page = this.page + 1;
				this.getlist();
			}
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getlist();
		}
	}
</script>

<style lang="less">
	page {
		background: #F2F6FC;
	}

	.likebox {
		width: 333rpx;
		background: #FFFFFF;
		border-radius: 16rpx 16rpx 16rpx 24rpx;

		image {
			width: 333rpx;
			height: 340rpx;
			// background: #FF690C;
			border-radius: 16rpx 16rpx 0rpx 0rpx;
		}

		.title {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #292929;
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			max-width: 200rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			.sex {
				width: 32rpx;
				height: 32rpx;
				background: #38CAFF;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 10rpx;
			}

			.sexs {
				width: 32rpx;
				height: 32rpx;
				background: #fbe2f4;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 10rpx;
			}
		}

		.tit {
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			padding: 10rpx 20rpx;
		}

		.chat {
			font-size: 26rpx;
			font-family: PingFang SC;
			padding-bottom: 10rpx;
			color: #686CFF;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 20rpx 0;

			text {
				margin-left: 8rpx;
			}
		}
	}
</style>
<template>
	<view>
		<u-navbar title="申请成为红娘" :is-back="true" :background="background" title-color="#FFFFFF"
			:border-bottom="false" back-icon-color="#FFFFFF"></u-navbar>

		<!-- 背景 -->
		<view class="bgs">
			<image src="../static/hnbg.png" style="width: 100%;height: 249rpx;"></image>
		</view>
		<view class="cont">
			<view class="box">


				<!-- <view class="name margin-bottom" style="justify-content: space-between;">
					<view>审核状态</view>
					<view class="status" v-if="formData.status==0">待审核</view>
					<view class="status" v-if="formData.status==2">已拒绝</view>
				</view> -->
				<view class="name margin-bottom" style="justify-content: space-between;" v-if="formData.status == 2">
					<view>拒绝理由:</view>
					<view class="status">{{ formData.remark }}</view>
				</view>
				<view class="name">
					<text>*</text>
					<view>真实姓名</view>
				</view>
				<view class="margin-top-sm">
					<u-input v-model="formData.realName" type="text" :border="true" placeholder="请输入联系姓名" />
				</view>
				<view class="name margin-top">
					<text>*</text>
					<view>身份证号</view>
				</view>
				<view class="margin-top-sm">
					<u-input v-model="formData.idNumber" type="idcard" :border="true" placeholder="请输入身份证号"
						maxlength="18" />
				</view>
				<view class="name margin-top">
					<text>*</text>
					<view>手机号码</view>
				</view>
				<view class="margin-top-sm">
					<u-input v-model="formData.matchPhone" type="tel" :border="true" placeholder="请输入手机号码"
						maxlength="11" />
				</view>
				<view class="name margin-top" @click="showCitySelect">
					<text>*</text>
					<view>接管区域</view>
				</view>
				<view class="margin-top-sm" style="position: relative;">
					<u-input v-model="formData.takeProvince" :disabled="true" :border="true" placeholder="请选择接管区域" />
					<view @click="showCitySelect"
						style="position: absolute;top: 0px;left: 0px;width: 100%;height: 100%;z-index: 9;"></view>
				</view>
				<view class="name margin-top" @click="showCitySelect">
					<text>*</text>
					<view>微信号</view>
				</view>
				<view class="margin-top-sm">
					<u-input v-model="formData.wxCode" :border="true" placeholder="请输入微信号" />
				</view>

				<view class="name margin-top" @click="showCitySelect">
					<text>*</text>
					<view>微信二维码</view>
				</view>
				<view class="margin-top-sm">
					<image v-if="formData.wxImg" class="huibox" @click="uploadImg('wxImg')" :src="formData.wxImg"
						mode="scaleToFill" />
					<view class="huibox" v-else @click="uploadImg('wxImg')">
						<u-icon name="plus" color="#CCCCCC" size="60"></u-icon>
					</view>
				</view>
				<u-picker ref="city" mode="region" v-model="show1" :params="params"
					@confirm="confirmProvince"></u-picker>
			</view>

			<view class="box ">
				<view class="name">
					<text>*</text>
					<view>上传身份证</view>
				</view>
				<view class="box2">
					<view @click="uploadImg('idCardFront')" class="slot-btn" hover-class="slot-btn__hover"
						hover-stay-time="150">
						<image :src="formData.idCardFront||'../static/upimg1.png'" mode="scaleToFill" />
					</view>

					<view @click="uploadImg('idCardVerso')" class="slot-btn" hover-class="slot-btn__hover"
						hover-stay-time="150">
						<image :src="formData.idCardVerso || '../static/upimg2.png'" mode="scaleToFill" />
					</view>


				</view>
			</view>
			<view class="xy row">
				<u-checkbox label-size="26rpx" v-model="checked" shape="circle" active-color="#FF6684">
					我已阅读并同意<text class="tit" @click="goXieyi">《协议说明》</text>
				</u-checkbox>
			</view>

			<view class="submit-btn" @click="submit" v-if="formData.isSubmit==0">提交审核</view>
		</view>

	</view>
</template>

<script>
	import configdata from '../../common/config.js';

	export default {
		data() {
			return {
				background: {
					backgroundImage: 'linear-gradient(90deg,  #FE6387 0%, #F95FA6 100%)'
				},
				value: '',
				fileList: [],
				action: 'https://www.example.com/upload',
				checked: false,
				formData: {
					realName: "", //真实姓名
					idNumber: "", //身份证号码
					idCardFront: "", //身份证正面图片（红娘认证）
					idCardVerso: "", //身份证反面图片（红娘认证）
					authType: "", //认证类型 1用户认证 2红娘认证
					takeProvince: "", //接管区域
					matchPhone: "",
					wxImg: "",
					wxCode: "",
					isSubmit: 0
				},
				show1: false,
				params: {
					city: false,
					province: true,
					area: false,
				}
			}
		},
		onLoad(options) {
			console.log(this.$refs);
			console.log(this.$refs.city);
			this.getHnInfo()
		},
		methods: {
			goXieyi() {
				uni.navigateTo({
					url: '/my/setting/xieyi2'
				})
			},
			showCitySelect() {
				console.log('111');

				this.show1 = true
			},
			confirmProvince(e) {
				this.formData.takeProvince = e.province.label
			},
			uploadImg(key) {
				let that = this;
				uni.chooseImage({
					count: 1,
					sourceType: ['album', 'camera'],
					success: res => {
						for (let i = 0; i < res.tempFilePaths.length; i++) {
							that.$queue.showLoading("上传中...");
							uni.uploadFile({ // 上传接口
								url: that.config("APIHOST1") + '/alioss/upload', //真实的接口地址
								filePath: res.tempFilePaths[i],
								name: 'file',
								success: (uploadFileRes) => {
									that.formData[key] = (JSON.parse(uploadFileRes.data).data)
									uni.hideLoading();
								}
							});
						}
					}
				})
			},
			//校验身份证号码
			checkIdCard(idCard) {
				let reg = /^[1-9]\d{5}(19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[Xx\d]$/;

				if (reg.test(idCard) === false) {
					return false;
				} else {
					return true
				}
			},
			submit() {
				if (this.checked == false) {
					uni.showToast({
						title: '请先同意《协议说明》',
						icon: 'none'
					})
					return
				}
				if (!this.formData.realName) {
					uni.showToast({
						title: "请输入真实姓名",
						icon: "none"
					})
					return
				}
				if (!this.formData.idNumber) {
					uni.showToast({
						title: "请输入身份证号码",
						icon: "none"
					})
					return
				}
				if (!this.checkIdCard(this.formData.idNumber)) {
					uni.showToast({
						title: "请输入正确身份证号码",
						icon: "none"
					})
					return
				}

				if (!this.formData.takeProvince) {
					uni.showToast({
						title: "请选择接管区域",
						icon: "none"
					})
					return
				}
				if (!this.formData.matchPhone) {
					uni.showToast({
						title: "请输入手机号",
						icon: "none"
					})
					return
				}
				if (!this.formData.wxCode) {

					uni.showToast({
						title: "请输入微信号",
						icon: "none"
					})
					return
				}
				let mobileReg = /^[a-zA-Z][a-zA-Z0-9_-]{5,19}$/
				if (!mobileReg.test(this.formData.wxCode)) {
					uni.showToast({
						icon: "none",
						title: "微信号不正确"
					})
					return
				}
				if (!this.formData.wxImg) {
					uni.showToast({
						title: "请上传微信二维码",
						icon: "none"
					})
					return
				}

				if (!this.formData.idCardFront) {
					uni.showToast({
						title: "请上传身份证正面",
						icon: "none"
					})
					return
				}
				if (!this.formData.idCardVerso) {
					uni.showToast({
						title: "请上传身份证反面",
						icon: "none"
					})
					return
				}
				this.$Request.postJson('/app/userCertification/saveUserCertification', {
					...this.formData,
					authType: "2",
				}).then(res => {
					console.log(res);
					if (res.code == 0) {
						uni.showToast({
							title: '提交成功',
							icon: 'success',
							duration: 2000
						});
						setTimeout(() => {
							uni.navigateBack({
								delta: 1
							})
						}, 2000)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							mask: true
						})
					}
				})
			},
			getHnInfo() {
				this.$Request.get('//app/userCertification/getMyUserCertification', {
					// roleId: e.roleId
					authType: 2
				}).then(res => {
					console.log(res);
					if (res.code === 0 && res.data) {
						this.formData = res.data
					} else {
						// this.$queue.showToast(res.msg)
					}
				})
			},
			config: function(name) {
				var info = null;
				if (name) {
					var name2 = name.split("."); //字符分割
					if (name2.length > 1) {
						info = configdata[name2[0]][name2[1]] || null;
					} else {
						info = configdata[name] || null;
					}
					if (info == null) {
						let web_config = cache.get("web_config");
						if (web_config) {
							if (name2.length > 1) {
								info = web_config[name2[0]][name2[1]] || null;
							} else {
								info = web_config[name] || null;
							}
						}
					}
				}
				return info;
			},
		}
	}
</script>

<style lang="less">
	page {
		background: #F2F2F2;
	}

	.bgs {
		width: 100%;
		height: 346rpx;
		// background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		position: fixed;
		/* #ifdef H5 */
		// top: 86rpx;
		/* #endif */
		/* #ifndef H5 */
		// top: 0;
		/* #endif */
		left: 0;
		right: 0;
		z-index: 9;
	}

	.cont {
		position: relative;
		z-index: 99;
		margin-top: 200rpx;

		.box {
			margin: 20rpx 32rpx;
			padding: 30rpx;
			background: #FFFFFF;
			border-radius: 24rpx;

			.name {
				display: flex;
				align-items: center;
				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #00000;

				text {
					color: #FF252B;
				}
			}
		}
	}

	.box2 {
		display: flex;
		align-items: center;
		width: 100%;
		flex-direction: column;
		padding-top: 10rpx;
	}

	.slot-btn {
		margin-top: 30rpx;

		image {
			background-color: #fff;
			position: relative;
			width: 530rpx;
			height: 300rpx;
		}

	}

	.xy {
		margin: 65rpx 0 28rpx 84rpx;
		font-family: PingFang SC;
		font-weight: 500;
		font-size: 24rpx;
		color: #666666;

		.tit {
			color: #FF6684;
		}
	}

	.submit-btn {
		width: 613rpx;
		height: 86rpx;
		background: linear-gradient(107deg, #FF6F9C 36%, #FF8EB6 100%);
		border-radius: 43rpx;
		font-family: PingFang SC;
		font-weight: bold;
		font-size: 28rpx;
		color: #FFFFFF;
		line-height: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto 35rpx;
	}

	.huibox {
		width: 202rpx;
		height: 202rpx;
		background: #F0F1F5;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		z-index: 9999;
	}
</style>

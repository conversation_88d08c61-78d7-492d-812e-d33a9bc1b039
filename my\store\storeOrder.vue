<template>
	<view class="container">
		<!-- 顶部统计区域 -->
		<view class="top-section">
			<view class="stats-cards">
				<view class="stat-card">
					<image class="card-bg" src="https://photo.zastatic.com/images/common-cms/it/20250527/1748342643960_965103_t.png" mode="aspectFill"></image>
					<view class="stat-content">
						<view class="stat-number">{{ orderCount }}</view>
						<view class="stat-label">总订单数</view>
					</view>
				</view>
				<!--<view class="stat-card">
					<image class="card-bg" src="https://photo.zastatic.com/images/common-cms/it/20250527/1748342643941_745864_t.png" mode="aspectFill"></image>
					<view class="stat-content">
						<view class="stat-number">{{ totalRevenue }}</view>
						<view class="stat-label">收益</view>
					</view>
				</view>-->
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list">
			<view class="order-item" v-for="(order, index) in orderList" :key="index">
				<view class="order-header">
					<view class="order-title">{{ order.title }}</view>
					<view class="order-amount">{{ order.amount }}</view>
				</view>
				<view class="order-details">
					<view class="detail-item">
						<text class="detail-label">订单编号：</text>
						<text class="detail-value">{{ order.orderNo }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">订单时间：</text>
						<text class="detail-value">{{ order.orderTime }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">会员名称：</text>
						<text class="detail-value">{{ order.memberName }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">红娘：</text>
						<text class="detail-value">{{ order.matchmaker }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			storeId: '',
			storeName: '',
			orderCount: 10,
			totalRevenue: '8242',
			orderList: [
				{
					title: '购买年度VIP会员',
					amount: '已完成',
					orderNo: 'YH202501150001',
					orderTime: '2025-01-15 09:23:15',
					memberName: '张志强',
					matchmaker: '李雅婷'
				},
				{
					title: '购买季度会员',
					amount: '已取消',
					orderNo: 'YH202501220002',
					orderTime: '2025-01-22 14:56:32',
					memberName: '王美丽',
					matchmaker: '张美琳'
				},
				{
					title: '购买月度会员',
					amount: '已完成',
					orderNo: 'YH202502030003',
					orderTime: '2025-02-03 11:18:47',
					memberName: '刘建国',
					matchmaker: '陈思雨'
				},
				{
					title: '购买高级服务包',
					amount: '已完成',
					orderNo: 'YH202502180004',
					orderTime: '2025-02-18 16:42:21',
					memberName: '陈雅琪',
					matchmaker: '王欣怡'
				},
				{
					title: '购买月度会员',
					amount: '已完成',
					orderNo: 'YH202503050005',
					orderTime: '2025-03-05 10:35:18',
					memberName: '李明华',
					matchmaker: '刘晓敏'
				},
				{
					title: '购买专属红娘服务',
					amount: '已取消',
					orderNo: 'YH202503120006',
					orderTime: '2025-03-12 13:27:54',
					memberName: '赵雅芬',
					matchmaker: '赵婉儿'
				},
				
			]
		}
	},
	onLoad(options) {
		// 获取传递的门店信息
		if (options.storeId) {
			this.storeId = options.storeId
			this.storeName = options.storeName
		}
		this.loadOrderData()
	},
	methods: {
		loadOrderData() {
			// 这里可以调用API获取订单数据
			// this.$Request.get("/app/store/orders", {
			//     storeId: this.storeId
			// }).then(res => {
			//     if (res.code == 0) {
			//         this.orderList = res.data.list
			//         this.orderCount = res.data.orderCount
			//         this.totalRevenue = res.data.totalRevenue
			//     }
			// });
		}
	}
}
</script>

<style scoped>
page {
	background: #f5f5f5;
}

.container {
	min-height: 100vh;
}

.top-section {
	padding: 20rpx;
}

.stats-cards {
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	position: relative;
	height: 160rpx;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-bg {
	position: absolute;
	top: 56rpx;
	right: 50rpx;
	width: 100rpx;
	height: 100rpx;
	z-index: 10;
}

.stat-content {
	position: relative;
	z-index: 2;
	padding: 40rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
    background: #FFFFFF;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	color: #333333;
	margin-left: 50rpx;
}

.stat-label {
	font-size: 28rpx;
	color: #666666;
	margin-left: 50rpx;
}

.order-list {
	padding: 0 20rpx;
}

.order-item {
	background: #FFFFFF;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.order-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.order-amount {
	font-size: 28rpx;
	font-weight: bold;
	color: #999;
}

.order-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.detail-item {
	display: flex;
	font-size: 28rpx;
	line-height: 1.5;
}

.detail-label {
	color: #666666;
	min-width: 160rpx;
}

.detail-value {
	color: #333333;
	flex: 1;
}
</style>

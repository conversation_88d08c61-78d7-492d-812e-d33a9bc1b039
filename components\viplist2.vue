<template>
	<view>
		<!-- @click="clickItem(index,item)" -->
		<view class="listbox" v-for="(item, index) in list" :key="index">
			<view class="userbox">
				<view class="iteimg">
					<image :src="item.userImg ? item.userImg.split(',')[0] : '../static/logo.png'" mode=""></image>
				</view>
				<view class="rightcont">
					<view class="usrbname">{{ item.realName||" " }}
						<image src="../static/images/my/rzicon.png"></image>
					</view>
					<view class="labl">
						<view class="sexicon" v-if="item.sex==1">
							<u-icon name="man" color="#FFFFFF"></u-icon>
							{{item.age}}岁
						</view>
						<view class="sexicons" v-if="item.sex==2">
							<u-icon name="woman" color="#FFFFFF"></u-icon>
							{{item.age}}岁
						</view>
						<view class="">
							{{ item.locationCity }}
							/{{ item.locationCounty }}
						</view>
					</view>
					<view class="tit text-cut">{{ item.feelingAngle||"" }}

						<!-- {{ item.status }}？ -->
					</view>
					<!-- <view class="tit">{{ item.idealAspect||"" }}</view> -->
				</view>
				<!-- <view class="btns" @click="cellMe(item.userPhone)">接管{{ item.sex == 1 ? "他" : item.sex == 2 ? "她" : "" }}

				</view> -->
			</view>
			<view class="flex btns">
				<view class="btn" @click="clickItem(index, item, 0)">
					<image src="/my/static/admin/cancel.png" mode="scaleToFill" />
					取消接管
				</view>
				<view class="btn" @click="clickItem(index, item, 2)">
					<image src="/my/static/admin/edit.png" mode="scaleToFill" />
					牵线成功
				</view>
				<view class="btn" @click="cellMe(item.userPhone)">
					<image src="/my/static/admin/cellMe.png" mode="scaleToFill" />
					联系{{ item.sex == 1 ? "他" : item.sex == 2 ? "她" : "" }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			//列表
			list: {
				type: Array,
				default: []
			},

		},
		methods: {
			clickItem(ind, item, type = 1) {
				this.$emit('click', {
					index: ind,
					item: item,
					type
				});
			},
			cellMe(phone) {
				uni.makePhoneCall({
					phoneNumber: phone //仅为示例
				});
			}
		}
	}
</script>

<style lang="less">
	.listbox {
		background: #FFFFFF;
		border-radius: 24rpx;
		margin: 20rpx 30rpx;
		padding: 20rpx;

		.userbox {
			display: flex;
			align-items: center;
		}

		.iteimg {
			width: 200rpx;
			height: 200rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 12rpx;
			}
		}

		.rightcont {
			flex: 1;
			// height: 200rpx;
			// display: flex;
			// align-items: center;
			// flex-wrap: wrap;
			// align-content: space-between;
			margin-left: 18rpx;

			.usrbname {
				display: flex;
				align-items: center;

				font-size: 32rpx;
				font-family: PingFang SC;
				font-weight: bold;
				color: #292929;

				image {
					width: 40rpx;
					height: 40rpx;
					margin-left: 10rpx;
				}
			}

			.labl {
				display: flex;
				align-items: center;
				font-size: 24rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #999999;
				margin: 20rpx 0;

				.sexicon {
					background: #38CAFF;
					border-radius: 10rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					padding: 4rpx 10rpx;
					margin-right: 10rpx;
				}

				.sexicons {
					background: #edbef3;
					border-radius: 10rpx;
					font-size: 24rpx;
					font-family: PingFang SC;
					font-weight: 500;
					color: #FFFFFF;
					padding: 4rpx 10rpx;
					margin-right: 10rpx;
				}
			}

			.tit {
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				color: #666666;
				min-height: 1em;
				width: 420rpx;
			}
		}

		.btns {
			justify-content: space-between;
			margin-top: 28rpx;

			.btn {
				width: 190rpx;
				height: 69rpx;
				border-radius: 10rpx;
				font-size: 26rpx;
				font-family: PingFang SC;
				font-weight: 500;
				display: flex;
				align-items: center;
				justify-content: center;

				image {
					width: 40rpx;
					height: 40rpx;
					margin-right: 5rpx;
				}

				&:nth-child(1) {
					border: 1px solid #CCCCCC;
					color: #999999;
				}

				&:nth-child(2) {
					border: 1px solid #FF6F9C;
					color: #FF6F9C;
				}

				&:nth-child(3) {
					background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
					color: #FFFFFF;

					image {
						margin-right: 20rpx;
					}
				}
			}

		}
	}
</style>
<template>
	<view class="text-white padding-lr">
		<view class="flex  padding-tb" @click="goNav('/pages/public/pwd')">
			<view class="flex-sub text-df" style="line-height: 50upx;">修改密码</view>
			<image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image>
		</view>
		<view class="flex  padding-tb">
			<view class="flex-sub text-df" style="line-height: 50upx;">消息通知</view>
			<u-switch activeColor="#7075FE" v-model="checked" @change="change()"></u-switch>
			<!-- <image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image> -->
		</view>
		<view class="flex  padding-tb" @click="goNav('/my/setting/logOff')">
			<view class="flex-sub text-df" style="line-height: 50upx;">注销账号</view>
			<image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image>
		</view>
		<!-- <view class="flex  padding-tb" @click="goNav('/my/feedback/index')">
			<view class="flex-sub text-df" style="line-height: 50upx;">意见反馈</view>
			<image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image>
		</view> -->
		<view class="flex  padding-tb" @click="goNav('/my/setting/xieyi')">
			<view class="flex-sub text-df" style="line-height: 50upx;">用户协议</view>
			<image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image>
		</view>
		<view class="flex  padding-tb" @click="goNav('/my/setting/mimi')">
			<view class="flex-sub text-df" style="line-height: 50upx;">隐私政策</view>
			<image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image>
		</view>
		<view class="flex  padding-tb" @click="goNav('/my/setting/about')">
			<view class="flex-sub text-df" style="line-height: 50upx;">关于我们</view>
			<image src="../../static/images/my/right.png" style="width: 15rpx;height: 30rpx;"></image>
		</view>
		<view class="btn" @click="goOut">退出登录</view>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				checked: true,
			}
		},
		onLoad() {
			this.getUserInfo()
		},
		methods: {
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						console.log(res.data.isSendMsg)
						this.checked = res.data.isSendMsg == 1 || res.data.isSendMsg == null ? true : false
					}
				});
			},
			change(status) {
				console.log(status);
				this.$Request.post('/app/user/updateSendMsg', {
					isSendMsg: status ? 1 : 2
				}).then(res => {
					if (res.code == 0) {

					}
				});
			},
			goNav(e) {
				uni.navigateTo({
					url: e
				})
			},
			//确认
			meHandleBtn() {
				let that = this
				if (this.meIndex == 'm1') {
					uni.removeStorageSync('userName')
					uni.removeStorageSync('avatar')
					uni.removeStorageSync('userId')
					uni.removeStorageSync('token')
					uni.removeStorageSync('phone')
					uni.removeStorageSync('zhiFuBaoName')
					uni.removeStorageSync('zhiFuBao')
					uni.removeStorageSync('invitationCode')
					uni.removeStorageSync('unionId')
					uni.removeStorageSync('openId')
					uni.removeStorageSync('isVIP')

					uni.showToast({
						title: '退出成功！',
						icon: 'none'
					})
					setTimeout(function() {
						uni.navigateBack()
					}, 1000)
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			goOut() {
				this.meShowModel = true
				this.meTitle = '提示'
				this.meContent = '确定退出登录吗？'
				this.meIndex = 'm1'
			}
		}

	}
</script>

<style>
	page {
		background: #FFFFFF;
	}

	.btn {
		width: 100%;
		height: 80upx;
		background: #7075FE;
		border-radius: 6upx;
		text-align: center;
		line-height: 80upx;
		margin-top: 40upx;
		font-size: 34upx;
		color: #fff;
	}
</style>
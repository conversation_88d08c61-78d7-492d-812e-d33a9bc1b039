<template>
	<view class="notice-container">
		<!-- 顶部导航栏 -->
		<u-navbar title="消息通知" :is-back="true" :background="background"></u-navbar>

		<!-- 消息列表 -->
		<scroll-view class="notice-list" scroll-y="true">
			<view
				class="notice-item"
				v-for="item in noticeList"
				:key="item.id"
				@click="handleNoticeClick(item)">

				<!-- 时间戳 -->
				<view class="notice-time">{{ item.time }}</view>

				<!-- 消息内容 -->
				<view class="notice-content">
					<!-- 头像 -->
					<view class="notice-avatar">
						<image class="avatar-img" :src="item.avatar" mode="aspectFill"></image>
					</view>

					<!-- 右侧内容区域 -->
					<view class="notice-right">
						<!-- 标题 -->
						<view class="notice-title">{{ item.title }}</view>

						<!-- 消息文本 -->
						<view class="notice-text">{{ item.content }}</view>

						<!-- 操作按钮 -->
						<view class="notice-action" v-if="item.actionText">
							<text class="action-text">{{ item.actionText }}</text>
							<text class="action-arrow">></text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			background: {
				background: '#ffffff'
			},
			noticeList: [
				{
					id: 1,
					time: '05-28 13:47',
					avatar: '/static/images/system-avatar.png',
					title: '身份开通',
					content: '您的合同已成功激活，恭喜你成为红娘！条件多多！福利多多',
					actionText: '',
					actionUrl: ''
				},
				{
					id: 2,
					time: '00:01',
					avatar: '/static/images/system-avatar.png',
					title: '身份开通',
					content: '您的合同已成功激活，恭喜你成为红娘！条件多多！福利多多',
					actionText: '去完善资料',
					actionUrl: '/pages/profile/complete'
				}
			]
		}
	},

	onLoad() {
		// 页面加载时的初始化
		this.loadNoticeList();
	},

	methods: {
		// 加载通知列表
		loadNoticeList() {
			// 这里可以调用API获取通知数据
			console.log('加载通知列表');
		},

		// 处理通知点击
		handleNoticeClick(item) {
			console.log('点击通知:', item);
			if (item.actionUrl) {
				uni.navigateTo({
					url: item.actionUrl
				});
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.notice-container {
	height: 100vh;
	background-color: #f8f8f8;
	display: flex;
	flex-direction: column;
}

.notice-list {
	flex: 1;
	padding: 0 32rpx;
	background-color: #f8f8f8;
}

.notice-item {
	margin-bottom: 32rpx;
	cursor: pointer;
}

.notice-time {
	text-align: center;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 24rpx;
	padding-top: 32rpx;
}

.notice-content {
	background: #ffffff;
	border-radius: 16rpx;
	padding: 32rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
	display: flex;
	align-items: flex-start;
}

.notice-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	overflow: hidden;
	margin-right: 24rpx;
	background: #e8f4ff;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
}

.avatar-img {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
}

.notice-right {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.notice-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.notice-text {
	font-size: 28rpx;
	color: #666666;
	line-height: 1.6;
	margin-bottom: 24rpx;
}

.notice-action {
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.action-text {
	font-size: 28rpx;
	color: #ff6b6b;
	margin-right: 8rpx;
}

.action-arrow {
	font-size: 24rpx;
	color: #ff6b6b;
	font-weight: bold;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.notice-title {
		font-size: 30rpx;
	}

	.notice-text {
		font-size: 26rpx;
	}

	.action-text {
		font-size: 26rpx;
	}
}
</style>
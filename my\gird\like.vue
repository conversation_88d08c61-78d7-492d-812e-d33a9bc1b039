<template>
	<view>
		<view class="margin-lr flex flex-wrap margin-top justify-between" v-if="dataList.length!=0">
			<view v-for="(item,index) in dataList" :key="index"  @click="isVip?godetail(item):''" >
				<view class="likebox" :class="!isVip?'huibg':'bg'" v-if="item.userData">
					<image :src="item.userData.userImg"></image>
					<view class="padding-tb-xs" :class="isVip?'fw':''">
						<view class="title">
							<!-- {{item.userData}} -->
							<text v-if="isVip">{{item.byUserName}}</text>
							<text v-else>{{item.userData.realName}}</text>
							<view class="sex" v-if="!isVip">
								<u-icon name="man" color="#FFFFFF" size="20"></u-icon>
							</view>
						</view>
						<view class="tit" v-if="isVip">
							{{item.userData.age?item.userData.age:'0'}}岁/{{item.userData.userHeight}}cm/{{item.userData.locationCity}}
						</view>
						<view style="width: 100%;height: 1px;background: #F2F2F2;" v-if="!like"></view>
						<!-- 	<view class="chat" v-if="!isVip">
							<u-icon name="chat-fill" color="#6367FF" size="40"></u-icon>
							<text>获取联系</text>
						</view> -->
						<view class="chat"v-if="item.isGetPhone!=1">
							<u-icon name="chat-fill" color="#6367FF" size="40"></u-icon>
							<text>获取联系方式</text>
						</view>
						<view class="chat" @click.stop="isVip?callPhone(item):''" v-else>
							<u-icon name="chat-fill" color="#6367FF" size="40"></u-icon>
							<text>联系TA</text>
						</view>
					</view>
				</view>

			</view>
		</view>
		<empty v-if="dataList.length==0"></empty>
		<view class="btns" v-if="!isVip" @click="govip">解锁全部喜欢我的人</view>
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				like: true,
				page: 1,
				limit: 10,
				dataList: [],
				isVip: ''
			}
		},
		onLoad() {

		},
		onShow() {
			this.userId = uni.getStorageSync('userId')
			this.getUserInfo()
			this.getList()

		},
		methods: {
			callPhone(item) {
				this.$Request.getT('/app/userGetPhoneRecord/getPostPushPhone?userId=' + item.userData.userId).then(
					res => {
						if (res.code == 0) {
							let phone = res.data
							uni.makePhoneCall({
								phoneNumber: phone
							})
						}
					});
			},
			godetail(item) {
				uni.navigateTo({
					url: '/package/pages/game/detail?byUserId=' + item.userData.userId
				})
			},
			govip() {
				uni.navigateTo({
					url: '/my/vip/index'
				})
			},
			getUserInfo() {
				this.$Request.get("/app/user/selectUserById").then(res => {
					if (res.code == 0) {
						// 会员  0不是  1是
						if (res.data.isVip && res.data.isVip == 1) {
							this.isVip = true
							uni.setStorageSync('isVIP', this.isVip)
						} else {
							this.isVip = false
							uni.setStorageSync('isVIP', this.isVip)
						}
					}
				});
			},
			getList() {
				let data = {
					page: this.page,
					limit: this.limit,
					type: 2,
					// byUserId: this.userId
				}
				this.$Request.get("/app/scFollow/getScFollowMyList", data).then(res => {
					if (res.code == 0) {
						res.data.records.forEach((d, index) => {
							if (d.userData && d.userData.userImg) {
								let userImg = d.userData.userImg.split(',')
								d.userData.userImg = userImg[0]
							}
						})
						if (this.page == 1) {
							this.dataList = res.data.records
						} else {
							this.dataList = [...this.dataList, ...res.data.records]
						}
					} else {
						console.log(res.msg)
					}
					uni.hideLoading();
					uni.stopPullDownRefresh();
				});
			}
		},
		onReachBottom: function() {
			this.page = this.page + 1;
			this.getList()
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getList()
		},
	}
</script>

<style lang="less">
	page {
		background: #F2F6FC;
	}

	.huibg {
		background: #1B1B1B;
		opacity: 0.3;
		width: 333rpx;
		height: 426rpx;
		// background-color: rgba(0, 0, 0, 0.5);
		position: relative;

		image {
			opacity: 0.5;
			height: 426rpx !important;
			border-radius: 8rpx !important;
			// background-color: rgba(0, 0, 0, 0.9);
		}

		.fw {
			position: absolute;
			bottom: 0;
			left: 0;
			right: 0;
			z-index: 99;

			.title {
				color: #FFFFFF !important;
			}

			.tit {
				color: #FFFFFF !important;
			}
		}
	}

	.bg {
		background: #FFFFFF;
	}

	.likebox {
		width: 333rpx;

		border-radius: 16rpx 16rpx 16rpx 24rpx;

		image {
			width: 333rpx;
			height: 340rpx;
			border-radius: 8rpx 8rpx 0rpx 0rpx;
		}

		.title {
			font-size: 32rpx;
			font-family: PingFang SC;
			font-weight: bold;
			color: #292929;
			padding: 0 20rpx;
			display: flex;
			align-items: center;
			max-width: 200rpx;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;

			.sex {
				width: 32rpx;
				height: 32rpx;
				background: #38CAFF;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 10rpx;
			}
		}

		.tit {
			font-size: 26rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
			padding: 10rpx 20rpx;
		}

		.chat {
			font-size: 24rpx;
			font-family: PingFang SC;

			color: #686CFF;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 10rpx;

			text {
				margin-left: 8rpx;
			}
		}
	}

	.btns {
		width: 686rpx;
		height: 110rpx;
		background: #171F2C;
		border-radius: 55rpx;
		font-size: 32rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFF3CC;
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: 30rpx;
		left: 0;
		right: 0;
		z-index: 99;
	}
</style>
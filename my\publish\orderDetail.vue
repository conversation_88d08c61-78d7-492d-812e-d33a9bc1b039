<template>
	<view>
		<view class="margin-lr padding  margin-top-sm" style="background: #FFFFFF;border-radius: 24rpx;">
			<view class="flex">
				<image :src="order.game.gameImg" style="width: 76rpx;height: 76rpx;border-radius: 10rpx;"></image>
				<view class="margin-left-xs flex align-center">
					<view class="margin-right-sm text-lg text-bold">{{order.game.gameName}}</view>
					<view class="round0 " style="padding: 5rpx 30rpx;" v-if="order.sex == 0">
						不限
					</view>
					<view v-if="order.sex == 1" class="round"
						style="display: inline-block;background-color:#e7fbff; color: #6EE3FB;padding: 2upx 12upx;">
						<u-icon name="man" color="#6EE3FB" size="24"></u-icon>
						男生
					</view>
					<view v-if="order.sex == 2" class="round"
						style="display: inline-block;background-color: #FBE8EE; color: #FF71A1;padding: 2upx 12upx;">
						<u-icon name="woman" color="#FF71A1" size="24"></u-icon>
						女生
					</view>
				</view>
			</view>
			<view class="flex margin-top" v-if="order.myArea">
				<view class="flex align-center" style="color: #B8B8CC;">
					<text style="color: #B8B8CC;" class="margin-right-xs" v-if="order.myLevel">玩家段位</text>
					<text style="color: #B8B8CC;" class="margin-lr-xs" v-if="order.myArea">/ 大区：</text>
				</view>
				<view style="color: #000;width: 50%;">{{order.myLevel}} <text v-if="order.myArea"
						style="margin-left: 10rpx;"> / {{order.myArea}}</text></view>
			</view>

			<view class="flex margin-top-sm" v-if="order.sex ">
				<view style="color: #B8B8CC;">性别要求：</view>
				<view v-if="order.sex == 0">不限</view>
				<view v-if="order.sex == 1">男</view>
				<view v-if="order.sex ==2">女</view>
			</view>
			<view class="flex margin-top-sm" v-if="order.skill">
				<view style="color: #B8B8CC;">技能水平：</view>
				<view>{{order.skill}}</view>
			</view>
			<view class="flex margin-tb-sm padding-top-xs" v-if="order.orderLevel">
				<view style="color: #B8B8CC;">包上分段位：</view>
				<view>{{order.orderLevel}}</view>
			</view>
			<view class="flex margin-tb-sm" v-if="order.location">
				<view style="color: #B8B8CC;">期望TA：</view>
				<view>{{order.location}} </view>
			</view>
			<view class="flex margin-top-sm" v-if="order.money">
				<text style="color: #B8B8CC;">价格要求：</text>
				<view>{{order.money}}
					<image src="../../static/share/icon_QQkongjian.png"
						style="width: 20rpx;height: 20rpx;margin: 0rpx 5rpx;"></image>/局
				</view>
			</view>
			<view class="flex margin-top-sm" v-if="order.num">
				<view style="color: #B8B8CC;">局数要求：</view>
				<view>{{order.num}}</view>
			</view>
			<view class="flex margin-top-sm" v-if="order.orderTakingTime">
				<view style="color: #B8B8CC;">派单时间：</view>
				<view>{{order.orderTakingTime}}</view>
			</view>
			<view class="margin-top-sm flex">
				<view style="color: #B8B8CC;" class="text-df">其他要求：</view>
				<view class="text-sm ">{{order.remark?order.remark:'暂无其他要求'}}</view>
			</view>
		</view>
		<view class="margin-lr padding  margin-top-sm" style="background: #FFFFFF;border-radius: 24rpx;">
			<!-- #ifndef H5 -->
			<view class="">
				<view>
					<view>语音介绍</view>
					<view>录制一段自我介绍的语音(介绍自己擅长位置，英雄，游戏成就 等皆可提升老板下单量)</view>
				</view>
				<jsfun-record :maxTime="60" :minTime="5" @okClick="saveRecord" @start='select' v-if="status == 1">
					<view class="flex justify-between text-center margin-top">
						<view class="padding-top">
							<!-- <image style="width: 120rpx;height: 120rpx;" src="../static/reset.png"></image> -->
						</view>
						<view>
							<view>
								<image style="width: 184rpx;height: 184rpx;" src="../static/luyin.png"></image>
							</view>
						</view>
						<view class="padding-top">
							<!-- <image style="width: 120rpx;height: 120rpx;" src="../static/confirm.png"></image> -->
						</view>
					</view>
					<view class="flex justify-between text-center">
						<view style="width: 120rpx;"></view>

						<view style="width: 184rpx;">点击录制</view>

						<view style="width: 120rpx;"></view>
					</view>
				</jsfun-record>
				<view class="flex justify-between margin-top-sm" v-if="status == 2">
					<view class="u-rela" style="width: 360rpx;" @click="playVoice">
						<image src="../static/15.png" style="width: 360rpx;height: 78rpx;"></image>
						<view class="flex justify-between u-abso"
							style="width: 326rpx;height: 58rpx;line-height: 58rpx;top:10rpx;left: 0;right: 0;margin: auto;">
							<image v-if="isPlay" src="../static/stop1.png" style="width: 58rpx;height: 58rpx;"></image>
							<image v-else src="../static/play1.png" style="width: 58rpx;height: 58rpx;"></image>
							<image src="../static/yinpin.png" style="width: 185rpx;height: 58rpx;"></image>
							<view>{{recordLength?recordLength+'S':'未知'}}</view>
						</view>
					</view>
					<u-icon @click="clearAudio()" name="close" size="40"></u-icon>
				</view>
			</view>
			<!-- #endif -->
			
			<!-- #ifndef H5 -->
			<view class="margin-top">
				<!-- #endif -->
				<!-- #ifdef H5 -->
				<view>
					<!-- #endif -->
					<view class="text-df text-bold">文字介绍</view>
					<view>
						<u-input v-model="content" type="textarea" placeholder="如：绍自己擅长位置，英雄，游戏成就 等皆可可提升老板下单量哦"
							height="300" maxlength="300" @input="confirmDetails" />
					</view>
				</view>
			</view>

			<!-- dingw -->
			<view class="margin-lr flex justify-between margin-top" style="margin-bottom: 120upx;">
				<view class="btn2" @click="back()">取消抢单</view>
				<view class="btn" @click="cancel()">确认抢单</view>
			</view>


			<!-- 抢单弹框 -->
			<u-popup v-model="show" mode="center" width="560rpx" border-radius="14" closeable="true"
				close-icon="close-circle" close-icon-size="48" close-icon-color="#000000" @close="qiangdan">
				<view class="text-center"
					style="padding-top:40rpx;background: linear-gradient(-200deg, #e2d6fe 10%, #ffffff 40%);">
					<view class="text-df text-bold">已提交抢单</view>
					<view class="popuptit">
						您已成功提交抢单！可在“我的抢单” 查看该订单，抢单成功后可在 “我的接单”查看该订单。
					</view>
					<view class="btn1" @click="qiangdan()">我已阅读并了解</view>
				</view>
			</u-popup>
			<!-- modal弹窗 -->
			<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
				@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
				:cancel-text='meCancelText'></u-modal>
		</view>
</template>

<script>
	import configdata from '../../common/config.js';
	import jsfunRecord from '../components/jc-record/jc-record.vue';
	export default {
		components: {
			jsfunRecord
		},
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				show: false,
				fastOrderId: '',
				order: [],
				status: 1,
				//录音相关参数
				// #ifndef H5
				//H5不能录音
				RECORDER: uni.getRecorderManager(),
				// #endif

				recording: false,
				willStop: false,
				infos: {},
				recordLength: 0, //录音时长
				content: '', //接单文字 
				voice: '', //接单语音
				sec: '', //语音秒数
				AUDIO: uni.createInnerAudioContext(),
			}
		},
		onLoad(option) {
			this.fastOrderId = option.fastOrderId
			if (this.fastOrderId) {
				this.getOrderDet()
			}
		},
		onShow() {

		},
		onReady() {
			this.AUDIO.onEnded(function(res) {
				this.isPlay = false;
			});
		},
		onUnload() {
			if (this.isPlay) {
				this.AUDIO.stop();
			}
			this.isPlay = !this.isPlay;
		},
		methods: {
			playVoice() {
				console.log(this.isPlay)
				this.AUDIO.src = this.form.voiceIntroduce;
				if (this.isPlay) {
					this.AUDIO.stop();
				} else {
					this.AUDIO.play();
				}
				this.isPlay = !this.isPlay;
			},
			//订单详情
			getOrderDet() {
				this.$queue.showLoading("加载中...");
				let data = {
					fastOrderId: this.fastOrderId
				}
				this.$Request.get('/app/fastOrder/selectFastOrderDetails', data).then(res => {
					if (res.code == 0) {
						this.order = res.data
						if (this.order.templateType == 2) {
							if (this.order.serviceType) {
								let serviceType = ''
								serviceType = this.order.serviceType.split('~')
								this.order.serviceType = serviceType[0]
							}
						}
					} else {
						uni.hideLoading();
						this.$queue.showToast(res.msg)
					}
					uni.stopPullDownRefresh();
					uni.hideLoading();
				});
			},
			//取消抢单
			back() {
				uni.navigateBack()
			},
			//跳转抢单
			qiangdan() {
				uni.redirectTo({
					url: '/package/pages/play/robOrder'
				})
			},
			//确认
			meHandleBtn() {
				let that = this
				if (this.meIndex == 'm1') {
					that.$Request.postJson("/app/fastOrder/insertFastOrderTake", that.infos).then(
						ret => {
							if (ret.code == 0) {
								that.show = true
							} else {
								uni.showToast({
									title: ret.msg,
									icon: 'none'
								})
							}
						});
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			//确认抢单
			cancel() {

				let that = this
				let data = {
					fastOrderId: that.fastOrderId,
					content: that.content,
					voice: this.voice,
					sec: this.sec
				}
				this.meShowModel = true
				this.meTitle = '抢单提示'
				this.meContent = '确认抢单吗?'
				this.meIndex = 'm1'
				this.infos = data
			},
			select(e) {
				this.status = e
			},
			saveRecord(recordPath) {
				console.log("===音频文件地址：" + recordPath + "===")
				uni.showLoading({
					title: '录音上传中...'
				})
				let that = this;
				uni.uploadFile({
					url: that.config("APIHOST1") + '/alioss/uploadMusic',
					filePath: recordPath,
					name: 'file',
					success: (uploadFileRes) => {
						console.log(JSON.parse(uploadFileRes.data))
						this.voice = JSON.parse(uploadFileRes.data).data.url
						this.sec = JSON.parse(uploadFileRes.data).data.sec
						this.recordLength = JSON.parse(uploadFileRes.data).data.sec
						uni.hideLoading()
					},
					fail: (err) => {
						console.log(err)
						uni.hideLoading()
					}
				});
			},
			config: function(name) {
				var info = null;
				if (name) {
					var name2 = name.split("."); //字符分割
					if (name2.length > 1) {
						info = configdata[name2[0]][name2[1]] || null;
					} else {
						info = configdata[name] || null;
					}
					if (info == null) {
						let web_config = cache.get("web_config");
						if (web_config) {
							if (name2.length > 1) {
								info = web_config[name2[0]][name2[1]] || null;
							} else {
								info = web_config[name] || null;
							}
						}
					}
				}
				return info;
			},
			// 清除录音
			clearAudio() {
				this.status = 1
				this.recordLength = 0
				this.form.voiceIntroduce = ''
			},
		}
	}
</script>

<style>
	.round0 {
		background: #f2f2f2;
		padding: 1px 8px 3px 6px;
		color: #000000;
		border-radius: 30rpx;
	}

	.round1 {
		background: #3A2FED;
		padding: 1px 8px 3px 6px;
		color: #FFFFFF;
		border-radius: 30rpx;
	}

	.round2 {
		background: #FF164A;
		padding: 1px 8px 3px 6px;
		color: #FFFFFF;
		border-radius: 30rpx;
	}

	.dingw {
		position: fixed;
		left: 0;
		right: 0;
		bottom: 80rpx;
		z-index: 9;
	}

	.btn {
		/* background: linear-gradient(90deg, #E6CCA7 0%, #DFBC94 100%); */
		border-radius: 16upx;
		/* flex: 1; */
		padding: 25rpx 0rpx;
		font-weight: bold;
		font-size: 32upx;
		color: #FFFFFF;
		/* border: 1rpx solid #AC75FE; */
		background: #AC75FE;
		width: 48%;
		text-align: center;
	}

	.btn2 {
		/* background: linear-gradient(90deg, #E6CCA7 0%, #DFBC94 100%); */
		border-radius: 16upx;
		/* flex: 1; */
		width: 48%;
		text-align: center;
		padding: 25rpx 0rpx;
		font-weight: bold;
		font-size: 32upx;
		color: #999999;
		/* background: #cccccc; */
		border: 1rpx solid #cccccc;
	}



	.popuptit {
		width: 520upx;
		margin: 30rpx auto;
		font-size: 28upx;
		font-family: PingFang SC;
		/* font-weight: 500; */
		color: #000;
	}

	.btn1 {
		color: #AC75FE;
		background: #EADEFF;
		/*width: 55upx;
		margin: 0 auto; */
		padding: 20rpx 60rpx;
		display: inline-block;
		border-radius: 55rpx;
		margin-top: 10rpx;
		margin-bottom: 75rpx;
	}
</style>
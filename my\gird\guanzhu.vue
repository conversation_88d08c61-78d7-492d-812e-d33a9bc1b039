<template>
	<view class="content">
		<view v-if="dataList.length != 0" class="bg u-flex u-p-l-30 u-p-t-30 u-p-b-10 u-p-r-30"
			v-for="(item,index) in dataList" :key='index'>

			<block v-if="type!=1">
				<view class="u-m-r-10" @click="goGuanZhuDetail(item.byUserId)">
					<u-avatar :src="item.byAvatar?item.byAvatar: '../../static/logo.png'" size="100"></u-avatar>
				</view>
				<view class="u-flex-1  margin-left-xs">
					<view class="u-font-16  text-bold">{{item.byUserName}}</view>
					<view class="flex align-center margin-top-xs" v-if="item.userData&&item.userData.age">
						<view class="sexicon" v-if="item.bySex==1">
							<u-icon name="man" color="#FFFFFF"></u-icon>
							{{item.userData&&item.userData.age?item.userData.age:'0'}}
						</view>
						<view class="sexicon" v-if="item.bySex==2">
							<u-icon name="woman" color="#FFFFFF"></u-icon>
							{{item.userData&&item.userData.age?item.userData.age:'0'}}
						</view>
						<!-- <view class="text-sm margin-left-xs" style="color: #999999;">{{item.userData.locationCity}}
						</view> -->
					</view>
				</view>
			</block>
			<block v-else>
				<view class="u-m-r-10">
					<u-avatar :src="item.avatar?item.avatar: '../../static/logo.png'" size="100"></u-avatar>
				</view>
				<view class="u-flex-1  margin-left-xs">
					<view class="u-font-16  text-bold">{{item.userName}}</view>
					<view class="flex align-center margin-top-xs">
						<view class="sexicon" v-if="item.bySex==1">
							<u-icon name="man" color="#FFFFFF"></u-icon>
							{{item.userData.age?item.userData.age:'0'}}
						</view>
						<view class="sexicon" v-if="item.bySex==2">
							<u-icon name="woman" color="#FFFFFF"></u-icon>
							{{item.userData.age?item.userData.age:'0'}}
						</view>
						<!-- 	<view class="text-sm margin-left-xs" style="color: #999999;">{{item.userData.locationCity}}
						</view> -->
					</view>
				</view>
			</block>
			<view v-if=" type == 2" @click="insert(item)" class="round"
				style="color: white;background: #FF769D;padding: 15upx 24upx;width: 150upx;text-align: center;font-size: 22upx;">
				取消关注</view>
			<!-- <view>
				<view v-if="item.status == 1" @click="insert(item)" class="round"
					style="color: white;background: #474966;padding: 10upx 24upx;width: 150upx;text-align: center;font-size: 22upx;">
					互相关注</view>
				<view v-if="item.status == 2 && type == 1" @click="insert(item)" class="round"
					style="color: white;background: #474966;padding: 10upx 24upx;width: 150upx;text-align: center;font-size: 22upx;">
					回关</view>
				<view v-if="item.status == 2 && type == 2" @click="insert(item)" class="round"
					style="color: white;background: #474966;padding: 10upx 24upx;width: 150upx;text-align: center;font-size: 22upx;">
					已关注</view>
			</view> -->
		</view>

		<empty v-if="dataList.length == 0"></empty>
	</view>
</template>

<script>
	import empty from '../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				dataList: [],
				type: 1,
				page: 1,
				limit: 10,
				userId: ''
			}
		},
		onLoad(e) {
			console.log(e)
			this.$queue.showLoading("加载中...");
			this.userId = uni.getStorageSync('userId')
			uni.setNavigationBarTitle({
				title: e.name
			})
			this.type = e.type
			if (this.type == 1) {
				this.getFansList()

			} else {
				this.getFollowList()
			}
		},
		methods: {
			goGuanZhuDetail(userId) {
				// uni.navigateTo({
				// 	url: '/my/gird/guanzhuDetail?userId=' + userId
				// });
				uni.navigateTo({
					url: '/package/pages/game/dongtai?byUserId=' + userId
				})
			},
			// 获取粉丝数量
			getFansList() {
				let data = {
					page: this.page,
					limit: this.limit,
					type: 1,
					// byUserId: this.userId
				}
				this.$Request.get("/app/scFollow/getScFollowMyList", data).then(res => {
					uni.hideLoading();
					if (res.code == 0) {
						if (this.page == 1) {
							this.dataList = res.data.records
						} else {
							this.dataList = [...this.dataList, ...res.data.records]
						}
					} else {
						console.log(res.msg)
					}
					uni.stopPullDownRefresh();
				});
			},
			// 获取关注数量
			getFollowList() {
				let data = {
					page: this.page,
					limit: this.limit,
					type: 1,
					// userId: this.userId
				}
				this.$Request.get("/app/scFollow/getMyScFollowList", data).then(res => {
					if (res.code == 0) {
						if (this.page == 1) {
							this.dataList = res.data.records
						} else {
							this.dataList = [...this.dataList, ...res.data.records]
						}
					} else {
						console.log(res.msg)
					}
					uni.hideLoading();
					uni.stopPullDownRefresh();
				});
			},
			insert(e) {
				this.$Request.postT('/app/scFollow/saveScFollow?byUserId=' + e.byUserId + '&type=1').then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.page = 1;
						this.getFollowList()
					}
				});
				// let that = this
				// let data = {
				// 	followUserId: e.userId
				// }
				// that.$Request.get("/app/userFollow/insert", data).then(res => {
				// 	console.log(res)
				// 	if (res.code == 0) {
				// 		uni.showToast({
				// 			title: res.msg,
				// 			icon: 'none'
				// 		})
				// 		setTimeout(function() {
				// 			if (that.type == 1) {
				// 				that.getFansList()
				// 			} else {
				// 				that.getFollowList()
				// 			}
				// 		}, 500)
				// 	}
				// });

			}
		},
		onReachBottom: function() {
			this.page = this.page + 1;
			if (e.type == 1) {
				this.getFansList()
			} else {
				this.getFollowList()
			}
		},
		onPullDownRefresh: function() {
			this.page = 1;
			// this.dataList = []
			if (this.type == 1) {
				this.getFansList()
			} else {
				this.getFollowList()
			}
		},
	}
</script>

<style>
	page {
		background: #FFFFFF;
	}

	.sexicon {
		width: 58rpx;
		height: 30rpx;
		background: #38CAFF;
		border-radius: 10rpx;
		font-size: 20rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
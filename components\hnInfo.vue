<!--
 * @name: 
 * @Author: 刘大可
 * @Date: 2024-03-06 17:59:12
 * @LastEditors: 刘大可
 * @LastEditTime: 2024-03-08 11:08:12
-->
<template>
  <div class="hnInfo">
    <u-popup v-model="hnshow" mode="bottom" border-radius="30" :closeable="true" close-icon-color="#000000">
        <view class="hnpopup">
          <view class="title">专属资深红娘助你脱单</view>
          <view class="box">
            <view class="hnavatr">
              <image src="@/static/logo.png"></image>
            </view>
            <view class="hnname">{{ hnInfo.realName }}</view>
            <view class="hnwx" v-if="hnInfo.wxCode">
              微信：{{ hnInfo.wxCode }}
              <image src="@/static/images/hn/copy.png" @click="copyOrder(hnInfo.wxCode)"></image>
            </view>
            <view class="hxwxm" v-if="hnInfo.wxImg">
              <image :show-menu-by-longpress="true" @click="previewImage(hnInfo.wxImg)"
                :src="hnInfo.wxImg">
              </image>
            </view>
            <view class="hntit" v-if="hnInfo.wxImg">长按识别二维码添加红娘微信</view>
            <view class="btn" @click="makePhone(hnInfo.matchPhone)">打电话</view>
          </view>
        </view>
      </u-popup>

  </div>
</template>

<script>
import Vue from 'vue';
export default Vue.extend({
  name: "hnInfo",
  props: {
    // hnshow:{
    //   type:Boolean,
    //   default:true
    // },
    // hnInfo:{
    //   type:Object,
    //   default:()=>{}
    // }
  },
  data() {
    return {
      hnshow: false,
      hnInfo: {}
    }
  },
  computed: {},
  methods: {
    open(data){
      this.$Request.get('/app/userRoleMatchmaker/getMatchInfoByUserId', {
        // roleId: e.roleId
        userId: data.userId
      }).then(res => {
        console.log(res);
        if (res.code === 0) {
          this.hnInfo = res.data
          this.hnshow = true
        } else {
          this.$queue.showToast(res.msg)
        }
      })
    },
    makePhone(e) {
      uni.makePhoneCall({
        phoneNumber: e
      })
    },
    copyOrder(content) {
      let that = this;
      uni.setClipboardData({
        data: content,
        success: function () {
          that.$queue.showToast('复制成功')
        }
      })
    },
  },
  watch: {},

  // 组件周期函数--监听组件挂载完毕
  mounted() {},
  // 组件周期函数--监听组件数据更新之前
  beforeUpdate() {},
  // 组件周期函数--监听组件数据更新之后
  updated() {},
  // 组件周期函数--监听组件激活(显示)
  activated() {},
  // 组件周期函数--监听组件停用(隐藏)
  deactivated() {},
  // 组件周期函数--监听组件销毁之前
  beforeDestroy() {},
}) 
</script>

<style scoped lang="scss">
    .hnpopup {
      background: linear-gradient(0deg, #FFFFFF 40%, #FFE4E9 100%);
      padding-bottom: 40rpx;
  
      .title {
        font-size: 30rpx;
        font-family: PingFang SC;
        font-weight: 800;
        color: #000000;
        text-align: center;
        padding: 30rpx 0;
      }
  
      .box {
        background: #FFFFFF;
        border: 1px solid #FFE9ED;
        border-radius: 24rpx;
        margin: 40rpx 72rpx 0;
        padding-bottom: 40rpx;
        text-align: center;
        position: relative;
  
  
        .hnavatr {
          position: absolute;
          top: -40rpx;
          left: 0;
          right: 0;
          z-index: 9;
  
          image {
            width: 120rpx;
            height: 120rpx;
            border-radius: 50%;
          }
        }
  
        .hnname {
          font-size: 30rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #FF6684;
          margin-top: 100rpx;
        }
  
        .hnwx {
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #999999;
          margin-top: 10rpx;
          display: flex;
          align-items: center;
          justify-content: center;
  
          image {
            width: 23rpx;
            height: 25rpx;
            margin-left: 10rpx;
          }
        }
  
        .hxwxm {
          margin-top: 40rpx;
  
          image {
            width: 299rpx;
            height: 282rpx;
            border-radius: 24rpx;
          }
        }
  
        .hntit {
          font-size: 26rpx;
          font-family: PingFang SC;
          font-weight: 500;
          color: #999999;
        }
  
        .btn {
          width: 505rpx;
          height: 86rpx;
          margin: 0 auto;
          background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);
          border-radius: 43rpx;
          font-size: 28rpx;
          font-family: PingFang SC;
          font-weight: bold;
          color: #FFFFFF;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 40rpx;
        }
      }
  
    }


</style>
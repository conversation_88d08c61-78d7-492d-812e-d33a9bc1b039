import md5 from "./md5.js"
// import { socketUrl } from '../config.js'
// import { imSign } from "../api/common.js";
// import { initMessageCount } from "./main/tabbar.js";
// 初始化im
const socketUrl = 'wss://nim.zhenai.com'
let SocketTask;
// 页面消息回调对象
let queueCB = {}
// 心跳定时器
let interval = null


// 首次进入小程序初始化
function start(){
	// 登录im
	init()
	// 上线
	// wx.onAppShow((e)=>{
	// 	init()
	// })
	// // 下线
	// wx.onAppHide((e)=>{
	// 	SocketTask?.close()
	// 	clearInterval(interval)
	// })

}
function makeConnectParams() {
	const ts = Date.now(),
	secret = 'seed_im_original_msg_topic_bus_1',
	businessId = 26,
	token = "9eb79c19-f00b-4fb3-8297-8c2d3b206100",
	userId = 1000316622,
	uuid = guid(),
	sign = md5(`${token}_${userId}`);
	return {
		ts,
		sign,
		uuid,
		userId,
		token,
	businessId
	};
}
async function init(){
    let sign = 'd9yBkrdcrGgAUNzbhN2RtbVbROH/uk2CM/n6D2VBn41jueJ0aMFL052a8CFfLuzI'
    // if (uni.getStorageSync('token')) {
    //   const {data} = await imSign()
    //   sign = data.data
    // }

    SocketTask = uni.connectSocket({
        url: socketUrl,
		success() {
			console.log('连接成功');
		},
		fail() {
			console.log('连接失败');
		}
    })
	
    SocketTask.onOpen((res) => {
    	// 发送登录消息
      const { uuid,ts,userId,token, businessId} = makeConnectParams();
      send({
        // 消息类型：1-登录消息，2-心跳消息，3-聊天消息，4-ACK消息，5-服务端通知，6-指令消息
        type: 1, // 登录消息
        head: {
          id: uuid,
          needAck: true,
          timestamp: ts,
        },
        body: {
          platform: 17, // 平台 (17-android, 18-IOS, 52-mina)
          uid: userId,//用户id
          token: token, // 用户登录token
          sign: sign, //用户签名 MD5(uid + LOGIN_KEY) LOGIN_KER私发
          appversion: "5.5.0",
          businessId: businessId, // 业务号
        },
      })
      // 每间隔10秒发送心跳消息
      clearInterval(interval)
      interval = setInterval(()=>{
        heartbeat()
      },10000)
      // 拉取离线消息
      msgBusiness()
      // pullNewChat()
    })
    SocketTask.onError((err)=>{
    	// 错误重连
    	setTimeout(init,5000)
    })
    SocketTask.onClose((err)=>{
    })
    // 监听收到消息
    onMessage()
}

// 监听收到好友消息
function onNews(callback,id){
	queueCB[id] = callback
}
// 删除监听
function offNews(id){
	delete queueCB[id]
}


// 监听收到消息
function onMessage(callback){
	SocketTask.onMessage((res) => {
		const data = JSON.parse(res.data);
		const head = data.head;
		const body = data.body;
		// 如果需要ack则回应ack
		if (head.needAck) {
			ack(head.id);
		}
		// console.log(`收到了消息`,data);
		switch(data.type){
			case 6:
				if(body?.code == 1 && body.errorMsg == "success"){
					// 登录成功
					console.log('登录成功 || 发送消息成功')
				}
				break
			case 5:
				// 收到消息
				let content = JSON.parse(body.content)
				console.log(`收到了消息`,content)
				// 告知服务器收到了好友消息
				sendSuccess(content.recordUuid)
				// 走页面逻辑
				msgBusiness(content)
				// wx.vibrateLong()
				break
			case 8:
				console.log(`收到了列表消息`,body)
				if (body.code === 1001) {
				msgBusiness(1)
				initMessageCount()
				// wx.vibrateLong()
				}
						// wxapi.toast(body.errorMsg)
					break
				case 604:
					// 604: 拉取离线接收消息的指令消息)

					break
			}
	})
}


// 心跳消息
async function heartbeat(){
	// 发送心跳消息
	const { uuid,ts } = makeConnectParams();
  try {
    await send({
      type: 3,
      head: {
        id: uuid,
        needAck: true,
        timestamp: ts,
      },
    })
  } catch (error) {
    setTimeout(() => {
      init()
      initMessageCount()
    },5000)
  }

}

// 发送ack消息
function ack(id){
	const { ts } = makeConnectParams();
	send({
		type: 6,
		head: {
			id: id,
			needAck: false,
			timestamp: ts,
		},
	})
}

// 收到消息通知所有订阅者
function msgBusiness(content){
	Object.keys(queueCB).forEach((keys)=>queueCB[keys]?.(content))
}

// 发送业务消息
function sendChat(targetUserId,content,sessionUuid,newUuid){
    const { uuid,ts,userId } = makeConnectParams()
    let body = {
	    // 消息类型：1-登录消息，2-心跳消息，3-聊天消息，4-ACK消息，5-服务端通知，6-指令消息
	    type: 5,
	    head: {
	        id: newUuid || uuid,
	        needAck: true,
	        timestamp: ts,
	    },
	    body: {
	        uid: userId,
            receiverId: targetUserId,
	        sidAck: true,
	        platform: 17,
	        content: {
	        	seedSystemType: 1,
	        	seedBusinessType: 11,
	        	sessionUuid,
	            recordUuid: newUuid || uuid,
	           	recordType: 1,
	            uid: userId,
	            targetUserId: targetUserId,
	            contentType: 1,
	            content
	        }
	    },
	}
	send(body)
	console.log('发送的消息体',body);
}

// 发送消息
function send(params){
  return new Promise((resolve, reject) => {
    SocketTask.send({
        data: JSON.stringify(params),
        success() {
          console.log("发送消息成功");
          resolve(1)
        },
        fail(err) {
          console.log("发送消息失败",err);
          reject(0)
        },
      });
  })
}

function initMessageCount() {

}

function imSign() {
	// return {
	// 	"timestamp":"2025-05-29 11:30:04",
	// 	"code":0,
	// 	"msg":"message.success.default",
	// 	"taskName":null,
	// 	"credit":null,
	// 	"data":"d9yBkrdcrGgAUNzbhN2RtdWHn+I8P/bgSdV3FzChc6FYh2dyhK9RFNZoGgSlXCW2"
	// }
}

function guid() {
    return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random()*16|0, v = c == 'x' ? r : (r&0x3|0x8);
        return v.toString(16);
    });
}

// 获取总未读数
function unReadCount(){
    return new Promise((resl,rej)=>{

    })
}
// 消息标记已读
function readChat(sessionUuid,targetUserId){


}


// 收到消息告知服务器
function sendSuccess(recordUuid){

}

// 拉取离线消息
function pullNewChat(){

}

export default {
	start,
  init,
  sendChat,
  onNews,
  offNews,
  unReadCount,
  readChat,
  sendSuccess,
  msgBusiness,
  guid
}

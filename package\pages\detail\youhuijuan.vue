<template>
	<view>
		<!-- #ifdef MP-WEIXIN -->
		<view style="width: 100%;padding-bottom: 70rpx;" v-if="list.length>0">
			<view style="width: 100%;margin-left: 12rpx;" v-for='(item,index) in list' :key='index'>
				<view class="bgImgaes">
					<!-- #endif -->

					<!-- #ifndef MP-WEIXIN -->
					<view style="width: 100%;">
						<view style="width: 100%;margin-left: 12rpx;" v-for='(item,index) in list' :key='index'>
							<view class="bgImgaes">
								<!-- #endif -->
								<view style="width: 92%;border-radius: 10rpx;padding: 10rpx;margin-left: 40rpx;">
									<view style="display: flex;">
										<view style="margin-top: 25rpx;width: 70%;height: 165rpx;">
											<view
												style="font-size: 28rpx;color: #000000;overflow: hidden;margin-top: 20upx;white-space: nowrap;text-overflow: ellipsis;">
												{{item.couponName}}(满{{item.minMoney}}可用)
											</view>
											<view style="font-size: 24rpx;color: #666666;margin-top: 10rpx;">
												有效期至{{item.failureTime}}</view>
										</view>
										<view
											style="text-align: center;width: 30%;margin-top: 60rpx;text-align: center;">
											<view style="color:#FF6F1B;font-size: 38rpx;"><text
													style="font-size: 48rpx;">{{item.lessMoney}}</text>币</view>
										</view>
										
									</view>
									<view style="display: flex;margin-left: 10rpx;margin-top: 3rpx;">
										<view style="width: 80%;font-size: 22rpx;color: #000000;">报名抵金币专用。</view>
										<view
											style="background: #F15B6C;color: #FFFFFF;font-size: 22rpx;border-radius: 5rpx;padding:4rpx 6rpx 6rpx 6rpx;text-align: center;"
											@tap='shiyong(item)'>立即使用</view>
									</view>
								
								</view>
							</view>
						</view>
					</view>
					<!-- 悬浮上拉 -->
					<view class="scroll_top" @tap="topScrollTap" v-bind:class="[scrollTop ? 'active' : '','']"
						style="bottom: 56px;">
						<text class="iconfont icon-shangla"></text>
					</view>

					<!-- 加载更多提示 -->
					<view class="s-col is-col-24" v-if="list.length > 0">
						<load-more :status="loadingType" :contentText="contentText"></load-more>
					</view>
					<!-- 加载更多提示 -->
					<!-- 加载更多提示 -->
					<!-- #ifdef APP-PLUS -->
					<empty v-if="list.length === 0" content="您当前没有优惠券哦~" show="false">
					</empty>
					<!-- #endif -->
					<!-- #ifndef APP-PLUS -->
					<empty v-if="list.length === 0" content='您当前没有优惠券哦'></empty>
					<!-- #endif -->
				</view>
</template>

<script>
	import empty from '../../../components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				page: 0,
				size: 10,
				loadingType: 0,
				scrollTop: false,
				tabIndex: 1,
				bj1: '../../../static/pw/banner.png',
				list: [],
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多劵了'
				}
			};
		},
		onShow() {
			this.getYouHuiJuanList('', this.tabIndex);
		},
		methods: {
			getCouponIssueList() {
				let userId = this.$queue.getData('userId');
				this.$Request.getT('/app/selfCouponIssue/useList?goodsId=&userId=' + userId).then(res => {
					if (res.status === 0) {
						res.data.forEach(d => {
							this.CouponIssueList.push(d);
						});
					}
				});
			},
			shiyong(item) {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			getYouHuiJuanList(type, status) {
				this.loadingType = 1;
				uni.showLoading({
					title: '加载中...'
				});
				let userId = this.$queue.getData('userId');
				this.$Request.getT('/app/selfCouponUser/userList?page=' + this.page + '&size=' + this.size + '&userId=' +
					userId +
					'&goodsId=&type=' + status).then(res => {
					if (res.status === 0) {
						if (this.page === 0 || res.status) {
							this.list = [];
						}
						res.data.content.forEach(d => {
							// if (!d.coupon.goodsImages) {
							// 	d.coupon.goodsImages =
							// 		'https://h5.jiudiansai.com/img/20210119/1dc6276309584b6d9b91adf6d1fb4a34.png';
							// }
							this.list.push(d);
						});
						if (res.data.content.length === this.size) {
							this.loadingType = 0;
						} else {
							this.loadingType = 3;
						}
						uni.hideLoading();
					} else {
						this.loadingType = 2;
					}
					uni.hideLoading();
					if (type === 'Refresh') {
						uni.stopPullDownRefresh(); // 停止刷新
					}
				});
			},
			tabSlect(item) {
				this.tabIndex = item.id;
				this.page = 0;
				this.getYouHuiJuanList('', this.tabIndex);
			},
			topScrollTap: function() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				});
			}
		},
		onPageScroll: function(e) {
			this.scrollTop = e.scrollTop > 200;
		},
		onReachBottom: function() {
			this.page = this.page + 1;
			this.getYouHuiJuanList('', this.tabIndex);
		},
		onPullDownRefresh: function() {
			this.page = 0;
			this.getYouHuiJuanList('Refresh', this.tabIndex);
		}
	};
</script>

<style>
	@import '../../../static/css/index.css';

	page {
		background: #f5f5f5;
		padding-bottom: 120rpx;
	}

	.bgImgaes {
		/* #ifdef MP-WEIXIN */
		background-image: url('../../../static/image/coupon_bg.png');
		background-size: 100%;
		height: 240rpx;
		margin-top: 10rpx;
		border-radius: 10rpx;
		/* #endif */
		/* #ifndef MP-WEIXIN */
		background-image: url('../../../static/image/coupon_bg.png');
		background-size: 100% 100%;
		height: 280rpx;
		margin-top: 10rpx;
		border-radius: 10rpx;
		/* #endif */
	}

	.tui-tabs {
		flex: 1;
		flex-direction: column;
		overflow: hidden;
		background-color: #fafafa;
		/* #ifdef MP-ALIPAY || MP-BAIDU */
		height: 100vh;
		/* #endif */
	}

	.tui-scroll-h {
		width: 750rpx;
		height: 80rpx;
		background-color: #ffffff;
		flex-direction: row;
		/* #ifndef APP-PLUS */
		white-space: nowrap;
		/* #endif */
		/* #ifdef H5 */
		position: fixed;
		top: 44px;
		left: 0;
		z-index: 999;
		/* #endif */
	}

	.tui-line-h {
		/* #ifdef APP-PLUS */
		height: 1rpx;
		background-color: #cccccc;
		/* #endif */
		position: relative;
	}

	/* #ifndef APP-PLUS*/
	.tui-line-h::after {
		content: '';
		position: absolute;
		border-bottom: 1rpx solid #cccccc;
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
		bottom: 0;
		right: 0;
		left: 0;
	}

	/* #endif */
	.tui-tab-item {
		/* #ifndef APP-PLUS */
		display: flex;
		/* #endif */
		// flex-wrap: nowrap;
		// padding-left: 34rpx;
		// padding-right: 34rpx;
	}

	.tui-tab-item-title {
		color: #555;
		font-size: 30rpx;
		height: 80rpx;
		line-height: 80rpx;
		flex-wrap: nowrap;
		/* #ifndef APP-PLUS */
		white-space: nowrap;
		/* #endif */
	}

	.tui-tab-item-title-active {
		color: #5E81F9;
		font-size: 32upx;
		font-weight: bold;
		border-bottom: 1rpx solid #5E81F9;
		border-bottom-width: 5upx;
		text-align: center;
	}

	.limapboxqing2 {

		font-size: 28upx;
		color: #333333;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
	}
</style>
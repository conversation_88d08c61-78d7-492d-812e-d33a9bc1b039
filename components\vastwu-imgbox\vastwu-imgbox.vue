<template>
	<view>
		<view class="card-box" :src="item.userImg" v-for="(item,i) in lists" :key="i"
			:class="i==0?'max-width':i==1?'mod-width':'min-width'" :style="{'z-index':  zIndexs[`zIndex${i+1}`]+100,'left':lefts[`left${i+1}`],
			'top':`${-(zIndexs[`zIndex${i+1}`]*offsetY)-offsetY}px`}" @touchend="moveEnd($event,i+1)"
			:animation="animations[`animation${i+1}`]" @click.stop="slideclick(item)">
			<!-- <image class="main-img" :src="item.userImg"></image> -->
			<image class="main-img" mode="aspectFill" :src="item.userImg" @touchstart="moveStart($event,i+1)"
				@touchmove="move($event,i+1)"></image>
			<!-- 可自定义内容 -->
			<!-- <image class="lookmore" @click.stop="slideclick(item)"
				src="https://cdn.img.telefen.com/Permerchant/mingpian/lookmore.png"></image> -->
			<view class="bottom">
				<view class="flex align-center justify-between">
					<view class="flex align-center ">
						<view class="yhm">{{item.realName}}</view>
						<image src="../../static/images/my/rzicon.png" style="width: 40rpx;height: 40rpx;"></image>
					</view>
					<view class="wezhi">
					
						<text v-if="item.locationCity!='市辖区'"> {{item.locationCity}}</text>
						<text v-else> {{item.locationProvince}}</text>
						{{item.locationCounty}}
					</view>
				</view>
				<view class="flex align-center margin-top-sm">
					<view class="sexicon" v-if="item.sex==1">
						<u-icon name="man" color="#FFFFFF"></u-icon>
						{{item.age}}岁
					</view>
					<view class="sexicons" v-if="item.sex==2">
						<u-icon name="woman" color="#FFFFFF"></u-icon>
						{{item.age}}岁
					</view>
					<view class="labe">{{item.education}}</view>
					<view class="labe" v-if="item.marriageStatus==1">未婚</view>
					<view class="labe" v-if="item.marriageStatus==2">离异</view>
					<view class="labe">{{item.userHeight}}CM</view>
				</view>
				<view class="remk">
					<u-icon name="/static/images/index/xinxin.png" color="#FF749F" size="35"></u-icon>
					<text>{{item.idealAspect}}</text>
				</view>
				<view class="flex align-center justify-between padding-lr-xl margin-top-xl">
					<view class="gunbi" @click.stop="onclickImg()">
						<u-icon name="close" color="#FFFFFF" size="45"></u-icon>
					</view>
					<view class="xihuan" @click.stop="slideclick(item,2,i)">
						<u-icon name="/static/images/index/xihuan.png" color="#FFFFFF" size="50"></u-icon>
					</view>
				</view>
			</view>
			<view class="centre" v-if="lists.length==0">
				<image src="../../static/images/empty.png" mode=""></image>
				<view class="tips">暂无数据</view>
				<!-- <view class="btn">去开通</view> -->
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			list: {
				type: Array,
				required: true,
				default: []
			},
			listImg: {
				type: Array,
				required: true,
				default: []
			},
			offsetX: {
				type: Number,
				default: 10
			},
			offsetY: {
				type: Number,
				default: 10
			},
			auto: {
				type: Boolean,
				default: false
			},
			isQh: {
				type: Boolean,
				default: false
			},
			dur: {
				type: Number,
				default: 1000
			},
		

		},
		data() {
			return {
				lists: [],
				currentIndex: 1,
				animations: {},
				zIndexs: {},
				lefts: {}, //左边偏移量
				widths: {}, //图片宽度

				running: false,
				touching: false, //是否正按着
				//初始位置
				x: 0,
				y: 0,
				// 移动位置
				x1: 0,
				y1: 0
			}
		},
		created() {
			// console.log(this.lists,'yyyyyyyyyyyyyyyyyyyy')
			// console.log(this.zIndexs)

			for (var i = 0; i < this.lists.length; i++) {
				this.$set(this.animations, `animation${i+1}`, uni.createAnimation({
					duration: 300,
					timingFunction: 'ease'
				}))

				this.$set(this.widths, `zIndex${i+1}`, -(i + 1))
				this.$set(this.zIndexs, `zIndex${i+1}`, -(i + 1))
				this.$set(this.lefts, `left${i+1}`, `${-(this.zIndexs[`zIndex${i+1}`]*this.offsetX)-this.offsetX}px`)
				this.$set(this.widths, `zIndex${i+1}`, -(i + 1))
			}

			if (this.auto) {
				setInterval(() => {
					if (!this.running && !this.touching)
						this.action(this.currentIndex)
				}, this.dur)
			}


		},
		watch: {
			listImg() {

			},
			list() {

				this.lists = this.list
				for (var i = 0; i < this.lists.length; i++) {
					this.$set(this.animations, `animation${i+1}`, uni.createAnimation({
						duration: 300,
						timingFunction: 'ease'
					}))

					this.$set(this.widths, `zIndex${i+1}`, -(i + 1))
					this.$set(this.zIndexs, `zIndex${i+1}`, -(i + 1))
					this.$set(this.lefts, `left${i+1}`, `${-(this.zIndexs[`zIndex${i+1}`]*this.offsetX)-this.offsetX}px`)
					this.$set(this.widths, `zIndex${i+1}`, -(i + 1))
				}
			},
			isQh(newData, oldData) {
				console.log(newData, '1111111111')
				console.log(oldData, '1111111111')

				if (this.isQh) {
					if (!this.running && !this.touching)
						this.action(this.currentIndex)
				}
			}

		},
		methods: {
			onclickImg() {
				this.$emit('onclickImg', this.currentIndex);
				this.action(this.currentIndex)
			},
			move(e, i) {
				this.x1 = e.touches[0].pageX;
				this.y1 = e.touches[0].pageY;

				if (this.zIndexs[`zIndex${i}`] == -1 && (this.x - this.x1) > 0)
					this.lefts[`left${i}`] = `${-(this.zIndexs[`zIndex${i}`]*this.offsetX)-this.offsetX}` - (this.x - this
						.x1) + 'px';

			},
			moveEnd(e, i) {
				console.log(e, i)
				this.touching = false;
				if ((this.x - this.x1) > 100 && this.x != 0 && this.x1 != 0) {

					if (this.zIndexs[`zIndex${i}`] != -1 || this.running) return
					this.action(i)
				} else {
					this.lefts[`left${i}`] = `${-(this.zIndexs[`zIndex${i}`]*this.offsetX)-this.offsetX}px`;
					this.x = 0
					this.x1 = 0
					this.running = false
				}
			},
			moveStart(e, i) {
				this.touching = true
				this.x = e.touches[0].pageX;
				this.y = e.touches[0].pageY;

			},
			action(i) {
				let _this = this
				_this.running = true;
				// this.animations[`animation${i}`].translateX(-250).translateY(0).rotate(0).opacity(0.3).step();
				// this.animations[`animation${i}`].translateX(0).translateY(0).rotate(0).opacity(1).step();
				// this.animations[`animation${i}`] = this.animations[`animation${i}`].export();

				setTimeout(() => {
					_this.lists.splice(0, 1)
					// console.log('lllllll',_this.lists)
					// console.log(_this.listImg, _this.currentIndex - 1)
					let indexs = _this.listImg.findIndex(item => item.dataId == _this.lists[_this.list.length - 1]
						.dataId)

					if (indexs != -1 && _this.listImg.length > indexs + 1) {
						_this.lists.push(_this.listImg[indexs + 1])
					}
					// _this.isQh = true

					for (var j = 0; j < _this.lists.length; j++) {
						// if (j + 1 === i) {
						// 	_this.zIndexs[`zIndex${j+1}`] = (_this.list.length)
						// } else {
						// 	_this.zIndexs[`zIndex${j+1}`]++
						// }
						_this.lefts[`left${j+1}`] =
							`${-(_this.zIndexs[`zIndex${j+1}`]*_this.offsetX)-_this.offsetX}px`
					}
					// 	if (i == _this.list.length) {
					// 		_this_this.currentIndex = 1
					// 	} else {
					// 		_this.currentIndex++
					// 	}
					// 	// 重置
					for (var k = 0; k < _this.lists.length; k++) {
						_this.$set(_this.animations, `animation${k+1}`, uni.createAnimation({
							duration: 300,
							timingFunction: 'ease'
						}))
					}
					_this.running = false

					_this.x = 0
					_this.x1 = 0

					_this.$forceUpdate()
				}, 300)
				// console.log(this.lists,'bbbbbbbbbbbbbbbbbbbbbb')
				_this.$nextTick(() => {
					_this.$forceUpdate()
				})

			},
			slideclick(item, index, i) {
				let data = {
					item: item,
					index: index,
					currentIndex: this.currentIndex
				}
				this.$emit('slideclick', data);

			}
		}
	}
</script>

<style lang="less" scoped>
	.card-box {
		position: absolute;
		height: 1098rpx;
		width: 686rpx;
		top: 0;
		// box-shadow: 0 0 20rpx #555;
		transition: z-index 2s easc;
		border-radius: 20rpx;
	}

	.max-width {
		width: 686rpx;
	}

	.mod-width {
		width: 651rpx;
		margin-left: 15rpx;
	}

	.min-width {
		width: 619rpx;
		margin-left: 33rpx;
	}

	.main-img {
		width: 100%;
		height: 100%;
		border-radius: 32rpx;
	}

	/* .lookmore {
		position: absolute;
		width: 136rpx;
		height: 53rpx;
		bottom: 95rpx;
		left: 38%;
		z-index: 110;
	} */

	.bottom {
		width: 100%;
		padding: 0 30rpx;
		position: absolute;
		/* width: 136rpx;
		height: 53rpx; */
		bottom: 30rpx;
		/* left: 38%; */
		z-index: 110;
		color: #FFFFFF;
	}

	.gunbi {
		width: 100rpx;
		height: 100rpx;
		/* background: #CCCBD2; */
		background: linear-gradient(0deg, #C7C7CF 0%, #D3D2D7 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.xihuan {
		width: 100rpx;
		height: 100rpx;
		background: linear-gradient(0deg, #FF6F9C 0%, #FFA8C7 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.yhm {
		font-size: 42rpx;
		font-family: PingFang SC;
		font-weight: bold;
		color: #FFFFFF;
		margin-right: 20rpx;
	}

	.wezhi {
		font-size: 26rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
	}

	.sexicon {
		background: #38CAFF;
		border-radius: 10rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		padding: 4rpx 10rpx;
		margin-right: 10rpx;
	}

	.sexicons {
		background: #fbe2f4;
		border-radius: 10rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		padding: 4rpx 10rpx;
		margin-right: 10rpx;
	}

	.labe {
		background: #FFFFFD;
		border-radius: 10rpx;
		font-size: 24rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #999999;
		padding: 4rpx 10rpx;
		margin-right: 15rpx;
	}

	.remk {
		width: 620rpx;
		height: 58rpx;
		background: #FFE7EF;
		border-radius: 29rpx;
		color: #333333;
		display: flex;
		align-items: center;
		padding: 0 10rpx 0 30rpx;
		margin-top: 20rpx;

		text {
			width: 100%;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 1;
			overflow: hidden;
			margin-left: 10rpx;
		}
	}
</style>
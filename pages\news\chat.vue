<template>
	<view class="chat-container">
        <u-navbar :title="targetUser.nickname" :is-back="true" :background="background"></u-navbar>
		<!-- 安全提示 -->
		<view class="safety-tips" v-if="showSafetyTips">
			<view class="safety-title">安全提示</view>
			<view class="safety-content">
				聊天过程中，谨防网络诈骗，凡是涉及到金钱往来都是骗子！谨慎交换微信、手机号码、qq等私人联系方式！
			</view>
		</view>

		<!-- 聊天内容区域 -->
		<scroll-view
			:class="['chat-content', { 'no-safety-tips': !showSafetyTips }]"
			scroll-y="true"
			:scroll-into-view="scrollIntoView"
			:scroll-with-animation="true"
			:upper-threshold="50"
			:lower-threshold="50"
			@scrolltoupper="loadMoreHistory"
			@scrolltolower="loadFirstPageMessages"
			>

			<!-- 加载历史消息状态 -->
			<view class="loading-history" v-if="loadingHistory">
				正在加载历史消息...
			</view>

			<!-- 加载第一页消息状态 -->
			<view class="loading-first-page" v-if="loadingFirstPage">
				正在加载第一页消息...
			</view>

			<!-- 聊天消息列表 -->
			<view class="message-list">
				<view
					class="message-item"
					v-for="(message, index) in messageList"
					:key="message.id"
					:id="'msg-' + message.id">

					<!-- 时间显示 -->
					<view class="message-time" v-if="shouldShowTime(index)">
						{{ formatTime(message.timestamp) }}
					</view>

					<!-- 消息内容 -->
					<view class="message-content" :class="{ 'message-right': message.isSelf }">
						<image
							class="avatar"
							:src="message.isSelf ? userAvatar : targetUser.avatar"
							mode="aspectFill">
						</image>
						<view class="message-bubble" :class="{ 'bubble-self': message.isSelf }">
							<text class="message-text">{{ message.content }}</text>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<!-- 输入框区域 -->
		<view class="input-area">
			<textarea
				class="message-input"
				v-model="inputMessage"
				placeholder="输入新消息..."
				:focus="inputFocus"
				@focus="onInputFocus"
				@blur="onInputBlur"
				@input="onInputChange"
				@confirm="sendMessage"
				confirm-type="send"
				:auto-height="false"
				:maxlength="500"
				:style="{ height: inputHeight + 'rpx' }"
				ref="messageInput">
			</textarea>
			<view
				class="send-button"
				:class="{ 'send-active': inputMessage.trim() }"
				@click="sendMessage">
				发送
			</view>
		</view>
	</view>
</template>

<script>
import socket from '../../utils/socket'
export default {
	data() {
		return {
            background: {
                background: '#ffffff'
            },
			// 目标用户信息
			targetUser: {
				nickname: '胡萝卜',
				avatar: 'https://static.jiebao.zhenai.com/78675857897156487.png?imageMogr2/crop/120x120'
			},
            messageId: '',
			// 当前用户头像
			userAvatar: 'https://static.jiebao.zhenai.com/2024058272988-eda51a4f-6fa2-4134-b463-3d7f8be5ec00.png?imageView2/0/w/500/h/500',
			// 消息列表
			messageList: [],
			// 输入框内容
			inputMessage: '',
			// 输入框焦点状态
			inputFocus: false,
			// 输入框高度
			inputHeight: 80, // 初始高度80rpx
			// 滚动到指定位置
			scrollIntoView: '',
			// 是否显示安全提示
			showSafetyTips: true,
			// 安全提示定时器
			safetyTimer: null,
			// 消息ID计数器
			messageIdCounter: 1,
			// 分页相关
			loadingHistory: false, // 触顶加载历史消息状态
			loadingFirstPage: false, // 触底加载第一页状态
			hasMoreHistory: true, // 是否还有更多历史消息
			pageSize: 20, // 每页消息数量
			oldestMessageId: null, // 最早消息ID，用于加载历史消息
			newestMessageId: null, // 最新消息ID，用于加载新消息
			scrollTop: 0, // 滚动位置
			isAtBottom: true, // 是否在底部
			timer: null, // 轮询定时器
			currentPage: 1, // 当前页码
			totalPages: 0 // 总页数
		}
	},

	onLoad(options) {
		// 获取传递的参数
		if (options.userId) {
			this.targetUser.userId = options.userId;
		}
		if (options.username) {
			this.targetUser.nickname = options.username;
		}
        this.messageId = 'pages/news/chat' + options.userId
		// 初始化聊天数据
		// this.initChatData();

		// 设置安全提示6秒后消失
		this.setSafetyTipsTimer();
	},

    onShow() {
        this.initChatData(1)
        socket.offNews(this.messageId)
        socket.onNews(this.initChatData, this.messageId)
    },

	onUnload() {
		// 清除定时器
		if (this.safetyTimer) {
			clearTimeout(this.safetyTimer);
		}
	},

	methods: {

		// 初始化聊天数据
		async initChatData(type) {
			// 加载最新的消息
			await this.loadRecentMessages(type);

			// 开启轮询获取新消息（当在底部时）
			// this.startPolling();
		},

		// 加载最新消息
		async loadRecentMessages(type) {
			try {
				// 加载最新的30条消息，确保有足够内容可以滚动
				const messages = this.getMockMessages('recent', null, 30);
				this.messageList = messages;

				// 更新消息ID范围
				if (messages.length > 0) {
					this.oldestMessageId = messages[0].id;
					this.newestMessageId = messages[messages.length - 1].id;
				}

				console.log(`初始加载${messages.length}条消息，ID范围：${this.oldestMessageId} - ${this.newestMessageId}`);

				// 滚动到最新消息
                if (!type) {
                    // this.$nextTick(() => {
                    //     this.scrollToBottom();
                    // });
                    this.scrollToBottom()
                }

			} catch (error) {
				console.error('加载消息失败:', error);
				uni.showToast({
					title: '加载消息失败',
					icon: 'none'
				});
			}
		},

		// 获取模拟消息数据
		getMockMessages(type = 'recent', referenceId = null, count = 20, page = 1) {
			// 完整的模拟消息数据（扩展更多数据用于分页测试）
			const allMessages = [];

			// 生成更多模拟数据
			for (let i = 1; i <= 100; i++) {
				const isEven = i % 2 === 0;
				allMessages.push({
					id: i,
					content: isEven ? `这是第${i}条消息，来自对方` : `这是第${i}条回复消息`,
					isSelf: isEven,
					timestamp: Date.now() - 1000 * 60 * (100 - i) // 按时间顺序排列
				});
			}

			// 计算总页数
			this.totalPages = Math.ceil(allMessages.length / count);

			if (type === 'recent') {
				// 返回最新的count条消息（初始加载）
				return allMessages.slice(-count);
			} else if (type === 'history') {
				// 返回referenceId之前的count条消息
				const index = allMessages.findIndex(msg => msg.id === referenceId);
				if (index > 0) {
					const start = Math.max(0, index - count);
					return allMessages.slice(start, index);
				}
				return [];
			} else if (type === 'firstPage') {
				// 返回第一页的消息
				const start = (page - 1) * count;
				const end = start + count;
				return allMessages.slice(start, end);
			} else if (type === 'new') {
				// 返回referenceId之后的count条消息
				const index = allMessages.findIndex(msg => msg.id === referenceId);
				if (index >= 0 && index < allMessages.length - 1) {
					const end = Math.min(allMessages.length, index + 1 + count);
					return allMessages.slice(index + 1, end);
				}
				return [];
			}

			return [];
		},

		// 设置安全提示定时器
		setSafetyTipsTimer() {
			this.safetyTimer = setTimeout(() => {
				this.showSafetyTips = false;
			}, 1000); // 6秒后隐藏
		},

		// 判断是否显示时间
		shouldShowTime(index) {
			if (index === 0) return true;

			const currentMsg = this.messageList[index];
			const prevMsg = this.messageList[index - 1];

			// 如果时间间隔超过5分钟，显示时间
			return currentMsg.timestamp - prevMsg.timestamp > 5 * 60 * 1000;
		},

		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp);
			const now = new Date();
			const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

			if (diffDays === 0) {
				// 今天，显示时间
				return date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit',
					hour12: false
				});
			} else if (diffDays === 1) {
				// 昨天
				return '昨天 ' + date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit',
					hour12: false
				});
			} else {
				// 更早，显示月日
				return date.toLocaleDateString('zh-CN', {
					month: '2-digit',
					day: '2-digit'
				}) + ' ' + date.toLocaleTimeString('zh-CN', {
					hour: '2-digit',
					minute: '2-digit',
					hour12: false
				});
			}
		},

		// 输入框获得焦点
		onInputFocus() {
			this.inputFocus = true;
			// 延迟滚动到底部，等待键盘弹出
			setTimeout(() => {
				this.scrollToBottom();
			}, 300);
		},

		// 输入框失去焦点
		onInputBlur() {
			this.inputFocus = false;
		},

		// 输入框内容变化
		onInputChange(e) {
			this.inputMessage = e.detail.value;
			this.adjustTextareaHeight();
		},

		// 动态调整textarea高度
		adjustTextareaHeight() {
			const text = this.inputMessage || '';
			const lines = this.calculateLines(text);

			const lineHeight = 39.2; // 28rpx * 1.4 ≈ 39.2rpx
			const padding = 40; // 上下padding总计40rpx (20rpx * 2)
			const minHeight = 80; // 最小高度80rpx
			const maxHeight = 160; // 最大高度160rpx

			let newHeight;
			if (lines <= 2) {
				// 1-2行，使用初始高度
				newHeight = minHeight;
			} else {
				// 超过2行，开始增加高度
				const contentHeight = lines * lineHeight;
				newHeight = Math.min(contentHeight + padding, maxHeight);
			}

			// 更新高度
			this.inputHeight = newHeight;
		},

		// 计算文本行数
		calculateLines(text) {
			if (!text) return 1;

			// 计算换行符导致的行数
			const lineBreaks = (text.match(/\n/g) || []).length;

			// 估算因为文本长度导致的自动换行
			// 根据字体大小和容器宽度估算每行字符数
			const avgCharsPerLine = 18; // 估算每行平均字符数（可根据实际情况调整）
			const textWithoutBreaks = text.replace(/\n/g, '');
			const estimatedLines = Math.ceil(textWithoutBreaks.length / avgCharsPerLine);

			// 返回实际行数：换行符行数 + 估算的自动换行行数
			return lineBreaks + estimatedLines;
		},

		// 发送消息
		sendMessage() {
			const message = this.inputMessage.trim();
			if (!message) return;

			// 生成新的消息ID（实际应用中应该由服务器生成）
			const newId = Math.max(...this.messageList.map(msg => msg.id)) + 1;

			// 添加新消息到列表
			const newMessage = {
				id: newId,
				content: message,
				isSelf: true,
				timestamp: Date.now()
			};

			this.messageList.push(newMessage);
			this.inputMessage = '';

			// 重置输入框高度
			this.inputHeight = 80;

			// 更新最新消息ID
			this.newestMessageId = newId;

			// 滚动到最新消息
			this.$nextTick(() => {
				this.scrollToBottom();
			});

			// 模拟对方回复（可选）
			this.simulateReply();
		},

		// 滚动到底部
		scrollToBottom() {
			if (this.messageList.length > 0) {
				const lastMessage = this.messageList[this.messageList.length - 1];
				this.scrollIntoView = 'msg-' + lastMessage.id;
                this.scrollTop = 10000000
			}
		},

		// 触顶加载历史消息
		async loadMoreHistory() {
			console.log('🔝 触顶事件触发！');
			console.log('当前状态：', {
				loadingHistory: this.loadingHistory,
				hasMoreHistory: this.hasMoreHistory,
				oldestMessageId: this.oldestMessageId,
				messageListLength: this.messageList.length
			});

			// if (this.loadingHistory || !this.hasMoreHistory) {
			// 	console.log('❌ 触顶加载被阻止：', {
			// 		loadingHistory: this.loadingHistory,
			// 		hasMoreHistory: this.hasMoreHistory
			// 	});
			// 	return;
			// }

			this.loadingHistory = true;
			console.log('✅ 开始触顶加载历史消息');

			// 显示加载提示
			uni.showToast({
				title: '加载历史消息...',
				icon: 'loading',
				duration: 1000
			});

			try {
				// 获取历史消息
				const historyMessages = this.getMockMessages('history', this.oldestMessageId, this.pageSize);
				console.log('📥 获取到历史消息：', historyMessages.length, '条');

				if (historyMessages.length > 0) {
					// 添加历史消息到列表开头
					this.messageList = [...historyMessages, ...this.messageList];
					this.oldestMessageId = historyMessages[0].id;

					console.log(`✅ 加载了${historyMessages.length}条历史消息，当前总消息数：${this.messageList.length}`);
					console.log('🆔 新的最早消息ID：', this.oldestMessageId);

					uni.showToast({
						title: `加载了${historyMessages.length}条历史消息`,
						icon: 'success',
						duration: 1000
					});
				} else {
					this.hasMoreHistory = false;
					console.log('❌ 没有更多历史消息了');
					uni.showToast({
						title: '没有更多历史消息了',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('❌ 加载历史消息失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loadingHistory = false;
				console.log('🏁 触顶加载完成');
			}
		},

		// 触底加载第一页消息
		async loadFirstPageMessages() {
			console.log('🔻 触底事件触发！');
			console.log('当前状态：', {
				loadingFirstPage: this.loadingFirstPage,
				currentPage: this.currentPage,
				messageListLength: this.messageList.length
			});

			if (this.loadingFirstPage) {
				console.log('❌ 触底加载被阻止：正在加载中');
				return;
			}

			this.loadingFirstPage = true;
			console.log('✅ 开始触底加载第一页消息');

			// 显示加载提示
			uni.showToast({
				title: '加载第一页消息...',
				icon: 'loading',
				duration: 1000
			});

			try {
				// 获取第一页消息
				const firstPageMessages = this.getMockMessages('firstPage', null, this.pageSize, 1);
				console.log('📥 获取到第一页消息：', firstPageMessages.length, '条');

				if (firstPageMessages.length > 0) {
					// 替换当前消息列表为第一页消息
					this.messageList = firstPageMessages;
					this.oldestMessageId = firstPageMessages[0].id;
					this.newestMessageId = firstPageMessages[firstPageMessages.length - 1].id;
					this.currentPage = 1;
					this.hasMoreHistory = true; // 重置历史消息状态

					console.log(`✅ 加载第一页消息，共${firstPageMessages.length}条`);
					console.log('🆔 消息ID范围：', this.oldestMessageId, '-', this.newestMessageId);

					uni.showToast({
						title: `加载第一页消息，共${firstPageMessages.length}条`,
						icon: 'success',
						duration: 1000
					});

					// 滚动到顶部
					this.$nextTick(() => {
						// 可以选择滚动到顶部或保持当前位置
					});
				}
			} catch (error) {
				console.error('❌ 加载第一页消息失败:', error);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			} finally {
				this.loadingFirstPage = false;
				console.log('🏁 触底加载完成');
			}
		},

		// 滚动监听
		onScroll(e) {
			const { scrollTop, scrollHeight, clientHeight } = e.detail;
			this.scrollTop = scrollTop;

			// 判断是否在底部（距离底部50px以内）
			this.isAtBottom = scrollHeight - scrollTop - clientHeight < 50;

			// 添加滚动调试信息（每10次滚动打印一次，避免日志过多）
			if (Math.random() < 0.1) {
				console.log('📜 滚动信息：', {
					scrollTop,
					scrollHeight,
					clientHeight,
					isAtBottom: this.isAtBottom,
					distanceFromBottom: scrollHeight - scrollTop - clientHeight
				});
			}

			// 如果不在底部，停止轮询
			if (!this.isAtBottom && this.timer) {
				clearInterval(this.timer);
				this.timer = null;
			} else if (this.isAtBottom && !this.timer) {
				// 如果在底部，开启轮询
				this.startPolling();
			}
		},

		// 开启轮询获取新消息
		startPolling() {
			if (this.timer) return;

			this.timer = setInterval(() => {
				if (this.isAtBottom) {
					this.checkNewMessages();
				}
			}, 3000); // 每3秒检查一次新消息
		},

		// 检查新消息
		async checkNewMessages() {
			try {
				const newMessages = this.getMockMessages('new', this.newestMessageId, 5);

				if (newMessages.length > 0) {
					// 添加新消息到列表
					this.messageList.push(...newMessages);
					this.newestMessageId = newMessages[newMessages.length - 1].id;

					// 如果在底部，自动滚动到最新消息
					if (this.isAtBottom) {
						this.$nextTick(() => {
							this.scrollToBottom();
						});
					}
				}
			} catch (error) {
				console.error('检查新消息失败:', error);
			}
		},

		// 模拟对方回复
		simulateReply() {
			setTimeout(() => {
				const replies = ['好的', '收到', '嗯嗯', '哈哈', '👍', '明白了', '没问题', '好呀'];
				const randomReply = replies[Math.floor(Math.random() * replies.length)];

				// 生成新的消息ID
				const newId = Math.max(...this.messageList.map(msg => msg.id)) + 1;

				const replyMessage = {
					id: newId,
					content: randomReply,
					isSelf: false,
					timestamp: Date.now()
				};

				this.messageList.push(replyMessage);

				// 更新最新消息ID
				this.newestMessageId = newId;

				// 如果在底部，自动滚动到最新消息
				if (this.isAtBottom) {
					this.$nextTick(() => {
						this.scrollToBottom();
					});
				}
			}, 1000 + Math.random() * 2000); // 1-3秒后回复
		}
	}
}
</script>

<style lang="scss" scoped>
.chat-container {
	height: 100vh;
	background-color: #f5f6f9;
	display: flex;
	flex-direction: column;
}

.chat-content {
	flex: 1;
	/* height: calc(100vh - 208rpx);*/ /* 减去导航栏和输入框的高度 */
	margin-bottom: 120rpx;
    margin-top: calc(constant(safe-area-inset-top));
    margin-top: calc(env(safe-area-inset-top));
	padding: 0 32rpx;
	overflow: hidden;
    position: absolute;
    top: 110rpx; /* 导航栏 + 安全提示的高度 */
    left: 0;
    right: 0;
    bottom: 0;
    transition: top 0.3s ease; /* 平滑过渡 */
}

/* 当安全提示消失后，调整聊天内容区域 */
.chat-content.no-safety-tips {
	top: 110rpx; /* 只保留导航栏高度 */
}

.loading-history,
.loading-first-page {
	text-align: center;
	padding: 20rpx;
	color: #999999;
	font-size: 24rpx;
}

.loading-history::before,
.loading-first-page::before {
	content: '';
	display: inline-block;
	width: 32rpx;
	height: 32rpx;
	border: 4rpx solid #e5e5e5;
	border-top-color: #007aff;
	border-radius: 50%;
	animation: loading-spin 1s linear infinite;
	margin-right: 16rpx;
	vertical-align: middle;
}

.loading-first-page {
	position: fixed;
	bottom: 140rpx;
	left: 50%;
	transform: translateX(-50%);
	background-color: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	border-radius: 8rpx;
	padding: 16rpx 32rpx;
	z-index: 1000;
}

@keyframes loading-spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

.safety-tips {
	position: fixed;
	top: calc(var(--status-bar-height, 44px) + 88rpx); /* 在u-navbar下方 */
	left: 0;
	right: 0;
	z-index: 998; /* 在导航栏下方，但在内容上方 */
	text-align: center;
	padding: 20rpx 32rpx;
	background-color: rgba(255, 249, 196, 0.95); /* 淡黄色背景，半透明 */
	border-bottom: 1rpx solid #f0c674;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	animation: fadeOut 0.5s ease-in-out 5.5s forwards;
}

.safety-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
}

.safety-content {
	font-size: 24rpx;
	color: #666666;
	line-height: 1.5;
}

@keyframes fadeOut {
	from {
		opacity: 1;
		transform: translateY(0);
	}
	to {
		opacity: 0;
		transform: translateY(-20rpx);
	}
}

.message-list {
	padding-bottom: 40rpx;
	min-height: calc(100vh - 300rpx); /* 确保有足够高度可以滚动 */
}

.message-item {
	margin-bottom: 32rpx;
}

.message-time {
	text-align: center;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 24rpx;
}

.message-content {
	display: flex;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.message-right {
	flex-direction: row-reverse;
}

.avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background-color: #f0f0f0;
	flex-shrink: 0;
}

.message-bubble {
	max-width: 500rpx;
	background-color: #ffffff;
	border-radius: 16rpx;
	padding: 20rpx 24rpx;
	margin: 0 16rpx;
	position: relative;
	word-break: break-word;
}

.bubble-self {
	background-color: #FDE8EC;
}

.bubble-self .message-text {
	color: #323233;
}

.message-text {
	font-size: 28rpx;
	color: #333333;
	line-height: 1.4;
}

.message-bubble::before {
	content: '';
	position: absolute;
	top: 24rpx;
	width: 16rpx;
	height: 16rpx;
	background-color: inherit;
	transform: rotate(45deg);
}

.message-content:not(.message-right) .message-bubble::before {
	left: -8rpx;
}

.message-right .message-bubble::before {
	right: -8rpx;
}

.debug-buttons {
	position: fixed;
	top: 50%;
	right: 20rpx;
	transform: translateY(-50%);
	z-index: 1000;
	display: flex;
	flex-direction: column;
	gap: 20rpx;
}

.debug-btn {
	background-color: rgba(0, 122, 255, 0.8);
	color: #ffffff;
	padding: 16rpx 24rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	text-align: center;
	min-width: 120rpx;
	cursor: pointer;
}

.debug-btn:active {
	background-color: rgba(0, 122, 255, 1);
}

.input-area {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 40rpx 32rpx;
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	border-top: 1rpx solid #e5e5e5;
	display: flex;
	align-items: flex-end;
	z-index: 999;
	min-height: 120rpx;
}

.message-input {
	flex: 1;
	min-height: 80rpx;
	max-height: 160rpx;
	background: #F7F8FA;
	border-radius: 16rpx;
	padding: 20rpx 24rpx;
	font-size: 28rpx;
	color: #333333;
	border: none;
	outline: none;
	line-height: 1.4; /* 行高约39.2rpx (28rpx * 1.4) */
	resize: none;
	box-sizing: border-box;
	word-wrap: break-word;
	overflow-y: auto; /* 超出最大高度时显示滚动条 */
	transition: height 0.2s ease; /* 高度变化动画 */
}

.send-button {
	width: 120rpx;
	height: 72rpx;
	background-color: #e5e5e5;
	border-radius: 36rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 16rpx;
	font-size: 28rpx;
	color: #999999;
	transition: all 0.3s ease;
}

.send-active {
	background-color: #EB1B47;
	color: #ffffff;
}

/* 适配不同屏幕 */
@media screen and (max-width: 750rpx) {
	.message-bubble {
		max-width: 400rpx;
	}

	.nav-title {
		font-size: 32rpx;
	}

	.message-text {
		font-size: 26rpx;
	}
}

/* 键盘弹出时的适配 */
.chat-container.keyboard-show {
	padding-bottom: 0;
}

.chat-container.keyboard-show .chat-content {
	margin-bottom: 200rpx;
}
</style>
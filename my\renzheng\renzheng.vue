<template>
	<view class="padding-lr">
		<view class="text-bold" style="font-size: 44upx;margin: 42upx 5upx 0upx 0upx;">请选择填写的认证类型</view>
		<view style="font-size: 24upx;color: #999999;">请选择您要填写编辑的认证类型</view>
		
		<view style="margin-top: 120upx;">
			<view class="flex justify-between align-center box padding" @click="bindGeren()">
				<view class="text-lg text-bold">实名认证</view>
				<view>
					<u-icon name="arrow-right" color="#808080" size="28"></u-icon>
				</view>
			</view>
			<view class="flex justify-between align-center box margin-top-sm padding" @click="bindQe()">
				<view class="text-lg text-bold">学历认证</view>
				<view>
					<u-icon name="arrow-right" color="#808080" size="28"></u-icon>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {}
		},
		methods:{
			 bindGeren(){
				 uni.navigateTo({
				 	url:'/my/renzheng/index'
				 })
			 },
			bindQe(){
				uni.navigateTo({
					url:'/my/renzheng/xueli'
				})
			}
		}
	}
</script>

<style>
	page {
		background-color: #FFFFFF;
	}
	.box{
		width: 670upx;
		height: 120upx;
		background: #f2f2f2;
		border-radius: 16upx;
	}
</style>

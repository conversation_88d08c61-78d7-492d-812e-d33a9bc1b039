<template>
	<view class="container">
		<!-- 顶部统计区域 -->
		<view class="top-section">
			<view class="stats-cards">
				<view class="stat-card">
					<image class="card-bg" src="https://photo.zastatic.com/images/common-cms/it/20250527/1748342643941_745864_t.png" mode="aspectFill"></image>
					<view class="stat-content">
						<view class="stat-number">{{ totalRevenue }}元</view>
						<view class="stat-label">订单收益</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 订单列表 -->
		<view class="order-list">
			<view class="order-item" v-for="(order, index) in orderList" :key="index">
				<view class="order-header">
					<view class="order-title">{{ order.title }}</view>
					<view class="order-amount">+{{ order.amount }} 元</view>
				</view>
				<view class="order-details">
					<view class="detail-item">
						<text class="detail-label">订单编号：</text>
						<text class="detail-value">{{ order.orderNo }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">订单时间：</text>
						<text class="detail-value">{{ order.orderTime }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">会员名称：</text>
						<text class="detail-value">{{ order.memberName }}</text>
					</view>
					<view class="detail-item">
						<text class="detail-label">红娘：</text>
						<text class="detail-value">{{ order.matchmaker }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			storeId: '',
			storeName: '',
			orderCount: 12,
			totalRevenue: '4856',
			orderList: [
				{
					title: '购买月度会员',
					amount: '1444',
					orderNo: 'SY202505120001',
					orderTime: '2025-05-12 10:25:33',
					memberName: '林志豪',
					matchmaker: '李雅婷'
				},
				{
					title: '购买月度会员',
					amount: '644',
					orderNo: 'SY202505100002',
					orderTime: '2025-05-10 14:18:27',
					memberName: '张雅琳',
					matchmaker: '张美琳'
				},
				{
					title: '购买月度会员',
					amount: '344',
					orderNo: 'SY202505080003',
					orderTime: '2025-05-08 16:42:15',
					memberName: '王建华',
					matchmaker: '陈思雨'
				},
				{
					title: '购买月度会员',
					amount: '194',
					orderNo: 'SY202505060004',
					orderTime: '2025-05-06 09:33:48',
					memberName: '刘美娟',
					matchmaker: '王欣怡'
				},
				{
					title: '购买月度会员',
					amount: '84',
					orderNo: 'SY202505040005',
					orderTime: '2025-05-04 11:56:12',
					memberName: '陈志明',
					matchmaker: '刘晓敏'
				},
				{
					title: '购买月度会员',
					amount: '344',
					orderNo: 'SY202505020006',
					orderTime: '2025-05-02 15:27:39',
					memberName: '赵丽华',
					matchmaker: '赵婉儿'
				},
				{
					title: '购买月度会员',
					amount: '644',
					orderNo: 'SY202504300007',
					orderTime: '2025-04-30 13:14:56',
					memberName: '孙志强',
					matchmaker: '孙丽娜'
				},
				{
					title: '购买月度会员',
					amount: '84',
					orderNo: 'SY202504280008',
					orderTime: '2025-04-28 17:38:21',
					memberName: '周雅芳',
					matchmaker: '周雨萱'
				},
				{
					title: '购买月度会员',
					amount: '194',
					orderNo: 'SY202504260009',
					orderTime: '2025-04-26 08:45:33',
					memberName: '吴建国',
					matchmaker: '吴佳慧'
				},
				{
					title: '购买月度会员',
					amount: '84',
					orderNo: 'SY202504240010',
					orderTime: '2025-04-24 12:22:47',
					memberName: '郑美玲',
					matchmaker: '郑雅芳'
				},
				{
					title: '购买月度会员',
					amount: '344',
					orderNo: 'SY202504220011',
					orderTime: '2025-04-22 14:51:18',
					memberName: '马志华',
					matchmaker: '李雅婷'
				},
				{
					title: '购买月度会员',
					amount: '194',
					orderNo: 'SY202504200012',
					orderTime: '2025-04-20 16:33:52',
					memberName: '黄雅丽',
					matchmaker: '张美琳'
				}
			]
		}
	},
	onLoad(options) {
		// 获取传递的门店信息
		if (options.storeId) {
			this.storeId = options.storeId
			this.storeName = options.storeName
		}
		this.loadOrderData()
	},
	methods: {
		loadOrderData() {
			// 这里可以调用API获取订单数据
			// this.$Request.get("/app/store/orders", {
			//     storeId: this.storeId
			// }).then(res => {
			//     if (res.code == 0) {
			//         this.orderList = res.data.list
			//         this.orderCount = res.data.orderCount
			//         this.totalRevenue = res.data.totalRevenue
			//     }
			// });
		}
	}
}
</script>

<style scoped>
page {
	background: #f5f5f5;
}

.container {
	min-height: 100vh;
}

.top-section {
	padding: 20rpx;
}

.stats-cards {
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	position: relative;
	height: 160rpx;
	border-radius: 12rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.card-bg {
	position: absolute;
	top: 56rpx;
	right: 50rpx;
	width: 100rpx;
	height: 100rpx;
	z-index: 10;
}

.stat-content {
	position: relative;
	z-index: 2;
	padding: 40rpx;
	height: 100%;
	display: flex;
	flex-direction: column;
	justify-content: center;
    background: #FFFFFF;
}

.stat-number {
	font-size: 48rpx;
	font-weight: bold;
	margin-bottom: 10rpx;
	color: #333333;
	margin-left: 50rpx;
}

.stat-label {
	font-size: 28rpx;
	color: #666666;
	margin-left: 50rpx;
}

.order-list {
	padding: 0 20rpx;
}

.order-item {
	background: #FFFFFF;
	border-radius: 12rpx;
	margin-bottom: 20rpx;
	padding: 40rpx;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.order-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 20rpx;
}

.order-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
}

.order-amount {
	font-size: 32rpx;
	font-weight: bold;
	color: #FF6B9D;
}

.order-details {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.detail-item {
	display: flex;
	font-size: 28rpx;
	line-height: 1.5;
}

.detail-label {
	color: #666666;
	min-width: 160rpx;
}

.detail-value {
	color: #333333;
	flex: 1;
}
</style>

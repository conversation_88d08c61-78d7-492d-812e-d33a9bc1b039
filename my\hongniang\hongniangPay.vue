<template>
  <view class="hongniang-pay-container">
    <view class="top">
      <img
        :src="
          isPayHongNianghttps
            ? 'https://photo.zastatic.com/images/common-cms/it/20250527/1748347998107_534602.png'
            : 'https://photo.zastatic.com/images/common-cms/it/20250528/1748362466924_17877.png'
        "
        class="top-img"
      />
    </view>
    <view class="bottom" v-if="!isPayHongNiang">
      <view class="payment-item" @click="togglePayment">
        <view class="payment-left">
          <image
            src="https://photo.zastatic.com/images/common-cms/it/20250527/1748341227691_317879_t.png"
            class="payment-icon"
            mode="aspectFit"
          ></image>
          <text class="payment-text">支付宝</text>
        </view>
        <view class="payment-right">
          <view class="radio" :class="{ active: isAlipaySelected }">
            <view class="radio-inner" v-if="isAlipaySelected"></view>
          </view>
        </view>
      </view>
    </view>
    <view class="bottom-btn" v-if="!isPayHongNiang">
      <button class="open-btn" @click="openShop">立即开通</button>
    </view>
  </view>
</template>

<script>
export default {
  name: "hongniangPay",
  data() {
    return {
      isAlipaySelected: true,
      isPayHongNiang: false,
    };
  },
  onLoad() {
    this.isAlipaySelected = uni.getStorageSync("isPayHongNiang");
    this.isPayHongNiang = uni.getStorageSync("isPayHongNiang") || false;
  },
  methods: {
    openShop() {
      // 这里处理开通店铺的逻辑
      uni.showModal({
        title: "确认开通",
        content: `确认支付3998元成为婚介吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用支付接口
            this.handlePayment();
          }
        },
      });
    },
    handlePayment() {
      // 支付逻辑
      uni.showLoading({
        title: "支付中...",
      });

      // 模拟支付请求
      setTimeout(() => {
        uni.hideLoading();
        uni.showToast({
          title: "支付成功",
          icon: "success",
        });
        uni.setStorageSync("isPayHongNiang", true);
        // 支付成功后的处理
        setTimeout(() => {
          uni.reLaunch({
            url: "/my/pay/paySuccess",
          });
        }, 1500);
      }, 2000);
    },
    togglePayment() {
      this.isAlipaySelected = !this.isAlipaySelected;
    },
  },
};
</script>

<style lang="scss" scoped>
.hongniang-pay-container {
  min-height: 100vh;
  background: #f5f5f5;
  position: relative;
}
.top {
  width: 100%;
  height: 926rpx;
}
.top-img {
  width: 100%;
  height: 100%;
}
.bottom {
  background: #fff;
}
.payment-item {
  width: 100%;

  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;

  .payment-left {
    display: flex;
    align-items: center;

    .payment-icon {
      width: 60rpx;
      height: 60rpx;
      margin-right: 20rpx;
    }

    .payment-text {
      font-size: 32rpx;
      color: #666;
    }
  }

  .payment-right {
    .radio {
      width: 40rpx;
      height: 40rpx;
      border: 4rpx solid #ddd;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &.active {
        border-color: #fead89;

        .radio-inner {
          width: 20rpx;
          height: 20rpx;
          background: #fead89;
          border-radius: 50%;
        }
      }
    }
  }
}
.bottom-btn {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;

  .open-btn {
    width: 100%;
    height: 88rpx;
    background: #fead89;
    color: white;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 400;
    display: flex;
    align-items: center;
    justify-content: center;

    &::after {
      border: none;
    }
  }
}
</style>

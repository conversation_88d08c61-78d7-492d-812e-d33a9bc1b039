<!--
 * @name: 我接管的用户
 * @Author: 刘大可
 * @Date: 2024-03-05 16:27:19
 * @LastEditors: 刘大可
 * @LastEditTime: 2024-03-07 09:52:21
-->
<template>
    <div class="myUsers">
        <list :list="list" @click="clickItem"></list>
        <empty v-if="list.length == 0"></empty>
        <view class="s-col is-col-24" v-if="list.length > 0">
            <load-more :status="loadingType" :contentText="contentText"></load-more>
        </view>
    </div>
</template>

<script>
import Vue from 'vue';
import list from "@/components/viplist2.vue";
import empty from '@/components/empty'

export default Vue.extend({
    components: {
        list, empty
    },
    data() {
        return {
            page: 1,
            list:[],
            loadingType: 0,
            contentText: {
                contentdown: '上拉显示更多',
                contentrefresh: '正在加载...',
                contentnomore: '没有更多数据了'
            },
        }
    },
    computed: {},
    methods: {
        getUserList() {
            uni.showLoading({
                title: '加载中...',
                mask: true
            });
            this.$Request.getT('/app/userRoleMatchmaker/getUserRoleMatchmakerList', {
                page: this.page,
                limit: 10,
            }).then(res => {
                uni.stopPullDownRefresh();
                uni.hideLoading();
                if (res.code == 0) {
                    this.list = this.page == 1 ? res.data.records : [...this.list, ...res.data.records]	
                    if (res.data.pages > res.data.current) {
                        this.loadingType = 0
                    } else {
                        this.loadingType = 2
                    }	
                }
            })
        },
        clickItem(e){
            console.log(e);
            if (e.type==0) {
                // 取消接管
                uni.showModal({
                    title:'提醒',
                    content:'取消接管用户将回归工作台',
                    success:(res)=>{
                        console.log(res);
                        if (res.confirm) {
                            this.$Request.post('/app/userRoleMatchmaker/deleteUserRoleMatchmakerById',{
                                roleId: e.item.roleId,
                                // roleId:123
                            }).then(res=>{
                                if (res.code == 0) {
                                    uni.showToast({
                                        title:'取消接管成功',
                                        icon:'none'
                                    })
                                    this.page=1
                                    this.getUserList()
                                }else{
                                    uni.showToast({
                                        title:res.msg,
                                        icon:'none'
                                    })
                                }
                            })
                        }
                    },
                })
            }
            if (e.type == 2) {
                // 牵线成功
                uni.showModal({
                    title: '提醒',
                    content: '确认用户牵线成功？',
                    success: (res) => {
                        console.log(res);
                        if (res.confirm) {
                            this.$Request.post('/app/userRoleMatchmaker/matchUpdateStatus', {
                                roleId: e.item.roleId,
                                // roleId:123
                            }).then(res => {
                                if (res.code == 0) {
                                    uni.showToast({
                                        title: '用户牵线成功成功',
                                        icon: 'none'
                                    })
                                    this.page = 1
                                    this.getUserList()
                                } else {
                                    uni.showToast({
                                        title: res.msg,
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                })
            }
        }
    },
    watch: {},

    // 页面周期函数--监听页面加载
    onLoad() {
        this.page=1
        this.getUserList()
    },
    // 页面周期函数--监听页面初次渲染完成
    onReady() { },
    // 页面周期函数--监听页面显示(not-nvue)
    onShow() { },
    // 页面周期函数--监听页面隐藏
    onHide() { },
    // 页面周期函数--监听页面卸载
    onUnload() { },
    // 页面处理函数--监听用户下拉动作
    onPullDownRefresh() { 
        this.page=1
        this.getUserList()
    },
    // 页面处理函数--监听用户上拉触底
    onReachBottom() {
        if (this.loadingType == 0) {
            this.page+=1
            this.getUserList()
        }
    },
    // 页面处理函数--监听页面滚动(not-nvue)
    // onPageScroll(event) {},
    // 页面处理函数--用户点击右上角分享
    // onShareAppMessage(options) {},
}) 
</script>

<style scoped></style>
<template>
	<view>
		<view class="box">
			<view @click.stop="clickSlider(item.userId)" @touchstart="moveStart($event,index)"
				@touchmove="moverIng($event,index)" @touchend="moveEnd($event,index)"
				:class="index==0?'max-width':index==1?'mod-width':'min-width'" class="box-item"
				:style="{zIndex:100-index,transform: `translateY(${item.y1}px)`,transition:item.transition?'transform 0.3s ease':'none'}"
				v-for="(item,index) in lists" :key="index">
				<image :src="item.userImg" mode="aspectFill"></image>
				<view class="box-item-info">
					<!-- 姓名/认证/地址 -->
					<view class="box-item-info-name flex align-center justify-between">
						<view class="box-item-info-name-l flex align-center">
							{{item.realName}}
							<image src="../../static/images/my/rzicon.png"
								style="width: 40rpx;height: 40rpx;margin-left: 10rpx;"></image>
						</view>
						<view class="box-item-info-name-r">
							<text v-if="item.locationCity!='市辖区'"> {{item.locationCity}}</text>
							<text v-else> {{item.locationProvince}}</text>
							{{item.locationCounty}}
						</view>
					</view>
					<view class="box-item-info-label flex align-center flex-wrap">
						<view class="box-item-info-label-item" style="background-color: #38CAFF;color: #ffffff;">
							<u-icon :name="item.sex==1?'man':'woman'" color="#FFFFFF"
								style="margin-right: 6rpx;"></u-icon>
							{{item.age}}岁
						</view>
						<view class="box-item-info-label-item">
							{{item.education}}
						</view>
						<view class="box-item-info-label-item">
							{{item.marriageStatus==2?'离异':'未婚'}}
						</view>
						<view class="box-item-info-label-item">
							{{item.userHeight}}CM
						</view>
					</view>
					<!-- 简介 -->
					<view class="box-item-info-jianjie flex align-center">
						<u-icon name="/static/images/index/xinxin.png" color="#FF749F" size="35"></u-icon>
						<view class="box-item-info-jianjies">
							{{item.idealAspect?item.idealAspect:'暂无'}}
						</view>
					</view>
					<!-- 按钮 -->
					<view class="box-item-info-btn flex align-center justify-center">
						<view @click.stop="closeCard(item.userId)"
							class="box-item-info-btn-l flex align-center justify-center">
							<u-icon name="close" color="#FFFFFF" size="45"></u-icon>
						</view>
						<view class="box-item-info-btn-r flex align-center justify-center">
							<u-icon name="/static/images/index/xihuan.png" color="#FFFFFF" size="50"></u-icon>
						</view>
					</view>





				</view>

			</view>
			<view class="box-empty" v-if="lists.length == 0">
				<image src="../../static/images/empty.png" mode="widthFix"></image>
				<view class="box-empty-txt">
					暂无数据
				</view>
			</view>
		</view>

	</view>
</template>
<script>
	export default {
		name: "sliderSiper",
		props: {
			list: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
				lists: [],
				//初始位置
				y: 0,
				yNum: 200,
			};
		},
		watch: {
			list(newVal) {
				if (this.list.length > 0) {
					this.spliceList()
				} else {
					this.lists = []
				}
			}
		},
		methods: {
			//进入详情
			clickSlider(userId) {
				this.$emit('clickSlider', userId)
			},
			//把不喜欢的加入过滤名单
			saveUserFilter(userId) {
				let data = {
					filterUserId: userId
				}
				this.$Request.postT('/app/userFilter/saveUserFilter', data).then(res => {
					if (res.code == 0) {
						this.lists.shift()

						this.setListItem()
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
					}
				})
			},
			//移除当前卡片
			closeCard(userId) {
				if (uni.getStorageSync('token')) {
					// this.lists.shift()
					// this.setListItem()
					this.saveUserFilter(userId)
				} else {
					this.$emit('closeSlider', false)
				}

			},
			//开始移动
			moveStart(e, index) {
				// if (!uni.getStorageSync('token')) {
				// 	this.$emit('closeSlider', false)
				// 	return
				// }
				// 阻止页面滚动
				e.preventDefault();
				this.y = e.touches[0].clientY;
				this.lists[index].moving = true;

			},
			moverIng(e, index) {
				let moveY = e.touches[0].clientY
				// 如果有滑动距离，则将isTouch设为true
				if (moveY) {
					if (!uni.getStorageSync('token')) {
						this.$emit('closeSlider', false)
						return
					}
				}
				// if (!uni.getStorageSync('token')) {
				// 	this.$emit('closeSlider', false)
				// 	return
				// }
				// 阻止页面滚动
				e.preventDefault();
				if (!this.lists[index].moving) return;
				const currentY = e.touches[0].clientY;
				const startY = this.y;
				// 检查是否向上滑动  
				if (currentY < startY) {
					// 更新y1值，只允许向上滑动  
					this.lists[index].y1 = e.touches[0].clientY - this.y;
					this.$forceUpdate(); // 强制更新视图  
				}

			},
			//追加新元素
			setListItem() {
				if (!this.lists[this.lists.length - 1]) {
					return
				}
				//先拿到lists最后一条的userID
				let endUserId = this.lists[this.lists.length - 1].userId
				// 然后去list中查询位置
				let indexs = this.list.findIndex(item => item.userId == endUserId)
				// 找到了
				if (indexs != -1) {
					//首先判断是不是最后一条
					if (indexs != this.list.length - 1) {
						//不是最后一条给lists追加一条新数据
						this.lists.push(this.list[indexs + 1])
					}
				}
			},
			//移动结束
			moveEnd(e, index) {
				// 阻止页面滚动
				e.preventDefault();
				if (!this.lists[index].moving) return;
				this.lists[index].moving = false;
				// 向上滑动超过阈值  
				if (this.lists[index].y1 < -this.yNum) { //滑动超过阈值了
					this.lists[index].y1 = 0;
					this.lists[index].transition = true;
					//移除第一个元素
					this.lists.shift()
					//再追加一个新元素
					this.setListItem()

					setTimeout(() => {
						this.lists[index].transition = false;
					}, 300)
				} else { //没有超过阈值 直接回到原位
					this.y = 0
					this.lists[index].y1 = 0;
					this.$nextTick(() => {
						this.lists[index].transition = true;
						this.$forceUpdate()
						setTimeout(() => {
							this.lists[index].transition = false;
						}, 300)
					})

				}
			},
			//初始化数据
			spliceList() {
				//初始化滑动情况
				if (this.list.length > 0) {
					this.list.map(item => {
						item.y1 = 0
						item.moving = false
						item.transition = false
					})
					this.lists = this.list.slice(0, 3)
				} else {
					this.lists = []
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	$height: 70vh;

	.box-empty {
		width: 100%;
		height: $height;
		display: flex;
		align-content: center;
		justify-content: center;
		flex-wrap: wrap;

		image {
			width: 378rpx;
		}

		.box-empty-txt {
			width: 100%;
			color: #999999;
			text-align: center;
			margin-top: 20rpx;
		}
	}

	.box {
		width: 686rpx;
		height: $height;
		position: relative;

		.box-item {
			transition: width 0.3s ease;
			width: 686rpx;
			height: $height;
			position: absolute;
			border-radius: 20rpx;
			top: 0;
		}

		.box-item-info {
			position: absolute;
			bottom: 30rpx;
			left: 30rpx;
			width: 626rpx;
			height: auto;
		}

		.box-item-info-name {
			width: 100%;
		}

		.box-item-info-name-l {
			font-size: 42rpx;
			color: #ffffff;
			font-weight: bold;
		}

		.box-item-info-name-r {
			color: #FFFFFF;
			font-size: 26rpx;
		}

		.box-item-info-label {
			width: 100%;
			margin-top: 20rpx;
		}

		.box-item-info-label-item {
			background-color: #FFFFFD;
			color: #999999;
			font-size: 24rpx;
			padding: 10rpx 16rpx;
			border-radius: 10rpx;
			margin-right: 10rpx;
		}

		.box-item-info-jianjie {
			padding: 10rpx 20rpx;
			margin-top: 20rpx;
			background-color: #FFE7EF;
			width: 100%;
			border-radius: 30rpx;

			.box-item-info-jianjies {
				margin-left: 10rpx;
				color: #000000;
				font-size: 25rpx;
				width: calc(100% - 50rpx);
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
				-o-text-overflow: ellipsis;
			}
		}

		.box-item-info-btn {
			width: 100%;
			margin-top: 42rpx;
		}

		.box-item-info-btn-l {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			background-color: #cccccc;
		}

		.box-item-info-btn-r {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			background: linear-gradient(0deg, #ff6f9c, #ffa8c7);
			margin-left: 210rpx;
		}

	}

	.max-width {
		width: 686rpx;
		height: $height;
		left: 0;
		margin-top: 40rpx;

		.box-items {
			width: 686rpx;
			height: $height;
			position: relative;
		}

		image {
			width: 686rpx;
			height: $height;
			border-radius: 20rpx;
		}
	}

	.mod-width {
		width: 676rpx;
		height: $height;
		left: 6rpx;
		margin-top: 20rpx;

		.box-items {
			width: 686rpx;
			height: $height;
			position: relative;
		}

		image {
			width: 676rpx;
			height: $height;
			border-radius: 20rpx;
		}
	}

	.min-width {
		height: $height;
		width: 666rpx;
		left: 12rpx;

		.box-items {
			width: 686rpx;
			height: $height;
			position: relative;
		}

		image {
			width: 666rpx;
			height: $height;
			border-radius: 20rpx;
		}
	}
</style>
<template>
	<view class="declaration-container">
		<!-- 顶部导航栏 -->
		<u-navbar title="报单中心" :is-back="true" :background="background"></u-navbar>

		<!-- 表单内容 -->
		<view class="form-content">
			<!-- 订单类型 -->
			<view class="form-item" @click="showOrderTypeSelect">
				<view class="form-label"><i class="required">*</i>订单类型</view>
				<view class="form-value" :class="{ 'placeholder': !orderType }">
					{{ orderType || '未选择' }}
				</view>
				<view class="form-arrow">></view>
			</view>

			<!-- 报单类型 -->
			<view class="form-item" @click="showReportTypeSelect">
				<view class="form-label"><i class="required">*</i>报单类型</view>
				<view class="form-value" :class="{ 'placeholder': !reportType }">
					{{ reportType || '未选择' }}
				</view>
				<view class="form-arrow">></view>
			</view>

			<!-- 客户手机号 -->
			<view class="form-item">
				<view class="form-label"><i class="required">*</i>客户手机号</view>
				<input
					class="form-input"
					v-model="customerPhone"
					placeholder="请输入客户手机号"
					type="number"
					maxlength="11"
				/>
			</view>

			<!-- 订单费用 -->
			<view class="form-item">
				<view class="form-label"><i class="required">*</i>订单费用</view>
				<input
					class="form-input"
					v-model="orderAmount"
					placeholder="请输入订单费用"
					type="digit"
				/>
			</view>

			<!-- 服务顾问 -->
			<view class="form-item" @click="showAdvisorSelect">
				<view class="form-label"><i class="required">*</i>服务截图<span class="service-pic-title">（需显示完整缴费订单号）</span></view>
				
				<view class="form-arrow">></view>
			</view>

			<!-- 添加按钮 -->
			<view class="add-sh" @click="addItem">
				<text class="add-icon">+</text>
			</view>

			<!-- 备注 -->
			<view class="form-item remark-item">
				<view class="form-label"><i class="required">*</i>备注<span class="service-pic-title">（通过App缴费，请勿报单）</span></view>
			</view>

			<!-- 备注输入框 -->
			<view class="remark-input-area">
				<textarea
					class="remark-textarea"
					v-model="remark"
					placeholder="请输入备注内容"
					maxlength="200"
					:auto-height="true"
				></textarea>
			</view>
		</view>

		<!-- 底部确认按钮 -->
		<view class="bottom-button">
			<button class="confirm-btn" @click="submitForm">确定</button>
		</view>

		<!-- 订单类型选择器 -->
		<u-select
			v-model="orderTypeShow"
			:list="orderTypeList"
			mode="single-column"
			@confirm="confirmOrderType"
			title="选择订单类型"
		></u-select>

		<!-- 报单类型选择器 -->
		<u-select
			v-model="reportTypeShow"
			:list="reportTypeList"
			mode="single-column"
			@confirm="confirmReportType"
			title="选择报单类型"
		></u-select>

		<!-- 服务顾问选择器 -->
		<u-select
			v-model="advisorShow"
			:list="advisorList"
			mode="single-column"
			@confirm="confirmAdvisor"
			title="选择服务顾问"
		></u-select>
	</view>
</template>

<script>
export default {
	data() {
		return {
			background: {
				background: '#ffffff'
			},
			// 表单数据
			orderType: '',
			reportType: '',
			customerPhone: '',
			orderAmount: '',
			serviceAdvisor: '',
			remark: '',

			// 选择器显示状态
			orderTypeShow: false,
			reportTypeShow: false,
			advisorShow: false,

			// 选择器数据
			orderTypeList: [
				{ label: '普通订单', value: '1' },
				{ label: 'VIP订单', value: '2' },
				{ label: '高级订单', value: '3' }
			],
			reportTypeList: [
				{ label: '新客户报单', value: '1' },
				{ label: '老客户续费', value: '2' },
				{ label: '升级服务', value: '3' }
			],
			advisorList: [
				{ label: '张三', value: '1' },
				{ label: '李四', value: '2' },
				{ label: '王五', value: '3' },
				{ label: '赵六', value: '4' }
			]
		}
	},

	onLoad() {
		// 页面加载时的初始化
	},

	methods: {
		// 显示订单类型选择器
		showOrderTypeSelect() {
			this.orderTypeShow = true;
		},

		// 显示报单类型选择器
		showReportTypeSelect() {
			this.reportTypeShow = true;
		},

		// 显示服务顾问选择器
		showAdvisorSelect() {
			this.advisorShow = true;
		},

		// 确认订单类型
		confirmOrderType(e) {
			this.orderType = e[0].label;
			this.orderTypeShow = false;
		},

		// 确认报单类型
		confirmReportType(e) {
			this.reportType = e[0].label;
			this.reportTypeShow = false;
		},

		// 确认服务顾问
		confirmAdvisor(e) {
			this.serviceAdvisor = e[0].label;
			this.advisorShow = false;
		},

		// 添加项目
		addItem() {
			console.log('添加项目');
			uni.showToast({
				title: '添加功能待开发',
				icon: 'none'
			});
		},

		// 提交表单
		submitForm() {
			// 表单验证
			if (!this.orderType) {
				uni.showToast({
					title: '请选择订单类型',
					icon: 'none'
				});
				return;
			}

			if (!this.reportType) {
				uni.showToast({
					title: '请选择报单类型',
					icon: 'none'
				});
				return;
			}

			if (!this.customerPhone) {
				uni.showToast({
					title: '请输入客户手机号',
					icon: 'none'
				});
				return;
			}

			if (!/^1[3-9]\d{9}$/.test(this.customerPhone)) {
				uni.showToast({
					title: '请输入正确的手机号',
					icon: 'none'
				});
				return;
			}

			if (!this.orderAmount) {
				uni.showToast({
					title: '请输入订单费用',
					icon: 'none'
				});
				return;
			}

			if (!this.serviceAdvisor) {
				uni.showToast({
					title: '请选择服务顾问',
					icon: 'none'
				});
				return;
			}

			// 提交数据
			const formData = {
				orderType: this.orderType,
				reportType: this.reportType,
				customerPhone: this.customerPhone,
				orderAmount: this.orderAmount,
				serviceAdvisor: this.serviceAdvisor,
				remark: this.remark
			};

			console.log('提交表单数据:', formData);

			uni.showToast({
				title: '提交成功',
				icon: 'success'
			});

			// 这里可以调用API提交数据
			// this.submitToServer(formData);
		}
	}
}
</script>

<style lang="scss" scoped>
.declaration-container {
	height: 100vh;
	background-color: #ffffff;
	display: flex;
	flex-direction: column;
}

.form-content {
	flex: 1;
	padding: 0 32rpx;
	background-color: #ffffff;
}

.form-item {
	background: #ffffff;
	padding: 32rpx;
	margin-bottom: 2rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	min-height: 88rpx;
	box-sizing: border-box;
}
.required {
	color: #EB1B47;
	font-size: 32rpx;
}
.form-label {
	font-size: 32rpx;
	color: #333333;
	font-weight: 500;
	min-width: 160rpx;
}

.service-pic-title {
	font-weight: 400;
	font-size: 24rpx;
	color: #969799;
}

.form-value {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
	text-align: right;
	margin-right: 16rpx;
}

.form-value.placeholder {
	color: #EB1B47;
}

.form-arrow {
	font-size: 28rpx;
	color: #EB1B47;
	font-weight: bold;
}

.form-input {
	flex: 1;
	font-size: 32rpx;
	color: #333333;
	text-align: right;
	border: none;
	outline: none;
	background: transparent;
}

.form-input::placeholder {
	color: #cccccc;
}

.add-button {
	background: #ffffff;
	margin: 40rpx 0;
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 16rpx;
}

.add-icon {
	font-size: 60rpx;
	color: #cccccc;
	font-weight: 300;
}

.remark-item {
	align-items: flex-start;
	padding-top: 32rpx;
	padding-bottom: 16rpx;
}

.remark-content {
	flex: 1;
	text-align: right;
	margin-right: 16rpx;
}

.remark-placeholder {
	font-size: 32rpx;
	color: #cccccc;
}

.remark-input-area {
	background: #ffffff;
	padding: 0 32rpx 32rpx;
	margin-bottom: 40rpx;
}

.remark-textarea {
	width: 100%;
	min-height: 220rpx;
	font-size: 32rpx;
	color: #333333;
	border-radius: 24rpx;
	border: none;
	outline: none;
	resize: none;
	background: #F7F8FA;
	padding: 24rpx;
	font-size: 28rpx;
}

.remark-textarea::placeholder {
	color: #cccccc;
}

.bottom-button {
	padding: 32rpx;
	background: #ffffff;
	border-top: 2rpx solid #f0f0f0;
}

.confirm-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
	color: #ffffff;
	font-size: 32rpx;
	font-weight: 600;
	border-radius: 44rpx;
	border: none;
	outline: none;
	display: flex;
	align-items: center;
	justify-content: center;
}

.confirm-btn:active {
	opacity: 0.8;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
	.form-label {
		font-size: 30rpx;
		min-width: 140rpx;
	}

	.form-value {
		font-size: 30rpx;
	}

	.form-input {
		font-size: 30rpx;
	}

	.remark-textarea {
		font-size: 30rpx;
	}

	.confirm-btn {
		font-size: 30rpx;
	}
}
</style>
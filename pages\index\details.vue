<template>
	<view class="content">
		<!-- #ifndef H5 -->
		<!-- 导航栏 -->
		<u-navbar title="动态" height="44" :is-back="false" :background="background" title-color="#111224"
			:border-bottom="false"></u-navbar>
		<!-- #endif -->

		<view class="bgjb"></view>
		<view class="fw " v-if="swiperList.length!=0">
			<view class="fw-box">
				<swiper class="screen-swiper" style="height: 280rpx;" :circular="true" :autoplay="true" interval="2500"
					duration="800">
					<swiper-item v-for="(item,index) in swiperList" :key="index" @click="goNav(item.url)">
						<image :src="item.imageUrl" mode="aspectFill"
							style="width: 100%;;height: 280rpx;border-radius: 24rpx;"></image>
					</swiper-item>
				</swiper>
			</view>
		</view>
		<!-- #ifdef H5 -->
		<u-sticky :enable="enable" offset-top="0" h5-nav-height="0" @fixed="fixed" @unfixed="unfixed"
			bg-color="#F7F7F7">
		<!-- #endif -->
			<!-- #ifndef H5 -->
			<u-sticky :enable="enable" :offset-top="customNavHeight" @fixed="fixed" @unfixed="unfixed"
				bg-color="#F7F7F7">
			<!-- #endif -->
				<view class="tabs " :class="isTop?'tabbg':''">
					<view class="flex align-center">
						<view :class="tabIndex==index?'tabsSe':''" @tap="tabClick(index)"
							class="tabs-box-item flex justify-center align-center" v-for="(item,index) in tabBars"
							:key="index">
							{{item.name}}
						</view>
					</view>
					<!-- <view class="fdt" @click="bindfb">发动态</view> -->
					<view class="flex align-center">
						<view class="" v-if="userId" style="position: relative;margin-right: 20rpx;">
							<image @click="goMsg()" style="width: 84rpx;height: 70rpx;"
								src="../../static/images/my/daitaiMsg.png" mode=""></image>
							<u-badge type="error" :count="count" :offset="[0,0]"></u-badge>
						</view>
						<view class="fdt" @click="bindfb">发动态</view>
					</view>
				</view>
			</u-sticky>


			<view class="list flex justify-center">
				<view class="list-box">
					<view class="list-box-item">
						<view class="" v-if="tabIndex!=1">
							<Head v-if="list.length>0" :list="list" :tabIndex="tabIndex" :userId="userId"
								@click="clickItem"></Head>
						</view>
						<view v-else>
							<hn v-if="list.length>0" :list="list" :userId="userId" @click="clickItem"></hn>
						</view>

						<!-- 暂无数据 -->
						<block v-if="list.length == 0">
							<view class="flex justify-center" style="margin-top: 200rpx;">
								<image src="../../static/images/empty.png" style="width: 387rpx;height: 341rpx;"
									mode="">
								</image>
							</view>
							<view class="flex justify-center"
								style="font-size: 32rpx;color: #999999;margin-top: 20rpx;font-weight: 700;">
								暂无数据
							</view>
						</block>
					</view>
				</view>
			</view>


			<hninfo ref="hnPopup"></hninfo>




			<!-- <view class="tui-tabs">
			<scroll-view id="tab-bar" scroll-with-animation class="tui-scroll-h" :scroll-x="true"
				:show-scrollbar="false">
				<view style="display: flex;">
					<view :key='index' v-for="(tab, index) in tabBars" :id="tab.id" :data-current="index"
						@tap="tabClick(index)">
						<view class="tui-tab-item-title" style="margin-left: 170upx;"
							:class="{ 'tui-tab-item-title-active': tabIndex == index }">{{ tab.name }}</view>
					</view>
				</view>
			</scroll-view>
		</view>

		<view style="background: #FFFFFF;height: 340upx;" v-if="bannerList.length > 0">
			<swiper class="swiper-container" :autoplay="true" :interval="5000" :circular="true" :indicator-dots="false"
				style="height: 260rpx;">
				<swiper-item class="swiper-wrapper" style="" v-for="(item, index3) in bannerList" :key="index3"
					@tap='toNavList(item.url)'>
					<image lazy-load='true' fade-show='true' :src="item.imageUrl" v-if="item"
						style="width: 100%;height: 260rpx;"></image>
				</swiper-item>
			</swiper>
		</view>

		<view>
			
		</view> -->
			<!-- <view class="part3" v-if="shangxianSelect != '否'" @click="bindfb">
			<image src="../../static/images/my/dtfb.png"></image>
		</view> -->

			<!-- 悬浮上拉 -->
			<!-- <view class="scroll_top" @tap="topScrollTap" v-bind:class="[scrollTop ? 'active' : '', '']"
				style="bottom: 56px;"><text class="iconfont icon-shangla"></text></view> -->

			<!-- 加载更多提示 -->
			<view class="s-col is-col-24" v-if="list.length > 0">
				<load-more :status="loadingType" :contentText="contentText"></load-more>
			</view>
			<!-- modal弹窗 -->
			<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
				@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
				:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	import Head from '@/components/Head.vue';
	import empty from '@/components/empty.vue';
	import hn from '@/components/hongnian.vue';
	import hninfo from "@/components/hnInfo.vue";
	export default {
		components: {
			empty,
			Head,
			hn,
			hninfo
		},
		data() {
			return {
				background: {
					backgroundImage: 'linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%)'
				},
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				shangxianSelect: '否',
				renzheng: 0,
				tabIndex: 0,
				tabBars: [{
						name: '推荐',
						id: 'hot',
						state: '-1'
					}, {
						name: '红娘',
						id: 'hn',
						state: '1'
					}, {
						name: '关注',
						id: 'yule',
						state: '1'
					}

				],
				gambitId: 0,
				title: '',
				content: '',
				contentImg: '',
				bannerList: [],
				list: [],
				followUserId: 0,
				isEnable: '否',
				scrollTop: false,
				page: 1,
				size: 10,
				loadingType: 0,
				scrollTop: false,
				contentText: {
					contentdown: '上拉显示更多',
					contentrefresh: '正在加载...',
					contentnomore: '没有更多数据了'
				},
				totaSize: 0,
				userId: '',
				swiperList: [],
				enable: true,
				isTop: false,
				count: 0, //消息个数
				customNavHeight: 170,
			}
		},
		onLoad(d) {
			uni.showLoading({
				title: '加载中...'
			});
			// #ifndef H5
			//因为手机状态栏高度的偏差，需要对条件粘性布局进行调整
			//获取手机系统信息
			let systemInfo = uni.getSystemInfoSync();
			console.log(systemInfo, '宽')
			//获取手机系统状态栏高度
			let statusBarHeight = systemInfo.statusBarHeight;
			// 判断系统状态栏高度是否小于47
			if (statusBarHeight <= 47) {
				// 如果小于，则粘性布局距离顶部的距离等于导航栏的高度（88）-47跟系统导航栏的差  自定义导航栏height (44) 
				this.customNavHeight = 88 - (47 - statusBarHeight) + 88

			}
			console.log(this.customNavHeight, statusBarHeight, '宽')
			// #endif
			let a = this.$queue.getData("isEnable")
			if (a) {
				this.isEnable = a;
			}

			uni.$on('click', (val) => {
				console.log("val______:" + val)
			});
		},
		onShow() {
			this.count = uni.getStorageSync('msgNum')

			this.enable = true
			this.userId = this.$queue.getData('userId');
			// #ifdef MP-WEIXIN
			this.shangxianSelect = this.$queue.getData('shangxianSelect');
			// #endif
			// #ifndef MP-WEIXIN
			this.shangxianSelect = '是';
			// #endif
			this.page = 1;
			this.getList('');

			this.getSelectBanner();
			// this.getRenZheng();
		},
		onPageScroll: function(e) {
			this.scrollTop = e.scrollTop > 200;
		},
		onHide() {
			this.enable = false
		},
		methods: {
			//去消息列表
			goMsg() {
				uni.navigateTo({
					url: '/my/myMsg/myMsg'
				})
			},
			goNav(url) {
				if (url.indexOf('/pages/') !== -1 || url.indexOf('/my/') !== -1 || url.indexOf('/package/') !== -1) {
					uni.navigateTo({
						url
					});
				} else {
					//#ifndef H5
					uni.navigateTo({
						url: '/pages/index/webView?url=' + url
					});
					//#endif
					//#ifdef H5
					window.location.href = url;
					//#endif
				}
			},
			fixed() {
				this.isTop = true
			},
			unfixed() {
				this.isTop = false
			},
			getRenZheng() {
				this.$Request.get("/app/userCertification/queryInsert").then(res => {
					if (res.code == 0) {
						// 0审核中 1通过 2拒绝 
						if (res.data == null) {
							this.renzheng = 0 //未实名
						} else if (res.data.status == 0) {
							this.renzheng = 1 //审核中
						} else if (res.data.status == 1) {
							this.renzheng = 2 //已实名
						} else if (res.data.status == 2) {
							this.renzheng = 3 //已拒绝
						}
					}
				});
			},
			/**
			 * @param {Object} url
			 */
			toNavList: function(url) {
				if (!url) return;
				let token = this.$queue.getData('token');
				if (token) {
					if (url.indexOf('/pages/') !== -1) {
						uni.navigateTo({
							url
						});
					} else {
						//#ifndef H5
						uni.navigateTo({
							url: '/pages/index/webview?url=' + url
						});
						//#endif
						//#ifdef H5
						window.location.href = url;
						//#endif
					}
				} else {
					this.goLoginInfo();
				}
			},
			getSelectBanner() {
				this.$Request.getT('/app/banner/selectBannerList?classify=1&state=1').then(res => {
					if (res.code == 0) {
						this.swiperList = res.data;
					}
				});
			},
			topScrollTap: function() {
				uni.pageScrollTo({
					scrollTop: 0,
					duration: 300
				});
			},
			tabClick(e) {
				let token = this.$queue.getData('token');
				if (!token) {
					this.goLoginInfo();
					return;
				}
				this.tabIndex = e;
				this.page = 1;
				this.getList('');
			},
			clickItem: function(options) {
				let token = this.$queue.getData('token');
				if (!token) {
					this.goLoginInfo();
					return;
				}
				if (options.index == 0) {
					uni.navigateTo({
						url: '/package/pages/detail/listDetails?trendsId=' + this.list[options.id].trendsId
					});
				} else if (options.index == 1) {
					let token = this.$queue.getData('token');
					if (token) {
						uni.navigateTo({
							url: '/package/pages/game/dongtai?byUserId=' + this.list[options.id].userId
						})
					} else {
						this.goLoginInfo();
					}
				} else if (options.index == 2) {
					// this.getMessageList2();
				} else if (options.index == 3) {
					this.saveLove(this.list[options.id].trendsId);
				} else if (options.index == 9) { //点击关注
					this.guanzhu1(this.list[options.id].userId);
				} else if (options.index == 10) {
					console.log(options);
					let data = options.id
					this.$refs.hnPopup.open(data)
				} else if (options.index == 99) {
					console.log(options);
					let data = options.id
					uni.navigateTo({
						url: '/package/pages/game/detail?byUserId=' + options.id.userId
					})
				}
			},
			guanzhu1(userId) {
				this.$Request.postT('/app/scFollow/saveScFollow?byUserId=' + userId + '&type=1').then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						this.page = 1;
						this.getList('');
					}
				});
			},
			//统一登录跳转
			goLoginInfo() {
				uni.navigateTo({
					url: '/pages/public/login'
				});
			},
			//确认
			meHandleBtn() {
				let that = this
				if (this.meIndex == 'm1') {
					uni.navigateTo({
						url: "/my/renzheng/index"
					});
				}
			},
			//取消
			meHandleClose() {
				let that = this
				if (this.meIndex == 'm1') {

				}
			},
			saveLove(trendsId) {
				// if (this.renzheng != 2) {
				// 	this.meShowModel = true
				// 	this.meTitle = '温馨提示'
				// 	this.meContent = '您还未认证陪玩官,请先认证'
				// 	this.meIndex = 'm1'
				// 	return;
				// }
				// let userId = this.$queue.getData('userId');
				let data = {
					trendsId: trendsId,
					// userId: userId
				}
				this.$Request.postT('/app/trendsLike/saveTrendsLike', data).then(res => {
					if (res.code == 0) {
						this.page = 1;
						this.getList('');
					} else {
						this.$queue.showToast(res.msg);
					}
				});
			},
			getList(type) {
				this.loadingType = 1;

				let userId = this.$queue.getData('userId') ? this.$queue.getData('userId') : '';
				let followUserId = 0;
				if (this.tabIndex != 0) {
					followUserId = userId;
				}
				let data = {}
				if (this.tabIndex == 0) {
					data = {
						page: this.page,
						limit: this.size,
						// userId: userId,
						releaseType: 1, // 发布类型 1用户发布 2红娘发布
						byUserId: userId,
					}

				} else if (this.tabIndex == 1) {
					data = {
						page: this.page,
						limit: this.size,
						// userId: userId,
						releaseType: 2, // 发布类型 1用户发布 2红娘发布
						byUserId: userId,
					}

				} else {
					data = {
						page: this.page,
						limit: this.size,
						userId: userId,
						releaseType: 1 // 发布类型 1用户发布 2红娘发布

					}
				}
				if (this.tabIndex == 2) {
					this.$Request.getT('/app/trends/getCareTrendsList', data).then(res => {
						if (res.code == 0) {
							this.totaSize = res.data.pages
							if (this.page == 1 || res.data) {
								this.list = [];
							}
							res.data.records.forEach(d => {
								if (d.trendsImage) {
									d.trendsImage = d.trendsImage.split(',');
								}
								this.list.push(d);
							})
							if (this.page < this.totaSize) { //还有下一页
								this.loadingType = 0;
							} else {
								this.loadingType = 2;
							}

						} else {
							this.loadingType = 2;
						}
						uni.hideLoading();
						if (type === 'refresh') {
							uni.stopPullDownRefresh();
						}
					});
				} else if (this.tabIndex == 0) {
					this.$Request.getT('/app/trends/getTrendsList', data).then(res => {
						if (res.code == 0) {
							this.totaSize = res.data.pages
							if (this.page == 1 || res.data) {
								this.list = [];
							}
							res.data.records.forEach(d => {
								if (d.trendsImage) {
									d.trendsImage = d.trendsImage.split(',');
								}
								this.list.push(d);
							})
							if (this.page < this.totaSize) { //还有下一页
								this.loadingType = 0;
							} else {
								this.loadingType = 2;
							}

						} else {
							this.loadingType = 2;
						}
						uni.hideLoading();
						if (type === 'refresh') {
							uni.stopPullDownRefresh();
						}
					});
				} else {
					// 红娘发布的用户

					this.$Request.getT('/app/trends/getTrendsList', {
						...data,
						releaseType: 2 // 发布类型 1用户发布 2红娘发布

					}).then(res => {
						if (res.code == 0) {
							this.totaSize = res.data.pages
							if (this.page == 1 || res.data) {
								this.list = [];
							}
							res.data.records.forEach(d => {
								if (d.trendsImage) {
									d.trendsImage = d.trendsImage.split(',');
								}
								this.list.push(d);
							})
							if (this.page < this.totaSize) { //还有下一页
								this.loadingType = 0;
							} else {
								this.loadingType = 2;
							}

						} else {
							this.loadingType = 2;
						}
						uni.hideLoading();
						if (type === 'refresh') {
							uni.stopPullDownRefresh();
						}
					});
					this.list = []
				}

			},
			//统一登录跳转
			goLoginInfo() {
				uni.navigateTo({
					url: '/pages/public/login'
				});
			},
			bindfb() {
				let token = this.$queue.getData('token');
				if (token) {
					// if (this.renzheng != 2) {
					// 	this.meShowModel = true
					// 	this.meTitle = '温馨提示'
					// 	this.meContent = '您还未认证陪玩官,请先认证'
					// 	this.meIndex = 'm1'
					// 	return;
					// }
					uni.navigateTo({
						url: '/package/pages/releaseone/releaseone'
					})
				} else {
					this.goLoginInfo();
				}
			}
		},
		onReachBottom: function() {
			if (this.page < this.totaSize) {
				this.page = this.page + 1;
				this.getList('');
			}

		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getList('refresh');
			this.getSelectBanner();
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F7F7F7;
	}

	.bgjb {
		width: 100%;
		height: 346rpx;
		position: fixed;
		/* #ifdef H5 */
		top: 0rpx;
		/* #endif */
		/* #ifndef H5 */
		top: 0;
		/* #endif */
		z-index: 1;
		background-image: url('../../static/images/bgImg.png');
		background-size: 100% 100%;
		// background: linear-gradient(to bottom, #e2d6feff, #F7F7F7);
		// background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		// background:repeating-linear-gradient(red , yellow 0%, green 100%);
		padding-top: 40rpx;
		padding-bottom: 30rpx;
	}

	.fw {
		width: 100%;
		position: relative;
		z-index: 99;
		/* #ifdef H5 */
		margin-top: 0rpx;
		/* #endif */
		/* #ifndef H5 */
		margin-top: 150rpx;
		margin-top: 0rpx;

		/* #endif */
		.fw-box {
			padding: 30rpx;
			// background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		}
	}

	.fdt {
		font-size: 30rpx;
		font-family: PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		background: linear-gradient(0deg, #FF6F9C 0%, #FFA8C7 100%);
		width: 170rpx;
		height: 70rpx;
		border-radius: 55rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.tabbg {
		background: linear-gradient(90deg, #E4E7F8 0%, #F1E3F4 46%, #FDE1EF 100%);
		/* #ifdef H5 */
		padding: 30rpx 30rpx 20rpx !important;
		/* #endif */
		/* #ifndef H5 */
		padding: 15rpx 30rpx 20rpx !important;
		/* #endif */
	}

	.tabs {
		width: 100%;
		// background: #F5F5F5;
		position: relative;
		z-index: 99;
		padding: 0rpx 30rpx 20rpx;
		display: flex;
		// align-items: center;
		justify-content: space-between;

		.tabs-box-item {
			// width: 120rpx;
			height: 100%;
			// border-radius: 50rpx;
			// background: #FFFFFF;
			margin-right: 60rpx;
			font-size: 34rpx;
			font-family: PingFang SC;
			font-weight: 500;
			color: #999999;
		}

		.tabsSe {
			font-size: 40rpx;
			font-family: PingFang SC;
			font-weight: 800;
			color: #333333;
		}
	}

	.list {
		width: 100%;
		height: auto;
		// margin-top: 120rpx;
		position: relative;
		z-index: 9;

		.list-box {
			width: 100%;
			height: 100%;

			.list-box-item {
				width: 100%;
				height: auto;
				// background-color: #FFFFFF;
				border-radius: 20rpx;
			}
		}
	}
</style>
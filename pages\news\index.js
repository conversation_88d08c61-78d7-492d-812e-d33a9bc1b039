import {
  getMessageList,
  sendTextMsg,
  detailInfoMemberID,
  canSendMsg,
  assistGenerate
} from '../../../api/message'
import { clone } from '../../../lib/mp-store/index'
import { timeTommChat, getTimeInfoMessageChat, debounce } from '../../../utils/util'
import { reportKibana } from '../../../utils/log'
import { storage } from '../../../utils/storage/index'
import { chatUserCheck } from '../../../api/chat.api'
import socket from '../../../utils/socket'
import create from '../../../utils/omix/create'

let app = getApp()
const isVipGroup = ({ groupType = '', subGroupType = '', } = {}) =>
  groupType && subGroupType && (groupType === 3 || (groupType === 5 && subGroupType === 2))

Page({
  storeConfig: {
    useState: store => ({
      userGroup: state => state.userGroup,
    }),
  },

  /**
   * 页面的初始数据
   */
  data: {
    scrollTop: null,
    maxHeight: 0,
    KeyboardHeight: 0,
    inputText: '',
    focusInput: false,
    pushPage: false,
    objectId: null,
    messageList: [],
    platform: wx.getSystemInfoSync().platform,
    objectDetail: {
      hasChatCard: false,
    },
    isShowTip: true,
    timer: null,
    topId: null,
    isChatVip: storage.get('isChatVip'), // 是否是春节畅聊卡会员
    sendMsgStatus: {
      canSend: false,
      placeHolder: '', // 文本输入框占位
      toastMsg: '', // 点击按钮toast
      checkRefresh: true, // 是否每次刷新请求更新
    },
    shouldRefreshMessageList: false,
    // AI聊天助手相关数据
    aiSwitchOn: true,
    aiMessages: [],
    isGeneratingMessage: false,
    aiFirstMsgStatus: true,
    aiBeansStatus: false,
    beansDeductType: true, // 豆子动画class
    beansAnimationClass: '', // 豆子动画class
    showBeansPopup: false, // 控制爱豆不足弹窗显示
    coinUsed: 1, // 爱豆消耗
    aiClickLoading: false, // 爱豆点击loading
    isNeverChat: false,
    prologueShow: false, // 开场白是否显示
    prologueList: [], // 开场白列表
    callMessageApiCount: 0, // 调用消息接口次数
    looked: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log(options)
    if (!options.objectId) {
      return
    }
    this.setAiStatus(options.objectId)
    let system = wx.getSystemInfoSync()

    // statusBarHeight 状态栏高度 titleBarHeight 文字导航栏高度 94 输入框高度
    this.setData({
      maxHeight: system.windowHeight - system.statusBarHeight - app.globalData.titleBarHeight - 94,
      objectId: options.objectId,
      messageId: 'pages/message/message-detail/index' + options.objectId
    })
    this.upTime()
    this.objectIdDetail()
    // 获取聊天list 并开启定时器
    this.getRecentTime()


  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
      // const query = wx.createSelectorQuery()
      // const maxHeight = this.data.maxHeight
      // query.select('#aiChatAssistant').boundingClientRect(rect => {
      //   console.log('aiChatAssistantDom', rect)
      //   this.setData({
      //     maxHeight: maxHeight - rect.height,
      //   })
      // })

      // query.exec()

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.getRecentTime()
    socket.offNews(this.data.messageId)
    socket.onNews(this.getRecentTime, this.data.messageId)
    wx.onKeyboardHeightChange(res => {
      this.setData({
        KeyboardHeight: res.height,
      })


    })
    if (Object.keys(this.data.objectDetail).length > 0) {
      this.reportChat(5, '对话页曝光')
    }
    // 隐藏实名认证
    this.selectComponent('#authentChat')?.closeCurrentPage()
    create.emitter.on('receive_new_chat_card', this.canSendMsgDetail2.bind(this))
    this.setData({
      shouldRefreshMessageList: false,
    })

  },
  reportChat(accessPoint, accessPointDesc, ext1) {
    const memberId = storage.get('memberId')
    const userGroup = this.data.objectDetail.userGroupDTO || {}
    const isVip = this.data.objectDetail.hasChatCard ? isVipGroup(userGroup) ? 'VIP' : '普通' : ''
    const isChatVip = this.data.objectDetail.hasChatCard ? '已开通' : '未开通'

    const type = `${isChatVip}畅聊卡的${isVip}用户`

    reportKibana({
      resourceKey: '消息tab',
      accessPoint,
      accessPointDesc,
      ext1: ext1,
      ext2: type,
      ext7: this.data.objectId,
      mid: memberId,
    })
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    create.emitter.off('receive_new_chat_card')
  },
  refreshMessageList() {
    if (this.data.shouldRefreshMessageList) {
      create.emitter.emit('refresh_messageList')
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    wx.offKeyboardHeightChange() // 取消监听键盘高度变化事件
    this.data.timer && clearInterval(this.data.timer)
    socket.offNews(this.data.messageId)
    create.emitter.off('receive_new_chat_card')
    this.refreshMessageList()
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },
  /**
   * 设置ai助手开关状态
   * @param {*} objectId 对象id
   */
  setAiStatus(objectId) {
    const aiStatusManage = wx.getStorageSync('aiStatusManage')
    if(aiStatusManage !== undefined && typeof aiStatusManage === 'object'){
      if(aiStatusManage.hasOwnProperty(objectId)){
        const {status,looked = false} = aiStatusManage[objectId]
        this.setData({
          aiSwitchOn: status,
          looked,
        })

      }else{
        aiStatusManage[objectId] = {
          status: true,
          looked: true,
        }
        console.log('set',1)
        wx.setStorageSync('aiStatusManage',aiStatusManage)
      }
    }else{
      wx.setStorageSync('aiStatusManage',{
        [objectId]:{
          status: true,
          looked: true,
        }
      })
    }
  },
  /**
   * 更新ai助手开关状态
   * @param {*} isOn 是否开启
   */
   updateAiStatus(isOn) {
    const aiStatusManage = wx.getStorageSync('aiStatusManage')
      aiStatusManage[this.data.objectId] = {
        status: isOn,
        looked: true,
      }
      wx.setStorageSync('aiStatusManage',aiStatusManage)
  },
  upTime() {
    setTimeout(() => {
      this.setData({
        isShowTip: false,
      })
    }, 6000)
  },
  onInput(e) {
    this.setData({
      inputText: e.detail.value,
    })
  },
  // &source=Comment&fromPage=4
  objectIdClick() {
    wx.navigateTo({ url: `/pages/main/message/detail-page/index?memberId=${this.data.objectId}&showBtn=${false}&source=receiveFlowerPage`, })
  },
  // 查看并编辑自己的资料页
  memeberIdClick() {
    wx.navigateTo({ url: `/pages/personalcenter/my-info-preview/index`, })
  },
  scrollTopUpper() {
    console.log('触底加载')
    this.getRecentTime()

  },
  // 滚动监听 是否在底部
  onScroll(e) {
    let { scrollHeight, scrollTop, } = e.detail

    if (scrollTop > 50) {
      // 不在底部 这个50是最小阀值
      // console.log('不在底部')
      // 关闭timer 轮询
      this.data.timer && clearInterval(this.data.timer)
      return
    }
  },
  lowerBottom() {
    console.log('上拉加载')
    if (this.data.messageList.length === 0) {
      return
    }
    // 防抖
    let index = this.data.messageList.length - 1

    if (this.data.topId === this.data.messageList[index].sid) {
      return
    }
    // 下拉触发
    this.data.topId = this.data.messageList[index].sid // 记录当前第一个ID
    this.getRelaMessage()

  },
  getRelaMessage() {
    getMessageList({
      sid: this.data.messageList[this.data.messageList.length - 1].sid,
      objectId: this.data.objectId,
    }).then((res) => {
      if(res.data.code !== 0) {
        wx.showToast({
          icon: 'none',
          title: res.data.msg,
        })
        return
      }

      res.data.data = this.handleList(res.data.data || [])
      // 填充数据
      if (res.data.data.length > 0) {
        let last = [this.data.messageList.length - 1]

        this.data.messageList[last].isChatTen = timeTommChat(this.data.messageList[last].defaultSendTime, res.data.data[0].defaultSendTime)// 是否相差10分钟
      }
      this.setData({
        messageList: this.data.messageList.concat(res.data.data),
      })
      this.data.topId = null // 防止重复请求

    })

  },
  async checkBothVip(objectId) {
    const { data: vipRes } = await chatUserCheck({
      objectId,
    })
    if (vipRes.code === 0) {

      if (!['Comment'].includes(this.data.source)) {
        // 只在聊天用户渠道校验，优恋圈不校验
        if (vipRes.data.memberIsChat && !vipRes.data.objectIsChat) {
          wx.showToast({
            title: '对方账号异常',
            icon: 'none',
          })
          throw '对方账号异常'
        }
      }

    }
  },
  confirm: debounce(async function() {
    console.log('发送')
    this.reportChat(7, '对话页点击-发送')
    await this.checkBothVip(this.data.objectId)

    if (!this.data.inputText.trim()) { return }
    sendTextMsg(
      {
        objectId: this.data.objectId,
        messageType: 1,
        content: this.data.inputText,
        source: 1,
        platform: this.data.platform === 'ios' ? 0 : 1,
      }
    ).then((res) => {
      if (res.data.code === 0) {
        // 清空输入内容
        this.setData({
          inputText: '',
          shouldRefreshMessageList: true,

        })
        this.getRecentTime()
      } else {
        // 用户未实名 进入我的认证
        if (res.data.code === 107) {
          this.blur()
          this.archiveJump()
          return
        }

        // 发送失败
        wx.showToast({
          icon: 'none',
          title: res.data.msg,
        })
      }
    })
  }, 1000, true),

  archiveJump() {
    const authentChatComponent = this.selectComponent('#authentChat')

    authentChatComponent.openInfo()
  },
  // 输入框聚焦 再次滚动到底部
  focus() {
    this.setData({
      focusInput: true,
    })
    // 键盘高度 推起
    this.getTT()

    this.reportChat(6, '对话页点击-输入框（仅未唤起键盘时）')
    const {isNeverChat,prologueShow,aiSwitchOn} = this.data

    if (prologueShow) {
       this.setData({
        prologueShow: false,
        aiFirstMsgStatus: false
      })
      if(isNeverChat && aiSwitchOn) {
        this.generateAiMessages()
      }
    }

  },
  blur() {
    this.setData({
      KeyboardHeight: 0,
      pushPage: false,
      scrollTop: null,
      focusInput: false,
    })

    // 关闭键盘
    wx.hideKeyboard()


  },

  getTT() {
    // 获取scroll的高度
    let obj = wx.createSelectorQuery().select('.scrollHeight')

    obj.boundingClientRect((data) => {
      this.data.clientHeight = data.height
      // 滑动到页面最底部
      this.setData({
        scrollTop: 0,
      })

      if (this.clientHeight + this.KeyboardHeight < this.data.maxHeight) {
        this.setData({
          pushPage: false,
        })

      } else {
        this.setData({
          pushPage: true,
        })
      }
    }).exec()
  },
  getRecentTime() {
    this.getData()
  },
  // 触发更新数据的接口
  getData() {
    Promise.all([
      this.getRecentMessage(),
      this.canSendMsgDetail()
    ]).then(async () => {
      const {messageList,sendMsgStatus:{canSend},looked} = this.data
      const isNeverChat = messageList.length === 0
      if(isNeverChat && canSend && !looked) {
        await this.generateAiPrologue()
        this.setData({
          prologueShow: true,
        })
      }else{
        this.setData({
          prologueShow: false,
        })
      }
      this.setData({
        isNeverChat,
      })
    })
  },
  // 获取最近的聊天消息
  getRecentMessage() {
    let callMessageApiCount = this.data.callMessageApiCount // 防止重复调用AI生成
    console.log(callMessageApiCount)
    return new Promise((resolve, reject) => {
      getMessageList({
        objectId: this.data.objectId,
      }).then((res) => {
        if(res.data.code !== 0) {
          wx.showToast({
            icon: 'none',
            title: res.data.msg,
          })
          return
        }
        const messageList = this.handleList(res.data.data || [])
        const lastMessageIsObject = messageList && (messageList[0] || {}).folder === 0
        const {aiSwitchOn,looked} = this.data
        if(lastMessageIsObject && aiSwitchOn && callMessageApiCount < 1 && !looked) {
          this.createAiMessage()
           callMessageApiCount++
        }
        this.setData({
          messageList: messageList,
          callMessageApiCount: callMessageApiCount,
        })
        resolve()
      })
    })
  },

  // 处理数据
  handleList(list) {
    let copyList = clone(list)

    let result = []

    for (let i = list.length - 1;i >= 0;i--) {
      let item = list[i]

      item.isChatTen = this.timeChat(item, i, list)// 是否相差10分钟
      item.defaultSendTime = new Date(copyList[i].sendTime).getTime() // 记录sendtime 计算时间差
      item.sendTime = getTimeInfoMessageChat(item.defaultSendTime)
      if (item.messageType === 5) {
        item.contentObj = JSON.parse(item.content) || {}
      }

      result.unshift(item)
    }
    return result
  },
  // 计算于上一条是否相差十分钟 ， 是 不展示时间
  timeChat(data, index, list) {
    let time2 = list[index + 1]

    // 最后一条数据
    if (!list[index + 1]) {
      return false
    }

    let isChat = timeTommChat(data.sendTime, time2.defaultSendTime)

    if (isChat) {
      return true
    } else {
      return false
    }

  },
  objectIdDetail() {
    detailInfoMemberID({
      memberId: this.data.objectId,
    }).then((res) => {
      this.reportChat(5, '对话页曝光')
      if(res.data.code !== 0) {
        wx.showToast({
          icon: 'none',
          title: res.data.msg,
        })
        return
      }
      this.setData({
        objectDetail: res.data.data,
      })

    })

  },
  // 检查 是否可以发送消息
  canSendMsgDetail() {
    return new Promise((resolve, reject) => {
      if (this.data.sendMsgStatus?.checkRefresh) {
        canSendMsg({
          objectId: this.data.objectId,
        }).then((res) => {
          if(res.data.code === 0){
            this.setData({
              sendMsgStatus: res.data.data,
            })
            resolve()
          } else {
            wx.showToast({
              icon: 'none',
              title: res.data.msg,
            })
            reject()
          }

        })
      }
   })
  },
  // 检查 是否可以发送消息
  canSendMsgDetail2() {
    canSendMsg({
      objectId: this.data.objectId,
    }).then((res) => {
      if(res.data.code === 0){
        this.setData({
          sendMsgStatus: res.data.data,
        })
      } else {
        wx.showToast({
          icon: 'none',
          title: res.data.msg,
        })
      }

    })

  },
  // 无法发送 点击输入框toast提示
  noSendToast() {
    if (this.data.sendMsgStatus?.canPopup) {
      this.openSevenChatPopup()

    } else if (this.data.sendMsgStatus?.toastMsg) {
      wx.showToast({
        icon: 'none',
        title: this.data.sendMsgStatus.toastMsg,
      })
    } else {
      // 跳转开通畅聊卡
      wx.navigateTo({
        url: '/pages/mine/privilege/chat/index',
      })
    }

  },
  openChat: debounce(async function() {
    //  跳转开通畅聊卡
    wx.navigateTo({
      url: `/pages/mine/privilege/chat/index?source=109`,
    })
  }, 1000, true),
  openSevenChatPopup: debounce(async function() {
    this.selectComponent('#notifyPopup').getPopups(8)

  }, 1000, true),
  sendText2Input(text) {
    this.setData({
      inputText: text.detail,
    })
  },
  sendPrologue(text) {
    this.setData({
      prologueShow: false,
      inputText: text.detail,
    })
    this.confirm()
    this.reportChat(14, 'AI生成内容-点击后填充输入框', text.detail)
  },
  regeneratePrologue() {
    this.generateAiPrologue()
    this.setData({
      prologueShow: true,
    })
  },
  async generateAiPrologue(tryCount = 0) {
    if(tryCount === 0){
      wx.showLoading({
        title: '生成中...',
      })
    }
    if(tryCount > 4) {
      wx.showToast({
        icon: 'none',
        title: '网络繁忙，请稍后再试',
      })
      wx.hideLoading()
      return
    }

    try {
      const res = await assistGenerate({
        objectId: this.data.objectId,
        userId: storage.get('memberId'),
        generateType: 2
      })
      if (res.data.code === 0) {
        const {replies = []} = res.data.data
        this.setData({
          prologueList: replies,
        })
        if (res.data.data.coinUsed > 0) {
          this.setData({
            aiBeansStatus: true,
            beansAnimationClass: ''
          });
          wx.nextTick(() => {
            this.showBeansAnimation()
          });
        }
      }else if(res.data.code === 140){
        this.generateAiPrologue(tryCount+1)
        return
      }
      wx.hideLoading()

    } catch (error) {
      wx.showToast({
        icon: 'none',
        title: '生成失败',
      })
       wx.hideLoading()
    }
  },
  // AI聊天助手相关方法
  toggleAiHelper(e) {
    const isOn = e.detail.value;
    this.setData({
      aiSwitchOn: isOn,
      isGeneratingMessage: isOn && this.data.aiMessages.length === 0
    });

    if (isOn && this.data.aiMessages.length === 0) {
      this.setData({
        aiFirstMsgStatus: true,
      })
      // this.generateAiMessages();
      this.reportChat(11, '对话页点击-Al聊天助手开关', 1)
    } else {
      this.setData({
        aiFirstMsgStatus: false,
        aiMessages: [],
      })
      this.reportChat(11, '对话页点击-Al聊天助手开关', 2)
    }
    this.updateAiStatus(isOn)
  },

  createAiMessage() {
    this.setData({
      aiFirstMsgStatus: false,
      prologueShow: false,
    });
    this.generateAiMessages();
    this.reportChat(12, '对话页点击-生成/重新生成(ai内容)', 1)
  },

  regenerateAiMessage() {
    this.generateAiMessages()
    this.reportChat(8, '对话页点击-生成/重新生成(ai内容)', 2)
  },

  // 爱豆消耗动画方法
  showBeansAnimation() {
    // 重置状态
    this.setData({
      aiBeansStatus: true,
      beansAnimationClass: ''
    });

    // 使用nextTick确保下一帧再添加动画类
    wx.nextTick(() => {
      // 设置动画类
      this.setData({
        beansAnimationClass: 'beans-animation'
      });

      // 动画结束后隐藏爱豆消耗提示
      setTimeout(() => {
        this.setData({
          aiBeansStatus: false,
          beansAnimationClass: ''
        });
      }, 1000); // 与动画时长一致
    });
  },

  async generateAiMessages(tryCount = 0) {
    if(tryCount > 4) {
      wx.showToast({
        icon: 'none',
        title: '网络繁忙，请稍后再试',
      })
      wx.hideLoading()
      return
    }
    if (this.data.aiClickLoading) {
      return
    }
    this.setData({
      aiClickLoading: true,
    })
    wx.showLoading({
      title: '生成中...',
      mask: false
    })
    try {
      const res = await assistGenerate({
        objectId: this.data.objectId,
        userId: storage.get('memberId'),
        generateType: 1
      })
    if (res.data.code === 0) {
      this.setData({
        isGeneratingMessage: true,
        // aiBeansStatus: true,
        // beansAnimationClass: ''
      });
      // 显示爱豆消耗动画
      if (res.data.data.coinUsed > 0) {
        this.setData({
          aiBeansStatus: true,
          beansAnimationClass: ''
        });
        wx.nextTick(() => {
          this.showBeansAnimation()
        });
      }
      // debugger
      this.setData({
        aiMessages: res.data.data.replies,
        isGeneratingMessage: false,
        coinUsed: res.data.data.coinUsed
      })
      wx.hideLoading()
    } else if (res.data.code === 139) {
      this.showBeansPopup()
      this.reportChat(13, '对话页爱豆不足弹窗', 0)
      wx.hideLoading()
    } else if (res.data.code === 140) {
      this.setData({
        aiClickLoading: false,
      })
      this.generateAiMessages(tryCount+1)
      return
    } else {
        // wx.hideLoading()
        wx.showToast({
          icon: 'none',
          title: res.data.msg,
        })
      }
      wx.hideLoading()
    } catch (error) {
      wx.hideLoading()
      wx.showToast({
        icon: 'none',
        title: '生成失败',
      })
    }
    this.setData({
      aiClickLoading: false,
    })
  },

  useAiMessage(e) {
    const message = e.currentTarget.dataset.message;
    this.setData({
      inputText: message,
      // aiSwitchOn: false
    });
    this.reportChat(14, 'AI生成内容-点击后填充输入框', message)
    // this.confirm();
  },

  // 检查爱豆是否足够
  checkBeans() {
    // 此处应该调用接口检查爱豆余额
    // 模拟随机返回结果，实际项目中应替换为真实接口调用
    return Math.random() > 0.5;
  },

  // 显示爱豆不足弹窗
  showBeansPopup() {
    this.setData({
      showBeansPopup: true
    });
  },

  // 关闭爱豆不足弹窗
  closeBeansPopup() {
    this.setData({
      showBeansPopup: false
    });
    this.reportChat(9, '对话页爱豆不足弹窗', 1)
  },

  // 阻止事件冒泡
  preventBubble() {
    // 空函数，用于阻止事件冒泡
  },

  // 前往购买爱豆
  goBuyBeans() {
    // 关闭弹窗
    this.closeBeansPopup();

    // 跳转到购买爱豆页面
    // wx.navigateTo({
    //   url: '/pages/mine/beans/index',
    // });
    this.selectComponent('#aidouRechatgeDialog').onShowComponent(2)
    this.reportChat(9, '对话页爱豆不足弹窗', 2)
  },

})

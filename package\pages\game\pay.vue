<template>
	<view>
		<view class="box" v-for="(item,index) in orderlist" :key="index">
			<view class="flex" @click="goDetail(item)">
				<image :src="item.avatar?item.avatar:'../../../static/logo.png'"
					style="width: 130rpx;height: 130rpx;border-radius: 24rpx;">
				</image>
				<view class="margin-left-sm">
					<view class="flex">
						<view class="tirle">{{item.userName}}</view>
						<image src="../../../static/images/my/renz.png" style="width: 38rpx;height:38rpx;"></image>
					</view>
					<view class="margin-top-sm">联系方式：{{maskPhoneNumber(item.userPhone)}}</view>
				</view>
			</view>
			<view class="margin-tb-sm" style="width: 100%;height: 1rpx;background: #E5E5E5;"></view>
			<view class="flex align-end justify-end">
				<view class="btn" @click="callPhone(item.userPhone)">联系TA</view>
			</view>
		</view>
		<empty v-if="orderlist.length==0"></empty>
	</view>
</template>

<script>
	import empty from '@/components/empty.vue'
	export default {
		components: {
			empty
		},
		data() {
			return {
				page: 1,
				limit: 10,
				count: 0,
				orderlist: []
			}
		},
		onLoad() {
			this.getlist()
		},
		onShow() {

		},
		methods: {
			goDetail(item) {
				uni.navigateTo({
					url: '/package/pages/game/detail?byUserId=' + item.buyUserId
				})
			},
			callPhone(phone) {
				uni.makePhoneCall({
					phoneNumber: phone
				})
			},
			//加密中间四位
			maskPhoneNumber(phoneNumber) {
				// 将手机号码转换为字符串类型，并根据其长度确定要替换的位数
				if (phoneNumber) {
					phoneNumber = phoneNumber.toString() //先强制转换成字符串类型
					return phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')
				}

			},
			getlist() {
				this.$Request.getT("/app/userGetPhoneRecord/getByPhoneList?page=" + this.page + '&limit=' + this.limit)
					.then(res => {
						if (res.code == 0) {
							if (this.page == 1) {
								this.orderlist = res.data.records
							} else {
								res.data.records.forEach(d => {
									this.orderlist.push(d);
								});
							}
							this.count = res.data.total;
						}
						uni.hideLoading();
						uni.stopPullDownRefresh()
					});
			},
		},
		onReachBottom: function() {
			if (this.orderlist.length == this.count) {
				uni.showToast({
					title: '已经到底了',
					icon: 'none'
				})
			} else {
				this.page = this.page + 1;
				this.getlist();
			}
		},
		onPullDownRefresh: function() {
			this.page = 1;
			this.getlist();
		}
	}
</script>

<style lang="less">
	page {
		background: #F7F7F7;
	}

	.box {
		background: #FFFFFF;
		border-radius: 25rpx;
		padding: 30rpx;
		margin: 30rpx;

		.tirle {
			font-size: 31rpx;
			font-family: PingFang SC;
			font-weight: 800;
			color: #333333;
			margin-right: 15rpx;
		}
	}

	.btn {
		background: linear-gradient(114deg, #FF6F9C 0%, #FF98BD 100%);;
		color: #FFFFFF;
		border-radius: 55rpx;
		padding: 10rpx 35rpx;
		font-size: 26rpx;
	}
</style>
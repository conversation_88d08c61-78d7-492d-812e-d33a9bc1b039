<template>
	<view class="padding">
		<view class="bg padding " style="border-radius: 24rpx;">
			<view class=" u-flex " @click="goNav('')">
				<view class="u-m-r-10">
					<u-avatar :src="order.user?order.user.avatar:'../../static/logo.png'" size="68"></u-avatar>
				</view>
				<view class="u-flex-1 text-white">
					<view class="u-font-14  text-bold">{{order.user?order.user.userName:''}}</view>
				</view>
				<view>
					<!-- <image src="../static/right.png" style="width: 16rpx;height: 28rpx;" ></image> -->
				</view>
			</view>
			<view class="flex justify-between margin-top-lg">
				<view style="color: #A1A2B3;width: 160upx;">服务技能</view>
				<view class="text-white">{{order.game.gameName}}</view>
			</view>
			<view class="flex justify-between margin-top-lg">
				<view style="color: #A1A2B3;width: 160upx;">购买数量</view>
				<view class="text-white">×{{order.orderNumber}}</view>
			</view>
			<view class="flex justify-between margin-top-lg">
				<view style="color: #A1A2B3;width: 160upx;">单价</view>
				<view class="text-white">
				{{order.orderTaking?order.orderTaking.money:''}}币/{{order.orderTaking?order.orderTaking.unit:''}}
				</view>
			</view>
			<view class="flex justify-between margin-top-lg" v-if="order.couponMoney">
				<view style="color: #A1A2B3;width: 160upx;">优惠券</view>
				<view class="text-white">-￥{{order.couponMoney}}</view>
			</view>
			<view class="flex justify-between margin-top-lg">
				<view style="color: #A1A2B3;width: 160upx;">会员优惠</view>
				<view class="text-white">{{youhui}}</view>
			</view>
			<view class="flex justify-between margin-top-lg" v-if="order.remarks">
				<view style="color: #A1A2B3;margin-right: 20upx;">备注说明</view>
				<textarea class="text-white flex-sub" style="height: 100upx;" v-model="order.remarks"></textarea>
			</view>
			<view class="flex justify-between margin-top-lg">
				<view style="color: #A1A2B3;" @click="goMsg">
					<u-icon name="chat" size="30" color="#1789FD" class="margin-right-sm"></u-icon>联系TA
				</view>
				<view class="text-white">实付：<text class="text-lg text-bold" style="color: #FF6F1B;">{{order.payMoney}}币</text></view>
			</view>
		</view>
		<view class="bg padding  margin-top-sm" style="border-radius: 24rpx;">
			<view class="text-lg text-white">订单信息</view>
			<view class="flex justify-between margin-top">
				<view style="color: #A1A2B3;width: 160upx;">订单编号</view>
				<view class="text-white flex align-center">{{order.ordersNo}}
					<image src="../static/copy.png" style="width: 45rpx;height: 45rpx;margin-left: 5upx;"
						@click.stop="copyClick(order.ordersNo)"></image>
				</view>
			</view>
			<view class="flex justify-between margin-top">
				<view style="color: #A1A2B3;width: 160upx;">下单时间</view>
				<view class="text-white">{{order.createTime}}</view>
			</view>
		</view>
		<view class="flex justify-between margin-top-xl" v-if="isTrue == 0">
			<u-button @click="cancelOrder(order)" class="margin-top" :custom-style="customStyle" :hair-line="false">取消订单
			</u-button>
			<u-button @click="pay" class="margin-top" :custom-style="customStyle2" :hair-line="false">立即支付</u-button>
		</view>
		<!-- modal弹窗 -->
		<u-modal v-model="meShowModel" :content="meContent" :title="meTitle" :show-cancel-button='meShowCancel'
			@cancel="meHandleClose" @confirm="meHandleBtn" :confirm-text='meConfirmText'
			:cancel-text='meCancelText'></u-modal>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//弹窗
				meShowModel: false, //是否显示弹框
				meShowCancel: true, //是否显示取消按钮
				meTitle: '提示', //弹框标题
				meContent: '', //弹框内容
				meConfirmText: '确认', //确认按钮的文字
				meCancelText: '取消', //关闭按钮的文字
				meIndex: '', //弹窗的key
				customStyle: {
					backgroundColor: '#ffffff',
					color: '#ac75fe',
					border: 1,
					borderColor:'#ac75fe',
					width: "330upx",
					margin: 0
				},
				customStyle2: {
					backgroundColor: '#ac75fe',
					color: '#FFFFFF',
					border: 0,
					width: "330upx",
					margin: 0
				},
				id: '',
				order: {
					user: {},
					game: {}
				},
				isTrue: 0,
				youhui: 0
			}
		},
		onLoad(e) {
			this.isTrue = e.isTrue
			if (this.isTrue) {
				uni.setNavigationBarTitle({
					title: '订单详情'
				})
			}
			this.id = e.id
			this.getOrder()
		},
		methods: {
			copyClick(copy) {
				uni.setClipboardData({
					data: copy,
					success: function(res) {
						uni.getClipboardData({
							success: function(res) {
								uni.showToast({
									title: "复制成功",
									icon: 'none',
								});
							},
						});
					},
				});
			},
			goNav(e) {
				uni.navigateTo({
					url: e
				})
			},
			getOrder() {
				let data = {
					id: this.id
				}
				this.$Request.get('/app/orders/queryOrders', data).then(res => {
					if (res.code == 0) {
						this.order = res.data
						this.youhui = (this.order.orderTaking.money * this.order.orderNumber - this.order
							.payMoney * 1).toFixed(2)
					}
				})
			},
			//确认
			meHandleBtn() {
				let that = this
				if (this.meIndex == 'm1') {
					that.$Request.post("/app/orders/payMoney", {
						ordersId: that.order.ordersId,
					}).then(res => {
						if (res.code == 0) {
							uni.showToast({
								title: '支付成功'
							})
							setTimeout(function() {
								uni.navigateBack()
							}, 1000)
						} else {
							uni.showToast({
								title: res.msg,
								icon: 'none'
							})
						}
					});
				}
			},
			//取消
			meHandleClose() {
				if (this.meIndex == 'm1') {

				}
			},
			// 支付订单
			pay() {
				let that = this
				this.meShowModel = true
				this.meTitle = '付款提示'
				this.meContent = '确认支付' + that.order.payMoney + '金币吗?'
				this.meIndex = 'm1'
			},
			// 取消订单
			cancelOrder(e) {
				let data = {
					id: e.ordersId,
					status: '3'
				}
				this.$Request.get('/app/orders/cancelOrder', data).then(res => {
					if (res.code == 0) {
						uni.showToast({
							title: '取消成功',
							icon: 'none'
						})
						setTimeout(function() {
							uni.navigateBack()
						}, 1000)
					} else {
						uni.navigateBack()
					}
				})
			},
			goMsg() {
				let data = {
					userId: uni.getStorageSync('userId'),
					focusedUserId: this.order.user.userId
				}
				this.$Request.postJson('/app/chat/insertChatConversation', data).then(res => {
					if (res.code == 0) {
						let id = this.order.user.userId == res.data.userId ? res.data.focusedUserId : this.order
							.user.userId
						uni.navigateTo({
							// url: '/pages/msg/im?chatConversationId=' + res.data.chatConversationId +
							// 	'&byUserId=' + id
							url: '/pages/msg/chat?chatConversationId=' + res.data.chatConversationId +
								'&byUserId=' + id + '&byNickName=' + this.order.user.userName
						})
					} else {
						this.$queue.showToast(res.msg);
					}
				})
			},
		}
	}
</script>

<style>
	.bg {
		background-color: #FFFFFF;
	}
</style>
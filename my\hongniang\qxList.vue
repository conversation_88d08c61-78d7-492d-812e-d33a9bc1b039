<!--
 * @name: 
 * @Author: 刘大可
 * @Date: 2024-03-01 16:17:29
 * @LastEditors: 刘大可
 * @LastEditTime: 2024-03-06 21:00:15
-->
<template>
    <view class="qxList-page">
        <view class="header_nav row">
            <view class="item" :class="flg==0?'cur':''">全部</view>
            <view class="item" :class="flg == 1 ? 'cur' : ''">牵线中</view>
            <view class="item" :class="flg ==2 ? 'cur' : ''">牵线成功</view>
            <view class="item" :class="flg ==3 ? 'cur' : ''">牵线失败</view>
        </view>

        <list :list="list" @click="clickItem"></list>

    </view>
</template>

<script>
import Vue from 'vue';
// import list from "@/components/viplist.vue";
// import list from "@/components/Head.vue";
import list from "@/components/recordList.vue";

export default Vue.extend({
    components: {
        list
    },
    data() {
        return {
            flg:0,
            	list: [{
                id: 1,
                sex: 1,
                age: 23,
                status:2,
                auditContent:"测测"
            }, {
                id: 1,
                sex: 2,
                age: 23,
                status: 1,

            }, {
                id: 1,
                status:0,

            },
            {
                id: 1
            },
            {
                id: 1
            },
            ]
        }
    },
    computed: {},
    methods: {},
    watch: {},

    // 页面周期函数--监听页面加载
    onLoad(e) {
        this.flg=e.type
     },
    // 页面周期函数--监听页面初次渲染完成
    onReady() { },
    // 页面周期函数--监听页面显示(not-nvue)
    onShow() { },
    // 页面周期函数--监听页面隐藏
    onHide() { },
    // 页面周期函数--监听页面卸载
    onUnload() { },
    // 页面处理函数--监听用户下拉动作
    // onPullDownRefresh() { uni.stopPullDownRefresh(); },
    // 页面处理函数--监听用户上拉触底
    // onReachBottom() {},
    // 页面处理函数--监听页面滚动(not-nvue)
    // onPageScroll(event) {},
    // 页面处理函数--用户点击右上角分享
    // onShareAppMessage(options) {},
}) 
</script>

<style scoped lang="scss">
    @import "./main.scss";
</style>